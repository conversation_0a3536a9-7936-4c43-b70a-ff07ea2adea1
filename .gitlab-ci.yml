image: harbor.yy.com/front_end/fec
stages:
  - build

variables:
  OLD_PWD: $CI_PROJECT_DIR

build:
  stage: build
  tags:
    - yyci_office
  cache:
    paths:
      - node_modules/
      - gitlog
  artifacts:
    expire_in: 1 week
    paths:
      - output/
      - gitlog
  script:
    - SASS_BINARY_SITE=https://npmmirror.com/mirrors/node-sass/ yarn; fec o;
    - echo $PWD
  after_script:
    - git log -1 --pretty=medium >> gitlog
  only:
    - /^(master|commit|pre|test|fecdemo|newFromRuoyiPlus)$/
