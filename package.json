{"name": "hdzt-manager", "version": "1.0.0", "description": "活动中台管理后台", "license": "MIT", "scripts": {"dev": "fec d", "build": "fec o"}, "dependencies": {"@riophae/vue-treeselect": "0.4.0", "axios": "0.24.0", "clipboard": "2.0.6", "diff-match-patch": "^1.0.5", "echarts": "4.9.0", "element-ui": "2.15.6", "file-saver": "2.0.5", "fuse.js": "6.4.3", "highlight.js": "9.18.5", "js-beautify": "1.13.0", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "node-sass": "4.14.1", "nprogress": "0.2.0", "quill": "1.3.7", "sass-loader": "7.3.1", "screenfull": "5.0.2", "sortablejs": "1.10.2", "view-design": "^4.4.0", "vue": "2.6.12", "vue-codemirror": "4.0.6", "vue-count-to": "1.0.13", "vue-cropper": "0.5.5", "vue-json-pretty": "^1.9.5", "vue-loader": "^15", "vue-meta": "2.4.0", "vue-router": "3.4.9", "vue-svga": "^1.0.0", "vuedraggable": "2.24.3", "vuex": "3.6.0", "webpack": "4.46.0"}, "devDependencies": {"@babel/core": "^7.15.0", "@babel/preset-env": "^7.15.0", "babel-loader": "^8.2.2", "babel-preset-env": "^1.7.0", "chalk": "4.1.0", "connect": "3.6.6", "css-loader": "^3.6.0", "lint-staged": "10.5.3", "runjs": "4.4.2", "sass-resources-loader": "^2.2.4", "script-ext-html-webpack-plugin": "2.1.5", "svg-sprite-loader": "5.1.1", "vue-template-compiler": "2.6.12"}, "resolutions": {"@babel/plugin-proposal-private-property-in-object": "7.21.11"}}