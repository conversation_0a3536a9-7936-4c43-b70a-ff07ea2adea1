<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <title>{{title}}</title>
  {{!--<link rel="icon" href="./assets/logo/{{icon}}">   --}}
    <meta name="keywords" content="{{keywords}}">
    <meta name="description" content="{{description}}">
    <meta name="format-detection" content="telephone=no, email=no">
    <meta name="applicable-device" content="mobile">
    {{#if __PROD}}
      <script src="https://unpkg.yy.com/@empjs/cdn-react@0.18.0/dist/reactRouter.production.umd.js"></script>
    {{else}}
      <script src="https://unpkg.yy.com/@empjs/cdn-react@0.18.0/dist/reactRouter.development.umd.js"></script>
    {{/if}}
   {{!-- <link rel="stylesheet" href="/assets/data/iview/styles/iview.css">  --}}
    {{#if __PROD__}}
        {{!-- <link rel="stylesheet" href="./assets/css/{{stylesheet}}.css"> --}}
    {{/if}}
    {{#if __DEV__}}
        <script src="//res.yy.com/libs/vconsole/vconsole.min.js"></script>
    {{/if}}
</head>
<body>
{% body %}
<script src="//web1.yystatic.com/public/libs/jquery/3.1.0/jquery.min.js"></script>
<script src="//udbres.yy.com/lgn/js/oauth/udbsdk/pcweb/udb.sdk.pcweb.embed.min.js"></script>
  <script src="https://unpkg.com/@empjs/share@3.6.0/output/sdk.js"></script>
  <script src="https://unpkg.yy.com/@hd/yo-admin-adapter@0.1.0/dist/vue.umd.js"></script>
</body>
</html>
