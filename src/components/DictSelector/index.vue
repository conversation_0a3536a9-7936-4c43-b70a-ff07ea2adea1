<template>
  <div class="dict-selector">
    <el-select v-model="selected" placeholder="点击选择" @change="$emit('change', $event)" :filterable="filterable">
      <el-option v-for="item in dictData" :value="item.dictValue" :label="item.dictLabel+'('+item.dictValue+')'" :key="item.dictValue"  />
    </el-select>
  </div>
</template>

<script>
import { getDicts } from "@/api/system/dict/data";
export default {
  name: "DictSelector",
  data() {
    return {
      dictData: [],
      selected: this.value
    }
  },
  props: {
    value: String,
    dictType: String,
    filterable: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  beforeMount() {
    this.loadDictData()
  },
  methods:{
    loadDictData() {
      const self = this
      getDicts(this.dictType).then(res => {
        if (res && res.code === 200) {
          self.dictData = res.data
        }
      })
    }
  },
  watch: {
    selected: function (val) {
      this.$emit('input', val)
      this.$emit('update:value', val)
    },
    value: function (val) {
      this.selected = val
    }
  }
}
</script>

<style scoped lang="scss">

</style>
