<template>
  <el-dialog :visible.sync="dialogVisible" :fullscreen="dialogFull" width="60%" :before-close="cancel">
    <template slot="title">
      <el-row type="flex">
        <el-col :span="12">
          <div>{{ title }}</div>
        </el-col>
        <el-col :span="1" :offset="11">
          <el-button type="text" size="mini" :icon="dialogFull ? 'el-icon-crop' : 'el-icon-full-screen'"
                     @click="dialogFull = !dialogFull"></el-button>
        </el-col>
      </el-row>
    </template>
    <codemirror ref="myCm" v-model="jsonValue" :options="cmOption" />
    <span slot="footer" class="dialog-footer">
            <el-button @click="cancel">取 消</el-button>
            <el-button type="primary" @click="submitJson">确 定</el-button>
        </span>
  </el-dialog>
</template>

<script>

// require component
import { codemirror } from 'vue-codemirror'
import 'codemirror/mode/xml/xml'
import 'codemirror/theme/monokai.css'

// require styles
import 'codemirror/lib/codemirror.css'

export default {
  name: "JsonModal",
  data: () => ({
    dialogVisible: false,
    dialogFull: false,
    title: 'JSON视图',
    jsonValue: '',
    context: {},
    cmOption: {
      lineNumbers: true, // 显示行号
      styleActiveLine: true,
      theme: 'monokai',
      mode: 'xml'
    }
  }),
  components: {
    codemirror
  },
  props: {
    contentType: {
      type: String,
      required: false,
      default: 'json'
    }
  },
  methods: {
    showModal(_json, _title, _context) {
      let jsonStr = '{}'
      if (typeof _json === 'string') {
        jsonStr = _json
        if (this.contentType === 'json') {
          try {
            const json = JSON.parse(_json || '{}')
            jsonStr = JSON.stringify(json, null, 4)
          } catch (e) {
            console.log('parse json fail', jsonStr)
            try {
              const json = eval('(' + _json + ')')
              jsonStr = JSON.stringify(json, null, 4)
            } catch (ee) {
              console.log('parse json with eval fail')
            }
          }
        }
      } else if (typeof _json === 'object') {
        jsonStr = JSON.stringify(_json, null, 4)
      }
      this.jsonValue = jsonStr
      this.title = _title || '数据查看'
      this.context = _context || {}
      this.dialogVisible = true
    },
    cancel() {
      this.jsonValue = ''
      this.title = 'JSON视图'
      this.dialogVisible = false
    },
    submitJson() {
      this.$emit('submit', {jsonValue: this.jsonValue, context: this.context})
      this.cancel()
    }
  }
}
</script>

<style>
.CodeMirror {
  border: 1px solid #eee;
  height: 600px;
}
</style>
