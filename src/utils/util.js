/*eslint-disable*/
const getCookie = name => {
  let arr
  const RE = new RegExp(`(^| )${name}=([^;]*)(;|$)`)
  if ((arr = document.cookie.match(RE))) {
    return unescape(arr[2])
  } else {
    return ''
  }
}
const setCookie = function (key, value, options) {
  if (arguments.length > 1 && !$.isFunction(value)) {
    options = $.extend({}, options)

    if (typeof options.expires === 'number') {
      const days = options.expires
      const t = (options.expires = new Date())
      t.setMilliseconds(t.getMilliseconds() + days * 864e5)
    }

    return document.cookie = [
      encodeURIComponent(key),
      '=',
      encodeURIComponent(value),
      options.expires ? `; expires=${options.expires.toUTCString()}` : '', // use expires attribute, max-age is not supported by IE
      options.path ? `; path=${options.path}` : '',
      options.domain ? `; domain=${options.domain}` : '',
      options.secure ? '; secure' : ''
    ].join('')
  }
}

const formatDate = date => {
  const d = new Date(date)
  const year = d.getFullYear()
  let month = d.getMonth() + 1
  const day = d.getDate() < 10 ? `0${d.getDate()}` : `${d.getDate()}`
  let hour = d.getHours()
  let minutes = d.getMinutes()
  let seconds = d.getSeconds()
  month = month < 10 ? `0${month}` : month
  hour = hour < 10 ? `0${hour}` : hour
  minutes = minutes < 10 ? `0${minutes}` : minutes
  seconds = seconds < 10 ? `0${seconds}` : seconds
  return `${year}-${month}-${day} ${hour}:${minutes}:${seconds}`
}

// 重写toFixed方法
const toFixedRE = (number, precision)=> {
  let str = number + ''
  const len = str.length
  let last = str.substr(len-1, len)
  if (last == '5') {
    last = '6'
    str = str.substr(0, len-1) + last
    return (str - 0).toFixed(precision)
  } else {
    return number.toFixed(precision)
  }
}
// 正整数和0
const TestNum = s => {
  const re = /^(0|[1-9][0-9]*)$/
  return re.test(s)
}
const httpFilter = url => {
  if (typeof url !== 'string') {
    url = ''
  }
  let newUrl = url
  if (newUrl) {
    newUrl = newUrl.replace(/^http[s]?:\/\//, '//')
  }
  if (/.+\.(jpg|gif|png|jpeg)/.test(newUrl)) {
    newUrl = newUrl.replace('s5.yy.com', 's3.yy.com')
  }
  return newUrl
}

const getUrlAllParams = function getUrlAllParams () {
  // 解决乱码问题
  var url = decodeURI(window.location.href)
  var res = {}
  var url_data = _.split(url, '?').length > 1 ? _.split(url, '?')[1] : null
  if (!url_data) return null
  var params_arr = _.split(url_data, '&')
  _.forEach(params_arr, function (item) {
    var key = _.split(item, '=')[0]
    var value = _.split(item, '=')[1]
    res[key] = value
  })
  return res
}

const getParam = (key, url = window.location.href) => {
  const r = new RegExp('(\\?|#|&)' + key + '=([^&#]*)(&|#|$)')
  const m = url.match(r)
  return decodeURIComponent(!m ? '' : m[2])
}

export {
  getCookie,
  setCookie,
  formatDate,
  toFixedRE,
  TestNum,
  httpFilter,
  getUrlAllParams,
  getParam
}
