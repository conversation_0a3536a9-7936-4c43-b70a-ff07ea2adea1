import $ from 'jquery'

export function logOut() {
  const uid = getYyUid()
  if (!(/^\d+$/.test(uid)) || uid <= 0) {
    return
  }
  const deleteAuthUrl = location.protocol + '//www.yy.com/login/logout'
  const pcWeb = window.UDB.sdk.PCWeb
  let executed = false
  $.ajax({
    url: deleteAuthUrl + '?uid=' + uid,
    type: 'get',
    dataType: 'jsonp',
    async: false,
    success: function (res) {
      alert('退出返回')
      if (res.hasOwnProperty('deleteCookieURL') && res.deleteCookieURL !== '') {
        pcWeb.initialize()
        pcWeb.deleteCrossmainCookieWithCallBack(res.deleteCookieURL)
      } else {
        alert('退出失败')
      }
      executed = true
    },
    error: function () {
      alert('退出异常')
      executed = true
    }
  })

  const interval = setInterval(function () {
    // eslint-disable-next-line no-undef
    if (executed) {
      clearInterval(interval)
    }
  }, 10)
}
export function getYyUid() {
  return getCookie('yyuid')
}

export function getYyUserName() {
  return getCookie('username')
}

/**
 *   <div id="udbsdk_login_content" class="emstyle" style="_overflow: hidden; _width: 313px;" />
 * @param callbackUrl
 */
export function openDiv(callbackUrl) {
  const oauthUrl = location.protocol + '//lgn.yy.com/lgn/oauth/p/noticeaccess.do'
  const pcWeb = window.UDB.sdk.PCWeb

  $.ajax({
    url: oauthUrl + '?jsoncallback=?',
    data: {
      callbackURL: callbackUrl
    },
    type: 'get',
    dataType: 'jsonp',
    async: false,
    success: function (res) {
      if (res.success === '1') {
        // res.url += '&cssid=5719_1'
        pcWeb.initialize()
        pcWeb.embedOpenLgn(res, callbackUrl)
      } else {
        alert(res.errMsg)
      }
    },
    error: function () {
    }
  })
}

export function decorateURL(url) {
  if (url == null || url.length === 0) {
    url = window.location.href
  }
  const b = url.indexOf('#')
  if (b > -1) {
    url = url.substring(0, b)
  }
  if (url.startsWith('http')) return url
  const scheme = window.location.href.startsWith('https') ? 'https://' : 'http://'
  return scheme + window.location.host + url
}
function init() {
  const interval = setInterval(function () {
    // eslint-disable-next-line no-undef
    if (window.hasOwnProperty('UDB')) {
      clearInterval(interval)
    }
  }, 10)
}
function getCookie(name) {
  const reg = new RegExp('(^| )' + name + '=([^;]*)(;|$)')
  const arr = document.cookie.match(reg)
  if (arr) {
    return unescape(arr[2])
  } else {
    return null
  }
}

function addScript(src) {
  const s = document.createElement('script')
  s.id = 'yyUdb'
  s.type = 'text/javascript'
  s.src = src
  document.body.appendChild(s)
}
