import request from "@/utils/request"

export const queryGameList = (actId, uid, yyno, gameId, gameState, startTime, endTime) => {
  return request({
    url: 'wzry/gameList',
    params: { actId, uid, yyno, gameId, gameState, startTime, endTime },
    method: 'get'
  })
}

export const reCloseGame = (actId, gameId) => {
  const host = process.env.ENV === 'production' ? 'hdzk.yy.com' : 'test-hdzk.yy.com'
  return new Promise((resolve, reject) => {
    $.ajax({
      url: `${location.protocol}//${host}/${actId}/cmpt/wzryGame/reCloseGameAward?gameId=${gameId}`,
      type: 'get',
      dataType: 'json',
      xhrFields: {withCredentials: true},
      success: function (data) {
        resolve(data)
      },
      error: function (e) {
        reject(e)
      }
    })
  })
}

export const refundUser = (actId, gameId, refundUid, quickRace, price) => {
  const host = process.env.ENV === 'production' ? 'hdzk.yy.com' : 'test-hdzk.yy.com'
  return new Promise((resolve, reject) => {
    $.ajax({
      url: `${location.protocol}//${host}/${actId}/cmpt/wzryGame/refundMepiao9527`,
      type: 'get',
      data: {gameId, refundUid, quickRace, price},
      dataType: 'json',
      xhrFields: {withCredentials: true},
      success: function (data) {
        resolve(data)
      },
      error: function (e) {
        reject(e)
      }
    })
  })
}
