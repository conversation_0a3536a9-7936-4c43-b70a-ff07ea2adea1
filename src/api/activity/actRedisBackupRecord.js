import request from "@/utils/request"

export default {
  queryActBackupRecords(formData) {
    return request({
      url: 'backup/record',
      params: formData,
      method: 'get'
    })
  },

  batchDelBackupRecord(ids) {
    console.log(ids)
    return request({
      url: 'backup/record/batch',
      data: JSON.stringify(ids),
      headers: {'Content-Type': 'application/json'},
      method: 'delete'
    })
  },

  sinkBackupRecord(recordId, sourceUri, clearPatterns) {
    return request({
      url: 'backup/sink/' + recordId,
      method: 'POST',
      data: {sourceUri, remark: '', clearPatterns},
      headers: {'Content-Type': 'application/json'}
    })
  },

  querySinkResult(sinkRecordId) {
    return request({
      url: 'backup/sink/state',
      params: {sinkRecordId: sinkRecordId},
      method: 'get'
    })
  },

  queryActSinkRecords(recordId, pageNo, pageSize) {
    return request({
      url: 'backup/sink/record',
      params: {recordId: recordId, pageNo: pageNo, pageSize: pageSize},
      method: 'get'
    })
  },

  async queryDefaultSinkSource() {
    return new Promise((resolve, reject) => {
      request({
        url: 'backup/sink/sources',
        method: 'get'
      }).then(res => {
        if (res.result == 200) {
          resolve(res.data)
        } else {
          reject('res code not 200')
        }
      }).catch(error => {
        console.log(error)
        reject('request fail')
      })
    })
  }
}
