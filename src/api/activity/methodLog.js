import request from '@/utils/request'

export default {
  queryMethodLabel(groupCode) {
    return request({
      url: 'ecology/geMethodLog/queryMethodLabel?groupCode=' + groupCode,
      method: 'get',
    })
  },
  queryGeMethodLogs(param) {
    return request({
      url: 'ecology/geMethodLog/queryGeMethodLogs?'+param ,
      method: 'get',
    })
  },
  retryMethod(geMethodLog) {
    return request({
      url: 'ecology/geMethodLog/retryMethod',
      method: 'post',
      data: geMethodLog,
    })
  }
}
