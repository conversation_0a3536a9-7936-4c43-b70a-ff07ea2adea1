import request from '@/utils/request'

export default {
  listActAttr(actId,dataBase) {
    return request({
      url: 'activityAttr/listAttrList?actId=' + actId+ '&dataBase=' + dataBase,
      method: 'get',
    })
  },
  saveActAttr(param) {
    return request({
      url: 'activityAttr/saveActAttr',
      method: 'post',
      data: param
    })
  },
  deleteActAttr(actId,dataBase,attrname) {
    return request({
      url: 'activityAttr/deleteActAttr',
      method: 'delete',
      params: { actId,dataBase,attrname }
    })
  }

}
