import request from '@/utils/request'
export default {
// 获取所有bannerKey
  bannerkey () {
    return request({
      url: 'banner/internal/bannerkey'
    })
  },
  // 添加banner
  addbanner (data) {
    return request({
      url: 'banner/internal/banner',
      method: 'post',
      data: data
    })
  },
  // 更新banner
  updatebanner (id, data) {
    return request({
      url: `banner/internal/banner/${id}`,
      method: 'put',
      params: data
    })
  },
  // 停用banner
  offbanner (id) {
    return request({
      url: `banner/internal/banner/${id}/disable`,
      method: 'put'
    })
  },
  // 启用banner
  enablebanner (id) {
    return request({
      url: `banner/internal/banner/${id}/enable`,
      method: 'put'
    })
  },
  // 获取banner列表
  listbanner (data) {
    return request({
      url: 'banner/internal/banner/page',
      params: data
    })
  }

}
