import request from '@/utils/request'

export default {
  listComponentAttrDefine(actId, componentId, useIndex) {
    return request({
      url: 'cmptConfig/listComponentAttrDefine?componentId=' + componentId + "&actId=" + actId + "&useIndex=" + useIndex,
      method: 'post',
    })
  },
  saveComponentAttrValue(param) {
    return request({
      url: 'cmptConfig/saveComponentAttrValue',
      method: 'post',
      data: param,
    })
  }
}
