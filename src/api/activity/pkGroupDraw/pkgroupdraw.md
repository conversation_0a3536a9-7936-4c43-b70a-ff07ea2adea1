后面没有了
#################################################
# Author: guoliping
# Date: 2022-10-24
# 注：127.0.0.1:8080 要换成对应环境的域名
#################################################

【前提】
	POST 请求参数body 是json内容，用java描述的结构如下
	
		public class PkGroupDrawRequest {
			// 抽签配置ID
			private long configId;

			// 活动ID
			private long actId;

			// 榜单ID
			private long rankingId;

			// 阶段ID
			private long phaseId;

			// 抽下一个
			private long next;

			// 位置成员列表， 某个位置上没有的，要用 null 占位，比如：["1", "2", null, null, "5", null]
			private List&lt;String&gt; positions;
		}
		
	具体请求并不需要里面的所有字段，按需提供即可


1.列出所有需要抽签的项目

	http://127.0.0.1:8080/hdzt/pkGroupDraw/list
	GET

	{
		"result": 200,						// 200表示成功，其他只表示失败
		"reason": "OK",
		"data": {
			"2022103001": [{
				"actId": 2022103001,
				"actName": "2022宝贝年度公会正赛",
				"configId": 1,  	·		// 抽签ID
				"configName": "公会", 		//抽签名称
				"rankingId": 1, 			//抽签榜单ID
				"rankingName": "公会赛制",
				"phaseId": 2, 				// 抽签阶段ID
				"phaseName": "PK赛1轮",
				"pkPromotCount" : 16; 		// pk晋级人数
				"pkReviveCount": 0 			// PK失败复活数量，为0表示没有复活
				"pkDepth": 2, 				// 树形PK深度（等于PK轮次）
				"drawBeginTime": "2022-11-26 22:00:00", // 抽签开始时间
				"drawEndTime": "2022-11-26 23:59:59" 	// 抽签截止时间

			}]
		}
	}


2.获取晋级名单和复活人数
	
	http://127.0.0.1:8080/hdzt/pkGroupDraw/members?configId=1&amp;actId=2022103001&amp;rankId=1&amp;phaseId=2
	GET

	{
		"result": 200,
		"reason": "OK",
		"data": {
			"pkMembers": [{				// 晋级名单
				"score": 99,  			// PK成员晋级的分数
				"name": "name-10001", 	// PK成员名字
				"rank": 1,  			// PK成员晋级的名次
				"id": "id-10001" 		// PK成员ID
			}, {
				"score": 98,
				"name": "name-10002",
				"rank": 2,
				"id": "id-10002"
			}, {
				"score": 97,
				"name": "name-10003",
				"rank": 3,
				"id": "id-10003"
			}, {
				"score": 96,
				"name": "name-10004",
				"rank": 4,
				"id": "id-10004"
			}, {
				"score": 95,
				"name": "name-10005",
				"rank": 5,
				"id": "id-10005"
			}, {
				"score": 94,
				"name": "name-10006",
				"rank": 6,
				"id": "id-10006"
			}, {
				"score": 93,
				"name": "name-10007",
				"rank": 7,
				"id": "id-10007"
			}, {
				"score": 92,
				"name": "name-10008",
				"rank": 8,
				"id": "id-10008"
			}, {
				"score": 91,
				"name": "name-10009",
				"rank": 9,
				"id": "id-10009"
			}, {
				"score": 90,
				"name": "name-10010",
				"rank": 10,
				"id": "id-10010"
			}, {
				"score": 89,
				"name": "name-10011",
				"rank": 11,
				"id": "id-10011"
			}, {
				"score": 88,
				"name": "name-10012",
				"rank": 12,
				"id": "id-10012"
			}, {
				"score": 87,
				"name": "name-10013",
				"rank": 13,
				"id": "id-10013"
			}, {
				"score": 86,
				"name": "name-10014",
				"rank": 14,
				"id": "id-10014"
			}],

		}
	}


3.获取对战编排结果

	http://127.0.0.1:8080/hdzt/pkGroupDraw/getPositions?configId=1&amp;actId=2022103001&amp;rankId=1&amp;phaseId=2
	GET

	{
		"result": 200,
		"reason": "OK",
		"data": [
			"id-10001", 		// PK成员ID
			"id-10014",
			null, 				// 坑位3未分配成员
			null, 				// 坑位4未分配成员
			"id-10014",
			......
			..........
		]
	}


4.临时保存对战编排结果

	http://127.0.0.1:8080/hdzt/pkGroupDraw/savePositions
	POST - 请求Body：PkGroupDrawRequest[configId, actId, rankingId, phaseId, positions]

	{"result":200,"reason":"OK","data":true}


5.对战编排就绪

	http://127.0.0.1:8080/hdzt/pkGroupDraw/readyDone
	POST - 请求Body：PkGroupDrawRequest[configId, actId, rankingId, phaseId, positions]

	{"result":200,"reason":"OK","data":true}



6.重新编排对战关系

	http://127.0.0.1:8080/hdzt/pkGroupDraw/readyRedo
	POST - 请求Body：PkGroupDrawRequest[configId, actId, rankingId, phaseId]

	{"result":200,"reason":"OK","data":true}


7.开始抽签（前端轮询 或 主动触发）

	http://127.0.0.1:8080/hdzt/pkGroupDraw/drawStart
	POST - 请求Body：PkGroupDrawRequest[configId, actId, rankingId, phaseId]

	{
		"result": 200,
		"reason": "OK",
		"data": {
			"status": 1, 			// 状态， 1：开始正常抽签，2：编排未就绪，3：已提交到中台不可再抽了，其他值：不可抽签的状态
			"next": 2,      		// 下个开始抽签的数组下标，0表示第一个位置，依此类推，status==1 时有效
			"positions": [			// 所有预置的抽签结果，status==1 时有效
				"id-10002",	// 对应位置上的成员
				"id-10003",
				"id-10004",
				......
				..........
			]
		}
	}


8.抽下一个

	http://127.0.0.1:8080/hdzt/pkGroupDraw/drawNext
	POST - 请求Body：PkGroupDrawRequest[configId, actId, rankingId, phaseId, next]

	{"result":200,"reason":"OK","data":true}


9.抽签结果提交到中台

	http://127.0.0.1:8080/hdzt/pkGroupDraw/drawSubmit
	POST - 请求Body：PkGroupDrawRequest[configId, actId, rankingId, phaseId]

	{
		"result": 200,
		"reason": "OK",
		"data": "提交成功"
	}