import request from "@/utils/request"

export const queryActResources = (actId) => {
  return request({
    url: 'actResource/getActResources',
    method: 'get',
    params: { actId }
  })
}

export const addActResource = (actResource) => {
  return request({
    url: 'actResource/addActResource',
    method: 'put',
    data: { ...actResource }
  })
}

export const updateActResource = (actResource) => {
  return request({
    url: 'actResource/updateActResource',
    method: 'post',
    data: { ...actResource }
  })
}

export const deleteActResource = (actId, resourceKey) => {
  return request({
    url: 'actResource/deleteActResource',
    method: 'delete',
    params: { actId, resourceKey }
  })
}

export const copyActResources = (actId, copyActId) => {
  return request({
    url: 'actResource/copyActResources',
    method: 'get',
    params: { actId, copyActId }
  })
}
