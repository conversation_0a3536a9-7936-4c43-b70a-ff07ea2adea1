import request from "@/utils/request"

export const queryAllPhases = (actId) => {
  return request({
    url: 'aov/mgr/phase/list',
    method: 'get',
    params: { actId }
  })
}

export const queryPhaseDetail = (actId, phaseId) => {
  return request({
    url: 'aov/mgr/phase/info',
    method: 'get',
    params: { actId, phaseId }
  })
}

export const queryPhaseRounds = (actId, phaseId) => {
  return request({
    url: 'aov/mgr/phase/rounds',
    method: 'get',
    params: { actId, phaseId }
  })
}

export const queryPhaseAwards = (actId, phaseId) => {
  return request({
    url: 'aov/mgr/phase/awards',
    method: 'get',
    params: { actId, phaseId }
  })
}

export const queryPhaseTeams = (actId, phaseId, state, page, pageSize) => {
  return request({
    url: 'aov/mgr/phase/teams',
    method: 'get',
    params: { actId, phaseId, state, page, pageSize }
  })
}

export const queryPhaseGames = (phaseId, roundNum, memberUid, state, page, pageSize) => {
  return request({
    url: 'aov/mgr/phase/games',
    method: 'get',
    params: { phaseId, roundNum, memberUid, state, page, pageSize }
  })
}

export const queryRoundGames = (actId, phaseId, roundNum) => {
  return request({
    url: 'aov/roundGames',
    method: 'get',
    params: { actId, phaseId, roundNum }
  })
}

export const createRoundGame = (req) => {
  console.log(req)
  return request({
    url: 'aov/createRoundGame',
    method: 'post',
    data: { ... req }
  })
}
