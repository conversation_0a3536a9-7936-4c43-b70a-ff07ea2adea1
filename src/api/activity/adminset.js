/**
 *@desc 权限管理
 *<AUTHOR>
 */
import request from '@/utils/request'

export default {

  permissionPageUrl() {
    return request({
      url: '/menu/permissionPageUrl',
      method: 'get'
    })
  },

  // 获取用户角色菜单
  getMenus () {
    return request({
      url: 'menu/listMenus'
    })
  },

  // 权限列表
  getPermissions(accessKey = 'hdzt-admin') {
    return request({
      url: '/menu/getPermissions',
     // dataType: 'jsonp',
      params: {
        accessKey
      }
    })
  },

  // 提交权限申请
  applyPermission(data) {
    return request({
      url: '/menu/applyPermission',
      method: 'post',
      params: data
    })
  },

  // 获取官方管理员列表
  adminMgrList(data) {
    return request({
      url: '/adminmgr/internal/adminMgrList',
      params: data
    })
  },
  // 新增更改官方管理权权限
  updateManager(data) {
    return request({
      url: '/adminmgr/internal/updateManager',
      params: data
    })
  },
  // 删除官方管理员权限
  delAdminMgr(data) {
    return request({
      url: '/adminmgr/internal/delAdminMgr',
      params: data
    })
  },
  // 查询权限
  adminMenuList() {
    return request({
      url: '/adminmgr/internal/adminMenuList'
    })
  }
}
