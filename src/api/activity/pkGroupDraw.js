import request from '@/utils/request'
import { Message } from 'view-design'
export default {
  // 活动列表
  getList (params) {
    return request({
      url: 'hdzt/pkGroupDraw/list',
      params,
      method: 'get',
    })
  },
  // 抽签池
  getMembers (params) {
    return request({
      url: 'hdzt/pkGroupDraw/members',
      params,
      method: 'get',
    })
  },
  // 获取临时保存数据
  getPositions (params) {
    return request({
      url: 'hdzt/pkGroupDraw/getPositions',
      params,
      method: 'get',
    })
  },
  // 提交暂存数据
  savePositions (params) {
    return request({
      url: 'hdzt/pkGroupDraw/savePositions',
      data: params,
      method: 'post',
    })
    .then(res => {
      if (res.result === 200) {
        Message.success('保存成功');
      }
    })
  },
  // readyDone 发布到抽签工具, 调用后只能通过 redo 重新调用
  readyDone (params) {
    return request({
      url: 'hdzt/pkGroupDraw/readyDone',
      data: params,
      method: 'post',
    })
    .then(res => {
      if (res.result === 200) {
        Message.success('保存成功');
      }
    })
  },
  // 修改
  readyRedo (params) {
    return request({
      url: 'hdzt/pkGroupDraw/readyRedo',
      data: params,
      method: 'post',
    })
    .then(res => {
      if (res.result === 200) {
        Message.success('撤销成功');
      }
    })
  },
  // 最后发布到后台 确认抽签结果
  drawSubmit (params) {
    return request({
      url: 'hdzt/pkGroupDraw/drawSubmit',
      data: params,
      method: 'post',
    })
    .then(res => {
      if (res.result === 200) {
        Message.success('操作成功');
      }
    })
  },

}
