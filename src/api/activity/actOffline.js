import request from "@/utils/request"

export default {
  queryActList(formData) {
    return request({
      url: 'actOffline/act/list',
      params: formData,
      method: 'get'
    })
  },

  copyHdzk2Pika(actId) {
    return request({
      url: 'actOffline/copyHdzk2Pika/' + actId,
      method: 'get'
    })
  },

  copyHdzt2Pika(actId) {
    return request({
      url: 'actOffline/copyHdzt2Pika/' + actId,
      method: 'get'
    })
  },

  setActArchive(actId) {
    return request({
      url: 'actOffline/setActArchive/' + actId,
      method: 'get'
    })
  },

  delHdzkRedis(actId) {
    return request({
      url: 'actOffline/delHdzkRedis/' + actId,
      method: 'get'
    })
  },

  delHdztRedis(actId) {
    return request({
      url: 'actOffline/delHdztRedis/' + actId,
      method: 'get'
    })
  },

  formatFileSize(fileSize) {
    const kb = 1024;
    const mb = kb * 1024;
    const gb = mb * 1024;
    if (fileSize < kb) {
      return fileSize + 'B'
    } else if (fileSize < mb) {
      let temp = fileSize / kb;
      temp = temp.toFixed(2);
      return temp + 'K'
    } else if (fileSize < gb) {
      let temp = fileSize / mb;
      temp = temp.toFixed(2);
      return temp + 'M'
    } else {
      let temp = fileSize / gb;
      temp = temp.toFixed(2);
      return temp + 'G'
    }
  }
}
