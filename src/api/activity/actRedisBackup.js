import request from "@/utils/request"

export default {
  queryActBackupConfigs(formData) {
    return request({
      url: 'backup/config',
      params: formData,
      method: 'get'
    })
  },

  queryActSourceProp(configId) {
    return request({
      url: 'backup/config/source/' + configId,
      method: 'get'
    })
  },

  queryBackupResult(recordId) {
    return request({
      url: 'backup/config/state',
      params: {recordId: recordId},
      method: 'get'
    })
  },

  addActBackupConfig(configFormData) {
    return request({
      url: 'backup/config',
      data: configFormData,
      method: 'post'
    })
  },

  updateActBackupConfig(configFormData) {
    return request({
      url: 'backup/config/' + configFormData.id,
      data: configFormData,
      method: 'put'
    })
  },

  deleteActBackupConfig(configId) {
    return request({
      url: 'backup/config/' + configId,
      method: 'delete'
    })
  },

  backupActSource(configId) {
    return request({
      url: 'backup/config/do',
      params: {configId: configId}
    })
  },

  formatFileSize(fileSize) {
    const kb = 1024;
    const mb = kb * 1024;
    const gb = mb * 1024;
    if (fileSize < kb) {
      return fileSize + 'B'
    } else if (fileSize < mb) {
      let temp = fileSize / kb;
      temp = temp.toFixed(2);
      return temp + 'K'
    } else if (fileSize < gb) {
      let temp = fileSize / mb;
      temp = temp.toFixed(2);
      return temp + 'M'
    } else {
      let temp = fileSize / gb;
      temp = temp.toFixed(2);
      return temp + 'G'
    }
  }
}
