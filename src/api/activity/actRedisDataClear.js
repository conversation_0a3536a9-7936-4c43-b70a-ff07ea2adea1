import request from '@/utils/request'

export default {
  loadDataSource() {
    return request({
      url: 'actRedisDataClear/listDropDown?type=1',
      method: 'get',
    })
  },
  loadDataScope(dataSource) {
    return request({
      url: '/actRedisDataClear/listDropDown?type=2&dataSource=' + dataSource,
      method: 'get',

    })
  },
  preview(param) {
    return request({
      url: '/actRedisDataClear/preview?' + param,
      method: 'get',
    })
  },
  clean(param) {
    return request({
      url: '/actRedisDataClear/cleanKey',
      method: 'post',
      data: param,
      timeout: 1200000
    })
  },
  cleanAll(param) {
    return request({
      url: '/actRedisDataClear/cleanAll',
      method: 'post',
      data: param,
      timeout: 1200000
    })
  },
  scanKeys(param) {
    return request({
      url: '/actRedisDataClear/scanKeys',
      method: 'post',
      data: param,
      timeout: 1200000
    })
  },
  loadMysqlAll(param) {
    return request({
      url: '/actMysqlDataClear/loadMysqlAll',
      method: 'post',
      data: param,
      timeout: 1200000
    })
  }
}
