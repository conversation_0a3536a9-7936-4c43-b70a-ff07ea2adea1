import request from '@/utils/request'


export default {
  queryViewDefineAttr(data) {
    return request({
      url: 'viewDefineAttr/queryViewDefineAttr',
      params: data,
      method: 'get',
    })
  },
  addViewDefineAttr(data) {
    return request({
      url: 'viewDefineAttr/addViewDefineAttr',
      data: data,
      method: 'post',
    })
  },

  updateViewDefineAttr(data) {
    return request({
      url: 'viewDefineAttr/updateViewDefineAttr',
      data: data,
      method: 'post',
    })
  },

  copyViewDefineAttr(data) {
    return request({
      url: 'viewDefineAttr/copyViewDefineAttr',
      data: data,
      method: 'post',
    })
  }

}
