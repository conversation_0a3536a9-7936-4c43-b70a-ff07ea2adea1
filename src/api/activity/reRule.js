import request from '@/utils/request'


export default {
  queryReRule(ruleId) {
    return request({
      url: 'reRule/queryReRule?ruleId=' + ruleId,
      method: 'get',
    })
  },
  addReRule(data) {
    return request({
      url: 'reRule/addReRule',
      data: data,
      method: 'post',
    })
  },

  updateReRule(data) {
    return request({
      url: 'reRule/updateReRule',
      data: data,
      method: 'post',
    })
  },
  copyReRule(data) {
    return request({
      url: 'reRule/copyReRule',
      data: data,
      method: 'post',
    })
  },
}
