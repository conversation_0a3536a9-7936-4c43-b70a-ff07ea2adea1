import request from "@/utils/request"

export const queryActivityInfo = (actId) => {
  return request({
    url: 'center/activity/info',
    method: 'get',
    params: { actId }
  })
}

export const queryAllConfigCategories = () => {
  return request({
    url: 'center/config/categories',
    method: 'get'
  })
}

export const queryConfigItems = (actId, categoryId) => {
  return request({
    url: 'center/config/items',
    params: { actId, categoryId },
    method: 'get'
  })
}

export const queryConfigSnippets = (actId, categoryId, itemId) => {
  return request({
    url: 'center/config/snippets',
    params: { actId, categoryId, itemId },
    method: 'get'
  })
}

export const queryConfigSnippetValue = (actId, categoryId, itemId, snippetId) => {
  return request({
    url: 'center/config/snippet/value',
    params: { actId, categoryId, itemId, snippetId },
    method: 'get'
  })
}

export const saveConfigSnippetState = (actId, categoryId, itemId, snippetId, state) => {
  return request({
    url: 'center/config/snippet/state',
    params: { actId, categoryId, itemId, snippetId, state },
    method: 'post'
  })
}

export const queryPageCmptWhitelist = (actId, componentIndex, member, page, pageSize) => {
  return request({
    url: 'component/whitelist/page',
    params: { actId, componentIndex, member, page, pageSize },
    method: 'get'
  })
}

export const addCmptWhitelist = (actId, componentIndex, member, configValue) => {
  return request({
    url: 'component/whitelist/one',
    params: { actId, componentIndex, member, configValue },
    method: 'put'
  })
}

export const updateCmptWhitelist = (actId, componentIndex, member, configValue) => {
  return request({
    url: 'component/whitelist/value',
    params: { actId, componentIndex, member, configValue },
    method: 'post'
  })
}

export const deleteCmptWhitelist = (actId, componentIndex, member) => {
  return request({
    url: 'component/whitelist/one',
    params: { actId, componentIndex, member },
    method: 'delete'
  })
}

export const importCmptWhitelist = (actId, componentIndex, file) => {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('actId', actId)
  formData.append('componentIndex', componentIndex)
  return request({
    url: 'component/whitelist/import',
    method: 'post',
    headers: { 'Content-Type': 'multipart/form-data' },
    data: formData
  })
}
