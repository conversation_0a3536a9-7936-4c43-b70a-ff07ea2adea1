import request from "@/utils/request"

export const queryPageTemplateInfo = (params) => {
  return request({
    url: 'template/management/template',
    method: 'get',
    params: { ... params }
  })
}

export const queryTemplateAttrTemplate = (templateId) => {
  return request({
    url: 'template/management/template/attr/template',
    method: 'get',
    params: { templateId }
  })
}

export const addTemplateAttrTemplate = (attrTemplate) => {
  return request({
    url: 'template/management/template/attr/template',
    method: 'put',
    data: attrTemplate
  })
}

export const updateTemplateAttrTemplate = (attrTemplate) => {
  return request({
    url: 'template/management/template/attr/template',
    method: 'post',
    data: attrTemplate
  })
}

export const deleteTemplateAttrTemplate = (templateId, db, tbl, col, rowIndex) => {
  return request({
    url: 'template/management/template/attr/template',
    method: 'delete',
    params: { templateId, db, tbl, col, rowIndex }
  })
}

export const queryTemplateAttrDefine = (templateId) => {
  return request({
    url: 'template/management/template/attr/define',
    method: 'get',
    params: { templateId }
  })
}

export const addTemplateAttrDefine = (attrDefine) => {
  return request({
    url: 'template/management/template/attr/define',
    method: 'put',
    data: attrDefine
  })
}

export const updateTemplateAttrDefine = (attrDefine) => {
  return request({
    url: 'template/management/template/attr/define',
    method: 'post',
    data: attrDefine
  })
}

export const deleteTemplateAttrDefine = (templateId, db, tbl, col, rowIndex) => {
  return request({
    url: 'template/management/template/attr/define',
    method: 'delete',
    params: { templateId, db, tbl, col, rowIndex }
  })
}
