import request from '@/utils/request'

export default {
  listAwardTask(actId) {
    return request({
      url: 'awardConfig/listAwardTask?actId=' + actId,
      method: 'get',
    })
  },
  saveAwardTask(param) {
    return request({
      url: 'awardConfig/saveAwardConfig',
      method: 'post',
      data: param
    })
  },
  saveCopyAwardTask(param) {
    return request({
      url: 'awardConfig/copyAwardConfig',
      method: 'post',
      data: param
    })
  }
}
