import request from "@/utils/request"

export const getActTemplateList = () => {
  return request({
    url: 'template/list',
    method: 'get'
  })
}

export const getActStateList = () => {
  return request({
    url: 'system/dict/data/type/fast_act_status',
    method: 'get'
  })
}

export const queryFastConfigActList = (params) => {
  return request({
    url: 'template/activity/list',
    method: 'get',
    params: { ...params }
  })
}

export const queryCopyActivityPreview = (req) => {
  return request({
    url: 'template/copy/activity/preview',
    method: 'post',
    data: { ... req },
    timeout: 20000
  })
}

export const copyActivity = (req) => {
  return request({
    url: 'template/copy/activity',
    method: 'post',
    data: { ... req },
    timeout: 60000
  })
}

export const deleteTemplateAct = (actId) => {
  return request({
    url: 'template/activity/info',
    method: 'delete',
    params: { actId }
  })
}

export const deleteFlushedConfig = (actId) => {
  return request({
    url: 'template/delete/config',
    method: 'delete',
    params: { actId },
    timeout: 20000
  })
}
