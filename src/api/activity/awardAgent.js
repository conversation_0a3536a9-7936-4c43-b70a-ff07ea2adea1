import request from "@/utils/request"

export const queryRewardAgentConfigs = (actId) => {
  return request({
    url: 'awardConfig/queryRewardAgents',
    method: 'get',
    params: { actId }
  })
}


export const addRewardAgentConfig = (actId, taskId, agentConfig) => {
  return request({
    url: 'awardConfig/addRewardAgent',
    method: 'put',
    params: { actId, taskId },
    data: { ...agentConfig }
  })
}

export const updateRewardAgentConfig = (actId, agentConfig) => {
  return request({
    url: 'awardConfig/updateAwardAgent',
    method: 'post',
    params: { actId },
    data: { ...agentConfig }
  })
}
