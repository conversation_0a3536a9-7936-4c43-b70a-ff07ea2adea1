import request from '@/utils/request'
export default {

  pkConfigs (data) {
    return request({
      url: 'pkconfigV2/pkConfigs',
      params: data,
      method: 'get',
    })
  },
  pkMembers (data) {
    return request({
      url: 'pkconfigV2/pkMembers',
      params: data,
      method: 'get',
      contentType: 'application/json'
    })
  },

  setupPKConfig (data) {
    return request({
      url: 'pkconfigV2/setupPKConfig',
      data: data,
      method: 'post',
    })
  },

}
