import request from '@/utils/request'

export default {
  pageRankTaskAwardIssue(param) {
    return request({
      url: 'rankingTaskAward/pageIssue',
      method: 'post',
      data: param,
    })
  },
  exportExcel(param) {
    return request({
      url: 'rankingTaskAward/exportExcel',
      method: 'get',
      params: param,
    })
  },
  listRankDropDown(actId) {
    return request({
      url: 'rankingTaskAward/listRankDropDown?actId=' + actId,
      method: 'post',
    })
  }
}
