import request from '@/utils/request'

export default {
  backupActData(data) {
    return request({
      url: 'actData/backupRankData',
      params: data,
      method: 'get',
    })
  },
  listAllBackupAct() {
    return request({
      url: 'actData/listAllBackupAct',
      method: 'get',
    })
  },
  dropActDataTable(data) {
    return request({
      url: 'actData/dropActDataTable',
      params: data,
      method: 'get'
    })
  }

}
