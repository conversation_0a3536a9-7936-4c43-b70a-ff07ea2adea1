import request from '@/utils/request'

export default {
  listAwardPackage(actId, taskId) {
    return request({
      url: 'awardConfig/listAwardPackage?actId=' + actId + "&taskId=" + taskId,
      method: 'get',
    })
  },
  saveAwardPackage(param) {
    return request({
      url: 'awardConfig/saveAwardPackage',
      data: param,
      method: 'post',
    })
  },
  listDropDown(type) {
    return request({
      url: 'awardConfig/listDropDown?dropDownType=' + type,
      method: 'get',
    })
  },
  saveAwardPackageItem(param) {
    return request({
      url: 'awardConfig/saveAwardPackageItem',
      data: param,
      method: 'post',
    })
  },
  removePackage(actId, taskId, packageId) {
    return request({
      url: 'awardConfig/removePackage?actId=' + actId + "&taskId=" + taskId + "&packageId=" + packageId,
      method: 'get',
    })
  }
}
