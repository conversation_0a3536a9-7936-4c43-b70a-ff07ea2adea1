import request from '@/utils/request'
export default {

  fetchPkgroup (data) {
    return request({
      url: 'pkConfig/fetchQualPkGroup.do',
      params: data,
      method: 'get',
    })
  },
  updatePkgroup (data) {
    return request({
      url: 'pkConfig/updateQualPkGroup.do',
      data: data,
      method: 'post',
    })
  },
  markAccomplished (data) {
    return request({
      url: 'pkConfig/markAccomplished.do',
      params: data,
      method: 'get',
    })
  },
  unmarkAccomplished (data) {
    return request({
      url: 'pkConfig/unmarkAccomplished.do',
      params: data,
      method: 'get',
    })
  },
}
