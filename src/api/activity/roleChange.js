import request from '@/utils/request'
export default {
  queryUserRole(data) {
    return request({
      url: 'changeRole/queryUserRole',
      params: data,
      method: 'get',
    })
  },
  calculateRoleChange(data) {
    return request({
      url: 'changeRole/calculateRoleChange',
      data: data,
      method: 'post',
    })
  },

  executeChangeRole(data) {
    return request({
      url: 'changeRole/executeChangeRole',
      data: data,
      method: 'post',
    })
  }
}
