import request from '@/utils/request'

export default {

  loadActInfos() {
    return request({
      url: 'hdzt/support/queryActList?type=1',
      method: 'get',
    })
  },
  queryActConfigVersions(actId, db) {
    return request({
      url: 'hdztmanager/actConfigVersion/list?actId=' + actId + '&dataBase=' + db,
      method: 'get',
    })
  },
  backup(actId, db, desc) {
    return request({
      url: 'hdztmanager/actConfigVersion/backup?actId=' + actId + '&dataBase=' + db + '&desc=' + desc,
      method: 'get',
    })
  },
  synToDeploy(actId, db, execute) {
    return request({
      url: 'activityHelp/synActConfigToDeploy?actId=' + actId + '&dataBase=' + db + '&execute=' + execute,
      method: 'get',
      timeout: 1200000, //设置时间超时，单位毫秒
    })
  },
  queryCompare(actId, db, id1, id2) {
    return request({
      url: 'hdztmanager/actConfigVersion/compare?actId=' + actId + '&dataBase=' + db + '&id1=' + id1 + "&id2=" + id2,
      method: 'get',
      timeout: 1200000, //设置时间超时，单位毫秒
    })
  },
  recoverActConfig(param) {
    return request({
      url: 'activityHelp/recoverActConfig',
      method: 'post',
      data: param,
      timeout: 1200000 //设置时间超时，单位毫秒
    })
  }

}
