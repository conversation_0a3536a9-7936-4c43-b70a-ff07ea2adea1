import request from '@/utils/request'
export default {
  exeRankDataOp (data) {
    return request({
      url: 'redis/exeRankDataOp',
      data: data,
      method: 'post',
    })
  },

  queryRankData(data) {
    return request({
      url: 'redis/queryRankData',
      params: data,
      method: 'get',
    })
  },

  queryCommandData(data) {
    return request({
      url: 'redis/queryCommandData',
      params: data,
      method: 'get',
    })
  },
  actIds () {
    return request({
      url: 'hdztmanager/actIds',
      method: 'get',
    })
  },
  queryRedisIP(data) {
    return request({
      url: 'redis/redisIp',
      params: data,
      method: 'get',
    })
  },
}
