import request from "@/utils/request"

export const queryProcessStep = (procId, actId) => {
  return request({
    url: 'process/query',
    method: 'get',
    params: {procId, actId}
  })
}

export const startProcess = (procId, actId, vars) => {
  return request({
    url: 'process/start',
    method: 'post',
    params: {procId, actId},
    data: vars
  })
}

export const executeProcessActivity = (procInstanceId, activityId) => {
  return request({
    url: 'process/activity/execute',
    method: 'post',
    params: {procInstanceId, activityId}
  })
}

export const restartProcessActivity = (procInstanceId, activityId) => {
  return request({
    url: 'process/activity/restart',
    method: 'post',
    params: {procInstanceId, activityId}
  })
}

export const cleanGreyStatus = (actId) => {
  return request({
    url: 'actMysqlDataClear/cleanGreyStatus',
    method: 'get',
    params: {actId},
    timeout: 1200000
  })
}

export const getDbComparing = (actId, db, env = '') => {
  return request({
    url: 'deploy/config/comparing',
    method: 'get',
    params: { actId, db, env },
    timeout: 30000
  })
}

export const batchExecuteStatements = (actId, db, statements) => {
  return request({
    url: 'deploy/statement/execute',
    method: 'post',
    params: { actId, db },
    data: [ ... statements ]
  })
}
