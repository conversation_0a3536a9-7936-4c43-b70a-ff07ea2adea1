import request from '@/utils/request'
export default {
  queryRank (data) {
    return request({
      url: 'excel/queryRank',
      params: data,
      method: 'get',
    })
  },

  queryRankData(data) {
    return request({
      url: 'redis/queryRankData',
      params: data,
      method: 'get',
    })
  },

  queryCommandData(data) {
    return request({
      url: 'redis/queryCommandData',
      params: data,
      method: 'get',
    })
  }
}
