import request from '@/utils/request'

export default {
  // 刷新命令
  getVirtTime (actId) {
    return request({
      url: 'hdzt_http/getVirtTime.do',
      params: {
        'actId': actId
      },
      method: 'get',
    })
  },
  // 修改命令
  setVirtTime (actId,time) {
    return request({
      url: 'hdzt_http/setVirtTime.do',
      params: {
        'actId': actId,
        'time': time
      },
      method: 'get',
    })
  }
}
