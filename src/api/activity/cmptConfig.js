import request from '@/utils/request'

export default {
  loadComponentConfig(actId, componentId, moduleName) {
    return request({
      url: 'cmptConfig/list?actId=' + actId + "&componentId=" + componentId + "&moduleName=" + moduleName,
      method: 'get',
    })
  },
  listCmptByActIds(actIds) {
    return request({
      url: 'cmptConfig/listCmptByActIds?actIds=' + actIds,
      method: 'get',
    })
  },

  multiCopyingCmpt(data) {
    return request({
      url: 'cmptConfig/mutliCopyCmpt',
      data: data,
      method: 'post',
    })
  },

  copyActComponent(data) {
    return request({
      url: 'cmptConfig/copyActComponent',
      method: 'post',
      data: data,
    })
  },
  loadComponentDefines() {
    return request({
      url: 'cmptConfig/listComponentDefine',
      method: 'post',
    })
  },
  saveActComponent(data) {
    return request({
      url: 'cmptConfig/saveActComponent',
      method: 'post',
      data: data,
    })
  },
  deleteActComponent(data) {
    return request({
      url: 'cmptConfig/deleteActComponent',
      method: 'post',
      data: data,
    })
  },
  synComponentAttrDefine(actId, componentId) {
    return request({
      url: 'cmptConfig/synComponentAttrDefine',
      method: 'get',
      params: { actId, componentId }
    })
  },
  initActComponent(json) {
    return request({
      url: 'cmptConfig/initActComponent',
      method: 'post',
      data: json
    })
  }
}
