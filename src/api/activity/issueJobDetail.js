import request from '@/utils/request'

export default {
  issuejobdetailLog(param) {
    return request({
      url: 'issuejobdetail/list',
      method: 'post',
      data: param,
    })
  },
  saveIssueJob(param) {
    return request({
      url: 'issuejob/saveIssueJob',
      method: 'post',
      data: param,
    })
  },
  exportExcel(param) {
    return request({
      url: 'awardIssueLog/exportExcel',
      method: 'get',
      params: param,
    })
  }
}
