import request from "@/utils/request"

export default {
  queryActOfflineDetail(actId) {
    return request({
      url: 'actOffline/task/detail',
      params: {actId: actId},
      method: 'get'
    })
  },

  queryActOfflineStepDetail(actId, stepId) {
    return request({
      url: 'actOffline/task/step/detail',
      params: {actId: actId, stepId: stepId},
      method: 'get'
    })
  },

  addActOfflineTask(actId) {
    return request({
      url: 'actOffline/task',
      params: {actId: actId},
      method: 'post'
    })
  },

  executeStep(actId, stepId) {
    return request({
      url: 'actOffline/step/execute',
      params: {actId: actId, stepId: stepId},
      method: 'put'
    })
  },

  refreshProcessingStep(actId, stepId) {
    return request({
      url: 'actOffline/step/fresh',
      params: {actId: actId, stepId: stepId},
      method: 'put'
    })
  }
}
