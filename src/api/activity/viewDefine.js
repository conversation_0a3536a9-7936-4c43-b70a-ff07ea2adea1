import request from '@/utils/request'


export default {
  queryViewDefine(data) {
    return request({
      url: 'viewDefine/queryViewDefine',
      params: data,
      method: 'get',
    })
  },
  addViewDefine(data) {
    return request({
      url: 'viewDefine/addViewDefine',
      data: data,
      method: 'post',
    })
  },

  updateViewDefine(data) {
    return request({
      url: 'viewDefine/updateViewDefine',
      data: data,
      method: 'post',
    })
  },
  copyViewDefine(data) {
    return request({
      url: 'viewDefine/copyViewDefine',
      data: data,
      method: 'post',
    })
  },
}
