import request from '@/utils/request'

export default {
  listRankTaskConfig(actId) {
    return request({
      url: 'rankTaskConfig/listRankTaskConfig?actId=' + actId,
      method: 'get',
    })
  },
  listRankDropDown(actId) {
    return request({
      url: 'rankTaskConfig/listRankDropDown?actId=' + actId,
      method: 'get',
    })
  },
  listRankPhaseDropDown(actId, rankId) {
    return request({
      url: 'rankTaskConfig/listRankPhaseDropDown?actId=' + actId + "&rankId=" + rankId,
      method: 'get',
    })
  },
  listAwardTaskDropDown(actId, model) {
    return request({
      url: 'rankTaskConfig/listAwardTaskDropDown?actId=' + actId + "&model=" + model,
      method: 'get',
    })
  },
  listAwardPackageDropDown(actId, taskId) {
    return request({
      url: 'rankTaskConfig/listAwardPackageDropDown?actId=' + actId + "&taskId=" + taskId,
      method: 'get',
    });
  },
  saveRankTaskConfig(param) {
    return request({
      url: 'rankTaskConfig/saveRankTaskConfig',
      data: param,
      method: 'post',
    })
  },
  saveRankTaskItem(param) {
    return request({
      url: 'rankTaskConfig/saveRankTaskItem',
      data: param,
      method: 'post',
    })
  },

  getRankItem(actId, rankId) {
    return request({
      url: 'rankTaskConfig/getRankItem?actId=' + actId + "&rankId=" + rankId,
      method: 'get',
    })
  },
  removeRankTaskConfig(param){
    return request({
      url: 'rankTaskConfig/deleteRankTaskConfig',
      data: param,
      method: 'post',
    })
  }
}
