import request from '@/utils/request'


export default {
  queryRoleRankMap(data) {
    return request({
      url: 'roleRankMap/queryRoleRankMap',
      params: data,
      method: 'get',
    })
  },
  addRoleRankMap(data) {
    return request({
      url: 'roleRankMap/addRoleRankMap',
      data: data,
      method: 'post',
    })
  },

  updateRoleRankMap(data) {
    return request({
      url: 'roleRankMap/updateRoleRankMap',
      data: data,
      method: 'post',
    })
  },
  copyRoleRankMap(data) {
    return request({
      url: 'roleRankMap/copyRoleRankMap',
      data: data,
      method: 'post',
    })
  },

}
