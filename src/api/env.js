
// 是否技术人员访问
export const isTecAccess = location.hostname.indexOf('-tec') >= 0

// 计算申请权限url
export const accessKey = isTecAccess ? 'hdzt-tec-admin' : 'hdzt-adminV2'
// 申请权限页面地址
export const applyAuthUrl = `${process.env.PERMISSION_HOST}/admin-static/adminset.html?accessKey=${accessKey}`

// 获取有权限的菜单地址 https://zhuiya-test.yy.com/web/permission/internal/getMenus?accessKey=zhuiya-admin
export const getAuthorizedMenusUrl = `${process.env.PERMISSION_HOST}/web/permission/internal/getMenus?accessKey=${accessKey}`

// 组件文档 地址
export const componentListLink = `${process.env.ENV}` === 'production'
    ? (isTecAccess ? 'https://manager-hdzt-tec.yy.com/ComponentList.html' : 'https://manager-hdzt.yy.com/ComponentList.html')
    : (isTecAccess ? 'https://test-manager-hdzt-tec.yy.com/ComponentList.html' : 'https://manager-hdzt-tec.yy.com/ComponentList.html')
