import {isTecAccess} from '@/api/env'
import {commonRouters, businessRoutes} from '@/router'
import {getRouters, getAuthorizedMenus} from '@/api/menu'
import Layout from '@/layout/index'
import ParentView from '@/components/ParentView'
import InnerLink from '@/layout/components/InnerLink'

const permission = {
  state: {
    routes: [],
    addRoutes: [],
    defaultRoutes: [],
    topbarRouters: [],
    sidebarRouters: []
  },
  mutations: {
    SET_ROUTES: (state, routes) => {
      state.addRoutes = routes
      state.routes = commonRouters.concat(routes)
    },
    SET_DEFAULT_ROUTES: (state, routes) => {
      state.defaultRoutes = routes
    },
    SET_TOPBAR_ROUTES: (state, routes) => {
      // 顶部导航菜单默认添加统计报表栏指向首页
      const index = [{
        path: 'index',
        meta: {title: '统计报表', icon: 'dashboard'}
      }]
      state.topbarRouters = routes.concat(index);
    },
    SET_SIDEBAR_ROUTERS: (state, routes) => {
      state.sidebarRouters = routes
    },
  },
  actions: {
    // 生成路由
    GenerateRoutes({commit}) {
      return new Promise(resolve => {
        // 基于zhuiya 菜单实现
        const zhuiyaResolver = (resolve) => {
          getAuthorizedMenus().then(res => {
            const path2menu = getPath2MenuInfo(res.data)
            // 根据有权限的菜单，激活可显示的路由, 并进行排序
            activeRouters(businessRoutes, path2menu)
            console.log('getAuthorizedMenus displayPaths', path2menu, 'sidebarRoutes:', businessRoutes)
            // 侧边栏 和 顶部路由
            const sidebarRoutes = businessRoutes
            commit('SET_ROUTES', sidebarRoutes)
            commit('SET_SIDEBAR_ROUTERS', commonRouters.concat(businessRoutes))
            commit('SET_DEFAULT_ROUTES', sidebarRoutes)
            commit('SET_TOPBAR_ROUTES', sidebarRoutes)
            resolve(sidebarRoutes)
          })
        }

        // 基于自身的 sys_menu 实现，走getRouters接口
        const selfResolver = (resolve) => {
          getRouters().then(res => {
            const sdata = JSON.parse(JSON.stringify(res.data))
            const rdata = JSON.parse(JSON.stringify(res.data))
            const sidebarRoutes = filterAsyncRouter(sdata)
            const rewriteRoutes = filterAsyncRouter(rdata, false, true)
            rewriteRoutes.push({path: '*', redirect: '/404', hidden: true})

            commit('SET_ROUTES', rewriteRoutes)
            commit('SET_SIDEBAR_ROUTERS', commonRouters.concat(sidebarRoutes))
            commit('SET_DEFAULT_ROUTES', sidebarRoutes)
            commit('SET_TOPBAR_ROUTES', sidebarRoutes)
            resolve(rewriteRoutes)
          })
        }

        // 选择菜单实现，后续统一都用 zhuiyaResolver
        //let resolver = isTecAccess ? zhuiyaResolver : selfResolver
        let resolver = zhuiyaResolver
        resolver(resolve)
      })
    }
  }
}

// 激活路由菜单
function activeRouters(routers, path2menu) {
  if (!routers || routers.length === 0 || !path2menu) {
    return
  }
  // 用于保存本地菜单映射
  let codePath2menu = {}
  let fillFullPathAndParent = (parentPath, parent, router) => {
    let path = parentPath + (/^\//.test(router.path) ? router.path : '/' + router.path)
    path = path.replace(/\/+/, '/')
    router.fullPath = path
    if (parent) {
      router.parent = parent
    }
    if (router.children && router.children.length) {
      router.children.forEach(child => fillFullPathAndParent(path, router, child))
    }
    codePath2menu[path] = router
    if (router.componentPath) {
      codePath2menu[router.componentPath] = router
    }
    // 这种要预先加载组件，否则后面加载不到
    if (!router.component && router.componentPath && router.alwaysHidden) {
      router.component = loadView(router.componentPath)
    }
  }
  routers.forEach(router => fillFullPathAndParent('', null, router))

  // 重新整理父子菜单关系, 按照zhuiya后台配置的来，允许你通过zhuiya来控制菜单的层级关系
  let fixTreeLevel = (parentPath, codeParent, router) => {
    let path = router.fullPath
    // 获取后台对应的配置
    let realRouter = path2menu[router.componentPath] || path2menu[path]
    if (realRouter && codeParent) {
      // 获取parent，看看和当前的parent是否是同一个，不同的话，需要进行移动
      let realParent = realRouter.parent
      if (realParent) {
        // 获取 realParent 对应的 codeParent
        let p = codePath2menu[realParent.componentPath] || codePath2menu[realParent.fullPath]
        if (p) {
          if (p !== codeParent) {
            // 不是同一个 parent，需要进行位置交换，把当前 router 挂到 p 下面，并从 parent 中移除
            if (!p.children) {
              p.children = []
            }
            // 挂到新的 p
            p.children = p.children.concat([router])
            // 从 parent 中移除
            codeParent.children = codeParent.children.filter(item => item !== router)
          }
        }
      }
    }
    if (router.children && router.children.length) {
      router.children.forEach(child => fixTreeLevel(path, router, child))
    }
  }
  routers.forEach(router => fixTreeLevel('', null, router))

  // 排序，按照 orderNo 倒序
  let sorter = (a, b) => {
    let o1 = a.orderNo || 0
    let o2 = b.orderNo || 0
    // 从大到小
    return o2 - o1
  }
  let iter = (router) => {
    let menu = path2menu[router.componentPath] || path2menu[router.fullPath]
    if (menu) {
      router.hidden = router.alwaysHidden === undefined ? false : router.alwaysHidden
      router.meta.title = menu.name
      router.meta.icon = menu.icon || router.meta.icon
      router.orderNo = menu.orderNo || 0
      if (!router.component && router.componentPath) {
        router.component = loadView(router.componentPath)
      }
    }
    //router.hidden = false
    if (router.children && router.children.length) {
      router.children.forEach(child => iter(child))
      router.children.sort(sorter)
      // 检查children，是否都没有展示
      for (const child of router.children) {
        if (!child.hidden) {
          return
        }
      }
      router.hidden = true
      if (!router.parent) {
        router.alwaysShow = false
      }
    }
  }
  // 遍历处理
  routers.forEach(router => iter(router))
  routers.sort(sorter)
}

// 获取有权限的菜单配置 map<path, menuInfo>
function getPath2MenuInfo(routers) {
  let map = {}
  if (routers && routers.length > 0) {
    let iter = (parentPath, parent, router) => {
      let url = router.url.replace(/:.*$/, '')
      let path = parentPath + (/^\//.test(url) ? url : '/' + url)
      path = path.replace(/\/+/, '/')
      if (!router.hidden) {
        map[path] = router
        map[router.url] = router
        // 子展示，那么父也要展示
        if (parentPath && parent) {
          map[parentPath] = parent
        }
      }
      if (router.children && router.children.length) {
        router.children.forEach(child => iter(path, router, child))
      }
      // 建立关系
      router.fullPath = path
      if (parent) {
        router.parent = parent
      }
    }
    routers.forEach(router => {
      iter('', null, router)
    })
  }
  return map
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter(route => {
    if (type && route.children) {
      route.children = filterChildren(route.children)
    }
    if (route.component) {
      // Layout ParentView 组件特殊处理
      if (route.component === 'Layout') {
        route.component = Layout
      } else if (route.component === 'ParentView') {
        route.component = ParentView
      } else if (route.component === 'InnerLink') {
        route.component = InnerLink
      } else {
        route.component = loadView(route.component)
      }
    }
    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type)
    } else {
      delete route['children']
      delete route['redirect']
    }
    return true
  })
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach((el, index) => {
    if (el.children && el.children.length) {
      if (el.component === 'ParentView' && !lastRouter) {
        el.children.forEach(c => {
          c.path = el.path + '/' + c.path
          if (c.children && c.children.length) {
            children = children.concat(filterChildren(c.children, c))
            return
          }
          children.push(c)
        })
        return
      }
    }
    if (lastRouter) {
      el.path = lastRouter.path + '/' + el.path
    }
    children = children.concat(el)
  })
  return children
}

export const loadView = (view) => {
  if (/^\/+/.test(view)) {
    view = view.replace(/^\/+/, '')
  }
  if (process.env.NODE_ENV === 'development') {
    return (resolve) => require([`@/views/${view}`], resolve)
  } else {
    // 使用 import 实现生产环境的路由懒加载
    return () => import(`@/views/${view}`)
  }
}

export default permission
