import Vue from 'vue'
import Router from 'vue-router'
/* Layout */
import Layout from '@/layout'
import InnerLink from '@/layout/components/InnerLink'
import ParentView from '@/components/ParentView'

import {applyAuthUrl, componentListLink} from '@/api/env'

Vue.use(Router)

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysHidden: true               // 当设置 true 的时候该路由 始终 不会再侧边栏出现 一般是由其他页面带参数跳转过来的页面 比如 /actComponentModul/cmptAttrConfig?actId=2099021001&componentId=1006&useIndex=1&componentTitle=xxx&actName=xxx
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * meta : {
 noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
 title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
 icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
 breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
 activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
 }
 */

// 公共路由
export const commonRouters = [
  {
    path: '/lxManager',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/lxManager',
        component: (resolve) => require(['@/views/menu/index'], resolve)
      }
    ]
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: (resolve) => require(['@/views/redirect'], resolve)
      }
    ]
  },
  {
    path: '/login',
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/register',
    component: (resolve) => require(['@/views/register'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true
  },
  // {
  //   path: '/applyPerms',
  //   hidden: true,
  //   component: (resolve) => require(['@/layout/components/InnerLink/index'], resolve),
  //   meta: {link: applyAuthUrl}
  // },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: (resolve) => require(['@/views/index'], resolve),
        name: 'Dashboard',
        meta: {title: '首页', icon: 'index', affix: true, noCache: true}
      }
    ]
  },
  {
    path: '/applyPerms',
    component: Layout,
    children: [
      {
        path: 'applyPerms',
        component: (resolve) => require(['@/layout/components/InnerLink/index'], resolve),
        name: 'ApplyPerms',
        meta: {title: '权限申请', icon: 'validCode', link: applyAuthUrl}
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'profile',
        component: (resolve) => require(['@/views/system/user/profile/index'], resolve),
        name: 'Profile',
        meta: {title: '个人中心', icon: 'user'}
      }
    ]
  },
  {
    path: '/system/user-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'role/:userId(\\d+)',
        component: (resolve) => require(['@/views/system/user/authRole'], resolve),
        name: 'AuthRole',
        meta: {title: '分配角色', activeMenu: '/system/user'}
      }
    ]
  },
  {
    path: '/system/role-auth',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'user/:roleId(\\d+)',
        component: (resolve) => require(['@/views/system/role/authUser'], resolve),
        name: 'AuthUser',
        meta: {title: '分配用户', activeMenu: '/system/role'}
      }
    ]
  },
  {
    path: '/system/dict-data',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index/:dictId(\\d+)',
        component: (resolve) => require(['@/views/system/dict/data'], resolve),
        name: 'Data',
        meta: {title: '字典数据', activeMenu: '/system/dict'}
      }
    ]
  },
  {
    path: '/system/oss-config',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/system/oss/config'], resolve),
        name: 'OssConfig',
        meta: {title: '配置管理', activeMenu: '/system/oss'}
      }
    ]
  },
  {
    path: '/tool/gen-edit',
    component: Layout,
    hidden: true,
    children: [
      {
        path: 'index',
        component: (resolve) => require(['@/views/tool/gen/editTable'], resolve),
        name: 'GenEdit',
        meta: {title: '修改生成配置', activeMenu: '/tool/gen'}
      }
    ]
  }
]

// 业务路由
export const businessRoutes = [
  {
    name: "ActFastConfig",
    path: "/actFastConfig",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "活动快配",
      icon: "star",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ActList",
        path: "actList",
        hidden: true,
        componentPath: '/activity/actFastConfig/index',
        //component: (resolve) => require(['@/views/activity/actFastConfig/index'], resolve),
        meta: {
          title: "活动列表",
          icon: "button",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActDeploy",
        path: "actDeploy",
        hidden: true,
        componentPath: '/activity/actFastConfigDeploy/index',
        meta: {
          title: "活动上线",
          icon: "upload",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActTemplateConfig",
        path: "actTemplateConfig",
        hidden: true,
        alwaysHidden: true,
        componentPath: '/activity/actTemplateConfig/index',
        meta: {
          title: "模板配置",
          icon: "upload",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActTemplateTemplate",
        path: "actTemplateTemplate",
        hidden: true,
        alwaysHidden: true,
        componentPath: '/activity/actTemplateConfig/AttrTemplate',
        meta: {
          title: "模板模板",
          icon: "upload",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActTemplateDefine",
        path: "actTemplateDefine",
        hidden: true,
        alwaysHidden: true,
        componentPath: '/activity/actTemplateConfig/AttrDefine',
        meta: {
          title: "模板字段",
          icon: "upload",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "Currency",
    path: "/currency",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "虚拟货币",
      icon: "star",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "Currency_add",
        path: "currency_add",
        hidden: true,
        component: InnerLink,
        query: "{}",
        meta: {
          title: "增加货币",
          icon: "dict",
          noCache: false,
          link: process.env.ENV === 'production' ? 'https://currency-zy.yy.com/currency/index.html' : 'https://currency-zy-test.yy.com/currency/index.html'
        }
      }
    ]
  },
  {
    name: "Monitor",
    path: "/monitor",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "系统监控",
      icon: "monitor",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "Online",
        path: "online",
        hidden: true,
        componentPath: '/monitor/online/index',
        meta: {
          title: "在线用户",
          icon: "online",
          noCache: false,
          link: null
        }
      },
      {
        name: "Druid",
        path: "druid",
        hidden: true,
        componentPath: '/monitor/druid/index',
        meta: {
          title: "数据监控",
          icon: "druid",
          noCache: false,
          link: null
        }
      },
      {
        name: "Cache",
        path: "cache",
        hidden: true,
        componentPath: '/monitor/cache/index',
        meta: {
          title: "缓存监控",
          icon: "redis",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "CommonTool",
    path: "/commonTool",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "通用工具",
      icon: "tree",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "PkGroupDraw",
        path: "pkGroupDraw",
        hidden: true,
        componentPath: '/activity/pkGroupDraw/index',
        meta: {
          title: "抽签工具",
          icon: "rate",
          noCache: false,
          link: null
        }
      },
      {
        name: "RoleChange",
        path: "roleChange",
        hidden: true,
        componentPath: '/activity/roleChange/index',
        meta: {
          title: "新分组工具",
          icon: "peoples",
          noCache: false,
          link: null
        }
      },
      {
        name: "RankData",
        path: "rankData",
        hidden: true,
        componentPath: '/activity/rankData/index',
        meta: {
          title: "导出榜单",
          icon: "download",
          noCache: false,
          link: null
        }
      },
      {
        name: "PkConfigV2",
        path: "pkConfigV2",
        hidden: true,
        componentPath: '/activity/pkConfigV2/index',
        meta: {
          title: "pk对阵调整",
          icon: "tree",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActConfigVersionMgr",
        path: "actConfigVersionMgr",
        hidden: true,
        componentPath: '/activity/actConfigVersion/index',
        meta: {
          title: "活动配置版本管理",
          icon: "table",
          noCache: false,
          link: null
        }
      },
      {
        name: "RankDataOp",
        path: "rankDataOp",
        hidden: true,
        componentPath: '/activity/rankDataOp/index',
        meta: {
          title: "榜单数据(开发版)",
          icon: "chart",
          noCache: false,
          link: null
        }
      },
      {
        name: "MethodLog",
        path: "methodLog",
        hidden: true,
        componentPath: '/activity/methodLog/index',
        meta: {
          title: "方法执行记录",
          icon: "log",
          noCache: false,
          link: null
        }
      },
      {
        name: 'RoleRemove',
        path: 'roleRemove',
        hidden: true,
        componentPath: '/activity/roleRemove/index',
        meta: {
          title: '角色移除',
          icon: 'delete',
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "TestTool",
    path: "/testTool",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "测试工具",
      icon: "bug",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ActTime",
        path: "actTime",
        hidden: true,
        componentPath: '/activity/actTime/index',
        meta: {
          title: "活动时间管理",
          icon: "time",
          noCache: false,
          link: null
        }
      },
      {
        name: "Toolkit",
        path: "toolkit",
        hidden: true,
        componentPath: '/activity/toolkit/index',
        meta: {
          title: "updateRanking小工具",
          icon: "upload",
          noCache: false,
          link: null
        }
      },
      {
        name: "Command",
        path: "command",
        hidden: true,
        componentPath: '/activity/command/index',
        meta: {
          title: "数据复位命令执行",
          icon: "documentation",
          noCache: false,
          link: null
        }
      },
      {
        name: "Cleanup",
        path: "cleanup",
        hidden: true,
        componentPath: '/activity/cleanup/index',
        meta: {
          title: "数据复位命令添加",
          icon: "nested",
          noCache: false,
          link: null
        }
      },
      {
        name: "Award",
        path: "award",
        hidden: true,
        componentPath: '/activity/award/index',
        meta: {
          title: "奖包验证",
          icon: "money",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "RankConfig",
    path: "/rankConfig",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "榜单配置",
      icon: "example",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "RankingConfig",
        path: "rankingConfig",
        hidden: true,
        component: InnerLink,
        query: "{}",
        meta: {
          title: "榜单配置",
          icon: "component",
          noCache: false,
          link: "https://webtest.yy.com/ge_brid/#/config"
        }
      }
    ]
  },
  {
    name: "AwardModul",
    path: "/awardModul",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "奖品模块",
      icon: "shopping",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "AwardConfig",
        path: "awardConfig",
        hidden: true,
        componentPath: '/activity/awardConfig/index',
        meta: {
          title: "抽发奖配置",
          icon: "form",
          noCache: false,
          link: null
        }
      },
      {
        name: "AwardIssueLog",
        path: "awardIssueLog",
        hidden: true,
        componentPath: '/activity/awardIssueLog/index',
        meta: {
          title: "抽发奖记录",
          icon: "log",
          noCache: false,
          link: null
        }
      },
      {
        name: "IssueJobDetail",
        path: "issueJobDetail",
        hidden: true,
        componentPath: '/activity/issueJobDetail/index',
        meta: {
          title: "补发记录",
          icon: "edit",
          noCache: false,
          link: null
        }
      },
      {
        name: "AwardPackage",
        path: "awardPackage",
        hidden: true,
        alwaysHidden: true,
        componentPath: '/activity/awardPackage/index',
        meta: {
          title: "奖包配置",
          icon: "build",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "ActComponentModul",
    path: "/actComponentModul",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "活动组件",
      icon: "component",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "CmptConfig",
        path: "cmptConfig",
        hidden: true,
        componentPath: '/activity/cmptConfig/index',
        meta: {
          title: "组件配置",
          icon: "component",
          noCache: false,
          link: null
        }
      },
      {
        name: "PkgComponent",
        path: "pkgComponent",
        hidden: true,
        componentPath: '/activity/pkgComponent/index',
        meta: {
          title: "组件属性测试",
          icon: "nested",
          noCache: false,
          link: null
        }
      },
      {
        name: "CmptAttrConfig",
        path: "cmptAttrConfig",
        hidden: true,
        alwaysHidden: true,
        componentPath: '/activity/cmptAttrConfig/index',
        meta: {
          title: "组件属性配置",
          icon: "#",
          noCache: false,
          link: null
        }
      },
      {
        name: "ComponentDocs",
        path: "componentDocs",
        hidden: true,
        component: InnerLink,
        query: "{}",
        meta: {
          title: "组件文档",
          icon: "component",
          noCache: false,
          link: componentListLink
          //link: window.location.protocol + '//' + window.location.hostname + '/ComponentList.html'
        }
      },
      {
        name: "ComponentDoc",
        path: "componentDoc",
        hidden: true,
        alwaysHidden: true,
        componentPath: '/activity/cmptDoc/index',
        meta: {
          title: "组件文档详情",
          icon: "nested",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "ActAttr",
    path: "/actAttr",
    hidden: true,
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "活动属性配置",
      icon: "example",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ActAttrConfig",
        path: "actAttrList",
        hidden: false,
        componentPath: '/activity/actAttrConfig/index',
        meta: {
          title: "活动属性列表",
          icon: "date-range",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActResource",
        path: "actResource",
        hidden: false,
        componentPath: '/activity/actResource/index',
        meta: {
          title: "活动资源列表",
          icon: "nested",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "RankTaskModul",
    path: "/rankTaskModul",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "榜单任务",
      icon: "chart",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "RankTaskConfig",
        path: "rankTaskConfig",
        hidden: true,
        componentPath: '/activity/rankTaskConfig/index',
        meta: {
          title: "榜单任务配置",
          icon: "date-range",
          noCache: false,
          link: null
        }
      },
      {
        name: "RankTaskAwardIssue",
        path: "rankTaskAwardIssue",
        hidden: true,
        componentPath: '/activity/rankTaskAwardIssue/index',
        meta: {
          title: "任务奖励查询",
          icon: "search",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "ActPendantModul",
    path: "/actPendantModul",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "活动挂件",
      icon: "tree-table",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ViewDefine",
        path: "viewDefine",
        hidden: true,
        componentPath: '/activity/guajian/index',
        meta: {
          title: "挂件tab",
          icon: "tab",
          noCache: false,
          link: null
        }
      },
      {
        name: "Rolerankmap",
        path: "rolerankmap",
        hidden: true,
        componentPath: '/activity/guajianRoleRankMap/index',
        meta: {
          title: "挂件成员配置",
          icon: "peoples",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "DataUpdate",
    path: "/dataUpdate",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "stream累榜上报",
      icon: "upload",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ReRule",
        path: "reRule",
        hidden: true,
        componentPath: '/activity/stream/index',
        meta: {
          title: "数据上报",
          icon: "upload",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "GreyClean",
    path: "/greyClean",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "活动上线",
      icon: "date-range",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ActConfigVersionDeploy",
        path: "actConfigVersionDeploy",
        hidden: true,
        componentPath: '/activity/actConfigVersion/index',
        meta: {
          title: "活动配置上线",
          icon: "upload",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActRedisDataClear",
        path: "actRedisDataClear",
        hidden: true,
        componentPath: '/activity/actRedisDataClear/index',
        meta: {
          title: "灰度数据清理(统一)",
          icon: "cascader",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActGreyWhiteList",
        path: "actGreyWhiteList",
        hidden: true,
        componentPath: '/activity/actGreyWhiteList/index',
        meta: {
          title: "灰度白名单",
          icon: "eye",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "DataCollect",
    path: "/dataCollect",
    hidden: true,
    component: Layout,
    meta: {
      title: "数据采集系统",
      icon: "example",
      noCache: false,
      link: null
    }
  },
  {
    name: "ActivityCheck",
    path: "/activityCheck",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "配置校验",
      icon: "validCode",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ActTimeCheck",
        path: "actTimeCheck",
        hidden: true,
        componentPath: '/activity/actTimeCheck/index',
        meta: {
          title: "时间校验",
          icon: "time",
          noCache: false,
          link: null
        }
      },
      {
        name: "ConfigCenter",
        path: "configCenter",
        hidden: true,
        componentPath: '/activity/configCenter/index',
        // component: (resolve) => require(['@/views/activity/configCenter/index'], resolve),
        meta: {
          title: "配置校验",
          icon: "setting",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "ActOffline",
    path: "/actOffline",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "活动离线",
      icon: "upload",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ActOfflineActList",
        path: "actOfflineActList",
        hidden: true,
        componentPath: '/activity/actOffline/index',
        meta: {
          title: "活动离线",
          icon: "date",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActOfflineOperation",
        path: "actOfflineOperation",
        hidden: true,
        componentPath: '/activity/actOfflineOperation/index',
        meta: {
          title: "离线操作",
          icon: "clipboard",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "Wzry",
    path: "/wzry",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "赏金赛",
      icon: "dashboard",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "GameList",
        path: "gameList",
        hidden: true,
        componentPath: '/activity/wzry/index',
        // component: (resolve) => require(['@/views/activity/wzry/index'], resolve),
        meta: {
          title: "王者赏金赛",
          icon: "cascader",
          noCache: false,
          link: null
        }
      },
      {
        name: 'AovAdjusting',
        path: 'aov/adjusting',
        hidden: true,
        component: InnerLink,
        meta: {
          title: '王者巅峰赛调整',
          icon: 'cascader',
          noCache: false,
          //link: process.env.ENV === 'production' ? 'https://hd-activity.yy.com/yo_2024_kingofglory_peak/pages/pc/backend_config_page.html' : 'https://hd-activity-test.yy.com/yo_2024_kingofglory_peak/pages/pc/backend_config_page.html'
          link: process.env.ENV === 'production' ? 'https://hd-activity.yy.com/astro/56/backendConfigPage?device=pc' : 'https://hd-activity-test.yy.com/astro/56/backendConfigPage?device=pc'
        }
      },
      {
        name: 'AovPhaseDetail',
        path: 'aovPhaseDetail',
        hidden: true,
        component: (resolve) => require(['@/views/activity/aov/index'], resolve),
        meta: {
          title: '王者巅峰赛',
          icon: 'cascader',
          noCache: false,
          link: null,
        }
      },
      {
        name: 'AovMatches',
        path: 'aovMatches',
        hidden: true,
        alwaysHidden: true,
        componentPath: '/activity/aov/match',
        meta: {
          title: '巅峰赛对局',
          icon: 'cascader',
          noCache: false,
          link: null
        }
      },      {
        name: "gameResult",
        path: "gameResult",
        hidden: true,
        component: (resolve) => require(['@/views/menu/index'], resolve),
        meta: {
          title: "王者赛果",
          icon: "form",
          noCache: false,
          link: null,
        },
        query: { tableKey: 'hdpt_admin.hdzk7_down.wzry_game', tableName: '赛事列表' }
      },
      {
        name: "game_tip_off_record",
        path: "game_tip_off_record",
        hidden: true,
        component: (resolve) => require(['@/views/menu/index'], resolve),
        meta: {
          title: "举报管理",
          icon: "form",
          noCache: false,
          link: null,
        },
        query: { tableKey: 'hdpt_admin.game_tip_off_record', tableName: '举报列表' }
      },
      {
        name: "hdzj_component_whitelist_2024057001",
        path: "hdzj_component_whitelist_2024057001",
        hidden: true,
        component: (resolve) => require(['@/views/menu/index'], resolve),
        meta: {
          title: "王者参赛黑名单",
          icon: "form",
          noCache: false,
          link: null,
        },
        query: { tableKey: 'hdpt_admin.hdzj_component_whitelist_2024057001', tableName: '黑名单列表' }
      }
    ]
  },
  {
    name: "RedisTool",
    path: "/redisTool",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "redis备份",
      icon: "redis",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "ActConfig",
        path: "actConfig",
        hidden: true,
        componentPath: '/activity/actConfig/index',
        meta: {
          title: "redis数据迁移",
          icon: "clipboard",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActRedisBackup",
        path: "actRedisBackup",
        hidden: true,
        componentPath: '/activity/actRedisBackup/index',
        meta: {
          title: "活动备份",
          icon: "clipboard",
          noCache: false,
          link: null
        }
      },
      {
        name: "ActRedisBackupRecord",
        path: "actRedisBackupRecord",
        hidden: true,
        componentPath: '/activity/actRedisBackupRecord/index',
        meta: {
          title: "活动备份记录",
          icon: "dict",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "System",
    path: "/system",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "系统管理",
      icon: "system",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "User",
        path: "user",
        hidden: true,
        componentPath: '/system/user/index',
        meta: {
          title: "用户管理",
          icon: "user",
          noCache: false,
          link: null
        }
      },
      {
        name: "Role",
        path: "role",
        hidden: true,
        componentPath: '/system/role/index',
        meta: {
          title: "角色管理",
          icon: "peoples",
          noCache: false,
          link: null
        }
      },
      {
        name: "Menu",
        path: "menu",
        hidden: true,
        componentPath: '/system/menu/index',
        meta: {
          title: "菜单管理",
          icon: "tree-table",
          noCache: false,
          link: null
        }
      },
      {
        name: "Dept",
        path: "dept",
        hidden: true,
        componentPath: '/system/dept/index',
        meta: {
          title: "部门管理",
          icon: "tree",
          noCache: false,
          link: null
        }
      },
      {
        name: "Post",
        path: "post",
        hidden: true,
        componentPath: '/system/post/index',
        meta: {
          title: "岗位管理",
          icon: "post",
          noCache: false,
          link: null
        }
      },
      {
        name: "Dict",
        path: "dict",
        hidden: true,
        componentPath: '/system/dict/index',
        meta: {
          title: "字典管理",
          icon: "dict",
          noCache: false,
          link: null
        }
      },
      {
        name: "Config",
        path: "config",
        hidden: true,
        componentPath: '/system/config/index',
        meta: {
          title: "参数设置",
          icon: "edit",
          noCache: false,
          link: null
        }
      },
      {
        name: "Notice",
        path: "notice",
        hidden: true,
        componentPath: '/system/notice/index',
        meta: {
          title: "通知公告",
          icon: "message",
          noCache: false,
          link: null
        }
      },
      {
        name: "Log",
        path: "log",
        hidden: true,
        redirect: "noRedirect",
        componentPath: '/monitor/log/index',
        component: ParentView,
        alwaysShow: true,
        meta: {
          title: "日志管理",
          icon: "log",
          noCache: false,
          link: null
        },
        children: [
          {
            name: "Operlog",
            path: "operlog",
            hidden: true,
            componentPath: '/monitor/operlog/index',
            meta: {
              title: "操作日志",
              icon: "form",
              noCache: false,
              link: null
            }
          },
          {
            name: "Logininfor",
            path: "logininfor",
            hidden: true,
            componentPath: '/monitor/logininfor/index',
            meta: {
              title: "登录日志",
              icon: "logininfor",
              noCache: false,
              link: null
            }
          }
        ]
      },
      {
        name: "Oss",
        path: "oss",
        hidden: true,
        componentPath: '/system/oss/index',
        meta: {
          title: "文件管理",
          icon: "upload",
          noCache: false,
          link: null
        }
      }
    ]
  },
  {
    name: "Tool",
    path: "/tool",
    hidden: true,
    redirect: "noRedirect",
    component: Layout,
    alwaysShow: true,
    meta: {
      title: "系统工具",
      icon: "tool",
      noCache: false,
      link: null
    },
    children: [
      {
        name: "Build",
        path: "build",
        hidden: true,
        componentPath: '/tool/build/index',
        meta: {
          title: "表单构建",
          icon: "build",
          noCache: false,
          link: null
        }
      },
      {
        name: "Gen",
        path: "gen",
        hidden: true,
        componentPath: '/tool/gen/index',
        meta: {
          title: "代码生成",
          icon: "code",
          noCache: false,
          link: null
        }
      },
      {
        name: "Swagger",
        path: "swagger",
        hidden: true,
        componentPath: '/tool/swagger/index',
        meta: {
          title: "系统接口",
          icon: "swagger",
          noCache: false,
          link: null
        }
      }
    ]
  }
]

// 所有业务路由
export const constantRoutes = commonRouters.concat(businessRoutes)

export default new Router({
  base: process.env.VUE_APP_CONTEXT_PATH,
  mode: 'history', // 去掉url中的#
  scrollBehavior: () => ({y: 0}),
  routes: commonRouters
})
