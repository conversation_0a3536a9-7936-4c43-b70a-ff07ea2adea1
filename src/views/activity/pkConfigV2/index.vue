<template>
  <div class="app-container">
    <div>
      <Form :model="queryForm" inline>

        <FormItem label="活动ID" prop="actId"
                  style="width: 300px"
                  :rules="{required: true, type:'number',trigger: 'blur'}">
          <Input inline v-model="queryForm.actId" placeholder="ActivityId" required style="width: 200px"
                 @on-blur="pkConfigs()" number
          />
        </FormItem>
        <FormItem>
          <Select v-model="selectedConfig" style="width:200px">
            <Option v-for="(item,index) in configList" :value="index" :key="item.title">[{{index}}]{{ item.title }}</Option>
          </Select>
          <Button type="primary" @click="pkMembers()">重置</Button>
        </FormItem>
        <span>交约宝年度正赛:2021103002 陪玩:2021114001</span>
      </Form>

      <Row style="background:#eee;padding:20px;width: 100%;height: 700px;overflow: scroll;flex-direction: column;align-items: center;" class="pk-list">
        <Row :style="{display: 'flex',justifyContent: 'center',width: items.length * 90 + 'px'}" v-for="(colItem, colIndex) in rowItems" :key="colIndex">
          <Col v-for="(item,index) in items" :key="index">
            <div :style="{width: '170px', flex: '0 0 auto', padding: '5px ' +  ([5,5,90,260,600][colIndex] + 'px'), boxSizing: 'content-box'}" v-if="(colIndex === 0 && index%2 === 0) || (colIndex !== 0 && index%(Math.pow(2, colIndex)) === 0)">
              <template v-if="colIndex === 0">
                <Card :bordered="false">
                  <p slot="title" style="width: 20px;height: 15px;overflow: hidden;">
                    <span>{{index2Group[index]}}</span>
                    <!-- v-if="item.logo"><img :src="item.logo" class="image" width="100%">--></p>

                  <div
                    draggable="true"
                    @dragstart="handleDragStart($event, index)"
                    @dragover.prevent="handleDragOver($event, index)"
                    @dragenter="handleDragEnter($event, index)"
                    @dragend="handleDragEnd($event, index)">

                    <p v-if="item.nick" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">昵称:<span :title="item.nick">{{item.nick}}</span></p>
                    <p v-if="item.name" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">昵称:<span :title="item.name">{{item.name}}</span></p>


                    <!-- <p style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis"> <span  :title="item.nick" >昵称: {{item.nick}}</span></p>-->
                    <p>排名:<span>{{item.rank}}</span></p>
                    <p v-if="item.asid" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">公会短号:<span :title="item.asid">{{item.asid}}</span></p>
                    <p v-if="item.contractAsid" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">签约公会:<span :title="item.contractAsid">{{item.contractAsid}}</span></p>

                  </div>
                </Card>
                <p style="text-align: center;font-weight: bold;">VS</p>
                <Card
                  :bordered="false">
                  <p slot="title" style="width: 20px;height: 15px;overflow: hidden;">
                    <span>{{index2Group[index+1]}}</span></p>
                  <div
                    draggable="true"
                    @dragstart="handleDragStart($event, index+1)"
                    @dragover.prevent="handleDragOver($event, index+1)"
                    @dragenter="handleDragEnter($event, index+1)"
                    @dragend="handleDragEnd($event, index+1)">
                    <!--<p style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis"> <span  :title="item.nick" >昵称: {{item.nick}}</span></p>-->
                    <p v-if="items[index+1].nick" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">昵称:<span :title="items[index+1].nick">{{items[index+1].nick}}</span></p>
                    <p v-if="items[index+1].name" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">昵称:<span :title="items[index+1].name">{{items[index+1].name}}</span></p>


                    <p>排名:<span>{{items[index+1].rank}}</span></p>
                    <p v-if="items[index+1].asid" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">
                      公会短号:<span :title="items[index+1].asid">{{items[index+1].asid}}</span></p>
                    <p v-if="items[index+1].contractAsid" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">
                      签约公会:<span :title="items[index+1].contractAsid">{{items[index+1].contractAsid}}</span></p>

                  </div>
                </Card>
              </template>
              <template v-else><!--v-else-if="isShowTree"-->
                <Card :bordered="false" v-for="(newItem, newIndex) in [items.slice(index, index + Math.pow(2, colIndex)).sort((a, b) => a.rank-b.rank)[0]]" :key="newIndex"
                      style="background-color: #9ba7b5">
                  <p slot="title" style="width: 40px;height: 40px;overflow: hidden;"  v-if="newItem.logo"><img :src="newItem.logo" class="image" width="100%"></p>

                  <!--<p style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis"> <span  :title="item.nick" >昵称: {{item.nick}}</span></p>-->
                  <p v-if="newItem.nick" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">昵称:<span :title="newItem.nick">{{newItem.nick}}</span></p>
                  <p v-if="newItem.name" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">昵称:<span :title="newItem.name">{{newItem.name}}</span></p>

                  <p>排名:<span>{{newItem.rank}}</span></p>
                  <p v-if="newItem.asid" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">公会短号:<span :title="newItem.asid">{{newItem.asid}}</span></p>
                  <p v-if="newItem.contractAsid" style="width:10rem;white-space: nowrap;overflow: hidden;text-overflow: ellipsis">签约公会:<span :title="newItem.contractAsid">{{newItem.contractAsid}}</span></p>

                </Card>
              </template>
            </div>
          </Col>
        </Row>
      </Row>

      <Button type="primary" @click="setupPKConfig()">确定调整</Button>


    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/pkConfigV2'
  export default {
    name: "PkConfigV2",
    components: {},
    data: function () {
      return {
        queryForm: {},
        configList: [],
        selectedConfig: null,
        items: [],
        dragging: null,
        targetDrag: null,
        index2Group: {
          0: 'A',
          1: 'E',
          2: 'B', 3: 'F', 4: 'C', 5: 'G', 6: 'D',
          7: 'H',
          8: 'I',
          9: 'M',
          10: 'J',
          11: 'N',
          12: 'K',
          13: 'O',
          14: 'L',
          15: 'P'
        },
        isShowTree: false
      }
    },
    computed: {
      rowItems() {
        const arr = [0, 1, 2, 3, 4, 5]
        return arr.filter((colItem, colIndex) => {
          return colIndex === 0 || (this.items.length/Math.pow(2, colIndex))%2 === 0 || (this.items.length/Math.pow(2, colIndex))%2 === 1
        })
      }
    },
    mounted() {
      // setTimeout(() => {
      //   this.items = [
      //     {
      //       addition: 0,
      //       asid: 500040000,
      //       avatarInfo: "http://dl.g.yy.com/channel.png",
      //       base: 0,
      //       itemDesc: "",
      //       key: "500040000",
      //       name: "神秘工会",
      //       rank: 1,
      //       sid: 500040000,
      //       value: 45500,
      //       viewExt: {},
      //       win: 0,
      //     },
      //     {
      //       addition: 0,
      //       asid: 87814665,
      //       avatarInfo:
      //         "http://downhdlogo.yy.com/applogo/session/60/60/81/0087814665/s87814665RhFE-8Ibe.jpg",
      //       base: 0,
      //       itemDesc: "",
      //       key: "87814665",
      //       name: "顶级频道今天压测，勿扰",
      //       rank: 2,
      //       sid: 87814665,
      //       value: 44020,
      //       viewExt: {},
      //       win: 0,
      //     },
      //     {
      //       addition: 0,
      //       asid: 500040023,
      //       avatarInfo: "http://dl.g.yy.com/channel.png",
      //       base: 0,
      //       itemDesc: "",
      //       key: "500040023",
      //       name: "神秘工会",
      //       rank: 3,
      //       sid: 500040023,
      //       value: 44020,
      //       viewExt: {},
      //       win: 0,
      //     },
      //     {
      //       addition: 0,
      //       asid: 500040022,
      //       avatarInfo: "http://dl.g.yy.com/channel.png",
      //       base: 0,
      //       itemDesc: "",
      //       key: "500040022",
      //       name: "神秘工会",
      //       rank: 4,
      //       sid: 500040022,
      //       value: 43280,
      //       viewExt: {},
      //       win: 0,
      //     },
      //     {
      //       addition: 0,
      //       asid: 500040021,
      //       avatarInfo: "http://dl.g.yy.com/channel.png",
      //       base: 0,
      //       itemDesc: "",
      //       key: "500040021",
      //       name: "神秘工会",
      //       rank: 5,
      //       sid: 500040021,
      //       value: 42540,
      //       viewExt: {},
      //       win: 0,
      //     },
      //     {
      //       addition: 0,
      //       asid: 500040020,
      //       avatarInfo: "http://dl.g.yy.com/channel.png",
      //       base: 0,
      //       itemDesc: "",
      //       key: "500040020",
      //       name: "神秘工会",
      //       rank: 6,
      //       sid: 500040020,
      //       value: 41800,
      //       viewExt: {},
      //       win: 0,
      //     },
      //     {
      //       addition: 0,
      //       asid: 500040019,
      //       avatarInfo: "http://dl.g.yy.com/channel.png",
      //       base: 0,
      //       itemDesc: "",
      //       key: "500040019",
      //       name: "神秘工会",
      //       rank: 7,
      //       sid: 500040019,
      //       value: 41060,
      //       viewExt: {},
      //       win: 0,
      //     },
      //     {
      //       addition: 0,
      //       asid: 500040018,
      //       avatarInfo: "http://dl.g.yy.com/channel.png",
      //       base: 0,
      //       itemDesc: "",
      //       key: "500040018",
      //       name: "神秘工会",
      //       rank: 8,
      //       sid: 500040018,
      //       value: 40320,
      //       viewExt: {},
      //       win: 0,
      //     },
      //   ]
      // }, 2000)
    },
    methods: {

      pkConfigs() {
        const self = this;

        let reqData = {"actId": self.queryForm.actId}
        Api.pkConfigs(reqData).then(res => {
          if (res.result == 0) {
            self.configList = res.data
          } else {
            self.$Message.warning(res.data);
          }
        })
      },

      pkMembers() {
        const self = this;

        let config = self.configList[self.selectedConfig].config;
        config = JSON.parse(config);
        console.log('pkMembers selectConfig->' + self.selectedConfig)
        debugger
        if (config.isTree) {
          this.isShowTree = true;
        }
        let reqData = {"actId": config.actId,"rankId": config.rankId,"phaseId": config.phaseId}
        Api.pkMembers(reqData).then(res => {
          if (res.result == 0) {
            this.items = res.data;
          } else {
            self.$Message.warning(res.data);
          }
        })
      },

      setupPKConfig() {
        const self = this;
        let config = self.configList[self.selectedConfig].config;
        console.log('setupPKConfig select->' + config)
        //let reqData = config
        config = JSON.parse(config);
        config.ranks = self.items

       // let reqData = JSON.stringify(config);

        Api.setupPKConfig(config).then(res => {
          if (res.result == 0) {
            alert("调整成功!!!" + res.data);
          } else {
            alert("调整失败!!!!"+res.data);
          }
        })
      },

      handleDragStart(e, index) {
        this.dragging = index;
      },

      handleDragEnd(e, index) {
        // const newItems = [...this.items];
        // console.log(JSON.stringify(newItems));
        // const src = newItems.indexOf(this.dragging);
        // const dst = newItems.indexOf(this.targetDrag);
        // //newItems.splice(dst, 0, ...newItems.splice(src, 1))

        // this.$set(newItems, src, this.targetDrag);
        // this.$set(newItems, dst, this.dragging);
        // this.items = newItems;
        // console.log(JSON.stringify(newItems));
        if (this.dragging !== null && this.targetDrag !== null) {
          const localItem = this.items[this.dragging]
          this.$set(this.items, this.dragging, this.items[this.targetDrag]);
          this.$set(this.items, this.targetDrag, localItem);
        } else {
          this.$Message.error('你挪出界了');
        }
        this.dragging = null;
        this.targetDrag = null;
      },

      //首先把div变成可以放置的元素，即重写dragenter/dragover
      handleDragOver(e) {
        e.dataTransfer.dropEffect = "move"; // e.dataTransfer.dropEffect="move";//在dragenter中针对放置目标来设置!
      },
      handleDragEnter(e, index) {
        //debugger
        e.dataTransfer.effectAllowed = "move"; //为需要移动的元素设置dragstart事件
        if (+index === +this.dragging) {
          return;
        }
        this.targetDrag = index;
        /*const newItems = [...this.items]
        console.log(newItems)
        const src = newItems.indexOf(this.dragging)
        const dst = newItems.indexOf(item)

        newItems.splice(dst, 0, ...newItems.splice(src, 1))

        this.items = newItems*/
      },


    }

  };
</script>
