<template>
  <div class="app-container">
    <div>
      <Form inline :model="formItem">
        <FormItem>
          <span>活动ID：</span>
          <Select v-model="formItem.actId" style="width:350px">
            <Option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                    :label='item.act_name+"("+item.act_id+")"'></Option>
          </Select>
        </FormItem>


        <Button :loading="loadingState" type="primary" @click="queryViewDefine()"> 查询
        </Button>
        <Button :loading="loadingState" type="primary" @click="addTask()"> 新建
        </Button>
        <Button type="primary" @click="copyConfig()"> 复制</Button>
      </Form>
      <br>

      <Modal :value="copyLayerState" width="600" :closable="false" :mask-closable="false">
        <span>该复制仅限于相关榜单结构的活动</span>
        <Form :label-width="100" :model="copyFormItem" ref="copyFormItem">
          <FormItem label="原活动ID" prop="srcActId">
            <Select v-model="copyFormItem.srcActId" style="width:300px">
              <Option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                      :label='item.act_name+"("+item.act_id+")"'></Option>
            </Select>
          </FormItem>
          <FormItem label="目标活动ID" prop="targetActId">
            <Select v-model="copyFormItem.targetActId" style="width:300px">
              <Option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                      :label='item.act_name+"("+item.act_id+")"'></Option>
            </Select>
          </FormItem>
          <span>若存在多个不同的赛程,复制后需要逐个进行展示时间的修改</span>
          <FormItem label="开始展示时间" prop="startShowTime">

            <DatePicker type="datetime" format="yyyy-MM-dd HH:mm:ss"
                        v-model="copyFormItem.startShowTime" style="width:300px"></DatePicker>

          </FormItem>
          <FormItem label="结束展示时间" prop="endShowTime">

            <DatePicker type="datetime" format="yyyy-MM-dd HH:mm:ss"
                        v-model="copyFormItem.endShowTime" style="width:300px"></DatePicker>
          </FormItem>

        </Form>
        <div slot="footer">
          <Button :loading="loadingCopyState" type="primary" @click="loadingCopyState=true; copyViewDefine()"> 确认复制
          </Button>
          <Button @click="closeCopyModal()"> 取消</Button>
        </div>
      </Modal>


      <Modal :value="addLayerState" width="600" :closable="false" :mask-closable="false">
        <p slot="header" style="text-align:center">
          <span v-if="opt_type===1">添加挂件定义</span>
          <span v-if="opt_type===2">编辑挂件定义</span>
        </p>
        <Form :label-width="100" :model="editFormItem" ref="editFormItem" :rules="viewDefineValidate">
          <FormItem label="活动ID" prop="actId">
            <Input v-if="opt_type===2" disabled v-model="editFormItem.actId" style="width:200px"/>
            <Select v-else v-model="editFormItem.actId" style="width:300px">
              <Option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                      :label='item.act_name+"("+item.act_id+")"'></Option>
            </Select>
          </FormItem>
          <FormItem label="挂件tab" prop="itemTypeKey">
            <Select v-if="opt_type===2" disabled v-model="editFormItem.itemTypeKey" style="width:300px">
              <Option value="anchor">主播</Option>
              <Option value="channel">公会</Option>
              <Option value="hall">厅</Option>
              <Option value="room">语音房房间</Option>
              <Option value="anchorFamily">家族</Option>
              <Option value="contribute">贡献</Option>
              <Option value="anchor2">主播分赛场1</Option>
              <Option value="anchor3">主播分赛场2</Option>
              <Option value="anchor4">主播分赛场3</Option>
              <Option value="anchor5">主播战队赛</Option>
              <Option value="peiwan_tuan">大神团</Option>
              <Option value="pw_peipei">大神陪陪</Option>
              <Option value="hall2">多厅</Option>
              <Option value="cp_top1">CP</Option>
              <Option value="cp_top1_2">CP1</Option>
              <Option value="channel2">公会分赛场1</Option>
              <Option value="channel3">公会分赛场2</Option>
            </Select>
            <Select v-else v-model="editFormItem.itemTypeKey" style="width:300px">
              <Option value="anchor">主播</Option>
              <Option value="channel">公会</Option>
              <Option value="hall">厅</Option>
              <Option value="room">语音房房间</Option>
              <Option value="anchorFamily">家族</Option>
              <Option value="contribute">贡献</Option>
              <Option value="anchor2">主播分赛场1</Option>
              <Option value="anchor3">主播分赛场2</Option>
              <Option value="anchor4">主播分赛场3</Option>
              <Option value="anchor5">主播战队赛</Option>
              <Option value="peiwan_tuan">大神团</Option>
              <Option value="pw_peipei">大神陪陪</Option>
              <Option value="hall2">多厅</Option>
              <Option value="cp_top1">CP</Option>
              <Option value="cp_top1_2">CP1</Option>
              <Option value="channel2">公会分赛场1</Option>
              <Option value="channel3">公会分赛场2</Option>
            </Select>
          </FormItem>
          <FormItem label="挂件名称">
            <Input placeholder="挂件名称" v-model="editFormItem.remark" style="width:300px"/>
          </FormItem>

          <FormItem label="角色类型" prop="roleType">
            <Select v-model="editFormItem.roleType" style="width:300px">
              <Option value=200>主播</Option>
              <Option value=400>公会</Option>
              <Option value=401>厅</Option>
              <Option value=402>大神团</Option>
              <Option value=700>语音房家族</Option>
            </Select>

          </FormItem>

          <FormItem label="角色ID">
            <Input placeholder="可为空" v-model="editFormItem.roleId" style="width:300px"/>
          </FormItem>

          <FormItem label="业务" prop="busiIds">
            <Select v-model="editFormItem.busiIds" style="width:300px">
              <Option value=400>游戏宝贝</Option>
              <Option value=500>交友</Option>
              <Option value=810>技能卡</Option>
            </Select>
          </FormItem>

          <FormItem label="显示topN">

            <Input placeholder="仅大神活动使用" v-model="editFormItem.showTopN" style="width:300px"/>
          </FormItem>

          <FormItem label="排序">

            <Input type="number" v-model="editFormItem.sort" style="width:300px"/>
          </FormItem>

          <FormItem label="状态" prop="status">
            <Select v-model="editFormItem.status" style="width:300px">
              <Option value="1">可用</Option>
              <Option value="0">不可用</Option>
            </Select>
          </FormItem>


          <FormItem label="展示开始时间">

            <Date-picker format="yyyy-MM-dd HH:mm:ss" type="datetime" :value="editFormItem.startShowTime"
                         @on-change="editFormItem.startShowTime=$event"
                         placeholder="展示开始时间" style="width: 300px"></Date-picker>

          </FormItem>
          <FormItem label="展示结束时间">
            <Date-picker format="yyyy-MM-dd HH:mm:ss" type="datetime" :value="editFormItem.endShowTime"
                         @on-change="editFormItem.endShowTime=$event"
                         placeholder="展示结束时间" style="width: 300px"></Date-picker>
          </FormItem>

        </Form>
        <div slot="footer">
          <Button :loading="loadingState" type="primary" @click="loadingState=true;addViewDefine('editFormItem')"
                  v-if="opt_type==1">
            确认添加
          </Button>
          <Button :loading="loadingState" type="primary"
                  @click="loadingState=true;updateViewDefineWithValid('editFormItem')"
                  v-if="opt_type==2">
            确认修改
          </Button>
          <Button @click="closeModal()">
            取消
          </Button>
        </div>
      </Modal>


      <Table :columns="columns" :data="viewDefineList"></Table>
      <br>

    </div>


  </div>
</template>

<script>
import Api from '@/api/activity/viewDefine'
import ActConfigVersion from '@/api/activity/actConfigVersion'

export default {
  name: "ViewDefine",
  components: {},
  data: function () {
    return {
      selectActList: [],
      viewDefineList: [],
      columns: [{
        title: '活动ID',
        width: 120,
        key: 'actId'
      }, {
        title: '挂件tab',
        width: 200,
        key: 'itemTypeKey',
        render: (h, params) => {
          let content
          const itemTypeKey = params.row.itemTypeKey
          switch (itemTypeKey) {
            case 'anchor':
              content = itemTypeKey + '(主播)'
              break
            case 'channel':
              content = itemTypeKey + '(公会)'
              break
            case 'hall':
              content = itemTypeKey + '(厅)'
              break
            case 'room':
              content = itemTypeKey + '(房间)'
              break
            case 'anchorFamily':
              content = itemTypeKey + '(家族)'
              break
            case 'anchor2':
              content = itemTypeKey + '(主播分赛场1)'
              break
            case 'anchor3':
              content = itemTypeKey + '(主播分赛场2)'
              break
            case 'anchor4':
              content = itemTypeKey + '(主播分赛场3)'
              break
            case 'anchor5':
              content = itemTypeKey + '(主播战队赛)'
              break
            case 'peiwan_tuan':
              content = itemTypeKey + '(大神团)'
              break
            case 'pw_peipei':
              content = itemTypeKey + '(大神陪陪)'
              break
            case 'hall2':
              content = itemTypeKey + '(多厅)'
              break
            case 'cp_top1':
              content = itemTypeKey + '(CP)'
              break
            case 'cp_top1_2':
              content = itemTypeKey + '(CP1)'
              break
            case 'channel2':
              content = itemTypeKey + '(公会分赛场1)'
              break
            case 'channel3':
              content = itemTypeKey + '(公会分赛场2)'
              break
            case 'contribute':
              content = itemTypeKey + '(贡献)'
              break
          }

          return h('p', content)
        }
      }, {
        title: '挂件名称',
        width: 100,
        key: 'remark',
      }, {
        title: '角色类型',
        width: 120,
        key: 'roleType',
        render: (h, params) => {
          let content
          switch (params.row.roleType) {
            case 400:
              content = '公会'
              break
            case 200:
              content = '主播'
              break
            case 401:
              content = '厅'
              break
            case 402:
              content = '团'
              break
            case 404:
              content = '语音房房间'
              break
            case 700:
              content = "语音房家族"
              break
          }
          return h('p', content)
        }
      }, {
        title: '角色ID',
        width: 100,
        key: 'roleId'
      }, {
        title: '业务ID',
        width: 100,
        key: 'busiIds'
      }, {
        title: '排序',
        width: 80,
        key: 'sort'
      }, {
        title: '展示topN',
        width: 100,
        key: 'showTopN',

      }, {
        title: '状态',
        width: 80,
        key: 'status',
        render: (h, params) => {
          let content = '不可用'
          if (params.row.status === 1) {
            content = '可用';
          }
          return h('p', content)
        }
      }, {
        title: '展示开始时间',
        width: 220,
        key: 'startShowTime',
        render: (h, params) => {
          let content = '';
          if (params.row.startShowTime) {
            content = new Date(params.row.startShowTime).toLocaleString()
          }

          return h('p', content);
        }
      }, {
        title: '展示结束时间',
        width: 220,
        key: 'endShowTime',
        render: (h, params) => {
          let content = '';
          if (params.row.endShowTime) {
            content = new Date(params.row.endShowTime).toLocaleString()
          }
          return h('p', content)
        }
      }, {
        title: '操作',
        align: 'center',
        width: 180,
        render: (h, params) => {
          let update = h('Button', {
            style: {
              marginLeft: '5px'
            },
            props: {
              type: 'primary'
            },
            on: {
              click: () => {
                this.editItem(params.index)
              }
            }
          }, '编辑');
          return h('p', [update])
        }
      }],


      formItem: {},
      editFormItem: {},

      loadingState: false,
      loadingStatus: false,
      opt_type: 1,
      addLayerState: false,
      viewDefineValidate: {
        // actId: [{required: true, message: '不可为空', trigger: 'blur'}],
        // itemTypeKey: [{required: true, message: '不可为空', trigger: 'blur'}],
        // roleType: [{required: true, message: '不可为空', trigger: 'blur'}],
        // busiIds: [{required: true, message: '不可为空', trigger: 'blur'}],
        // status: [{required: true, message: '不可为空', trigger: 'blur'}]
      },
      copyFormItem: {},
      copyLayerState: false,
      loadingCopyState: false,
    }

  },
  created: function () {
    this.loadActInfos()
  },
  methods: {
    //加载活动数据
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    queryViewDefine() {
      const self = this;
      let formItem = this.formItem
      let reqData = formItem
      self.loadingState = true;
      Api.queryViewDefine(reqData).then(res => {
        if (res.result === '0') {
          this.viewDefineList = res.data
        } else {
          self.$Message.warning("查询失败:" + res.data)
        }
      })
      self.loadingState = false;
    },

    editItem: function (index) {
      const self = this
      let data = self.viewDefineList[index]
      data.startShowTime = new Date(data.startShowTime);
      data.endShowTime = new Date(data.endShowTime);
      data.status = data.status + '';
      data.roleType = data.roleType + '';
      self.setEditData(data)
    },
    // 父组件触发编辑操作，设置表单数据
    setEditData(data) {
      const self = this
      self.editFormItem = data
      self.opt_type = 2
      self.addLayerState = true
    },
    closeModal() {
      this.addLayerState = false
      this.loadingState = false
    },
    closeCopyModal() {
      this.copyLayerState = false
      this.loadingCopyState = false
    },
    addTask() {
      this.editFormItem = {};
      this.opt_type = 1;
      this.addLayerState = true;
    },

    copyConfig() {
      this.copyFormItem = {
        srcActId: this.formItem.actId,
        targetActId: ''
      };
      this.copyLayerState = true;
    },

    addViewDefine(name) {
      const self = this
      let reqData = self.editFormItem
      let flag = false;
      this.$refs[name].validate((valid) => {
        if (valid) {
          flag = true;
        }
      });
      if (!flag) {
        this.loadingState = false
        return;
      }

      //reqData = JSON.stringify(reqData);
      Api.addViewDefine(reqData).then(res => {
        if (res.result == '0') {
          self.$Message.success("添加成功:" + res.data);
          this.queryViewDefine()
        } else {
          alert(("添加失败:" + res.data));
        }
      });
      this.addLayerState = false
      this.loadingState = false
    },

    updateViewDefineWithValid(name) {
      console.log(111)
      this.updateViewDefine();
    },

    updateViewDefine() {
      const self = this
      let reqData = self.editFormItem;

      console.log('reqdata->' + JSON.stringify(reqData))
      Api.updateViewDefine(reqData).then(res => {
        if (res.result === '0') {
          self.$Message.success("修改成功:" + res.data);
          this.formItem.actId = this.editFormItem.actId
          this.editFormItem = {};
          this.queryViewDefine()
        } else {
          alert(("修改失败:" + res.data));
        }
      });
      this.addLayerState = false
      this.loadingState = false
    },

    copyViewDefine() {
      const self = this
      let reqData = self.copyFormItem;

      //reqData = JSON.stringify(reqData);
      console.log('reqdata->' + JSON.stringify(reqData))
      Api.copyViewDefine(reqData).then(res => {
        if (res.result === '0') {
          self.$Message.success("修改成功:" + res.data);
          this.formItem.actId = self.copyFormItem.targetActId;
          this.queryViewDefine();
        } else {
          alert(("修改失败:" + res.data));
        }
      });

      this.copyLayerState = false
      this.loadingCopyState = false

    }

  }
};
</script>
