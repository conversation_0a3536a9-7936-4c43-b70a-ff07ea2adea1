<template>
  <div class="app-container">
    <div>

      <p>
        <br>
        成员ID规则: 主播->uid , 公会->sid , 厅->sid_ssid 厅ID体系->tid
        <br>
      </p>
      <div>
        <Form :model="formItem">
          <FormItem label="活动ID" prop="actId">
            <Input v-model="formItem.actId" placeholder="ActivityId" required style="width: 200px"/>
          </FormItem>

          <FormItem label="角色类型" prop="roleType">
            <RadioGroup v-model="formItem.roleType">
              <Radio label="200">主播</Radio>
              <Radio label="400">公会</Radio>
              <Radio label="401">厅(sid_ssid)</Radio>
              <Radio label="202">厅管</Radio>
              <Radio label="700">家族</Radio>
            </RadioGroup>
          </FormItem>

          <FormItem label="成员ID" prop="memberId">
            <!--<Input v-model="formItem.memberId" placeholder="MemberId" required style="width: 200px"/>-->
            <Input v-model="inputMemberId" placeholder="MemberId" required style="width: 200px"/>
            <Button :loading="calculateLoadingStatus" type="primary" @click="queryUserRole()">查询</Button>
          </FormItem>

        </Form>
      </div>
      <div>
        当前角色为: {{srcRole}}
        可调整的角色为：
        <Select v-model="targetRole" clearable style="width:200px">
          <Option v-for="item in canSelectRoles" :value="item" :key="item">{{ item }}</Option>
        </Select>
        <Button :loading="loadingStatus" type="primary" @click="calculateRoleChange()">确定</Button>
        <i-switch v-model="forceExe" @on-change="change" />强制执行(仅限开发人员使用)
        <br>
        角色说明:<br>
        角色ID : 角色名
        <div style="border: 1px" v-for="(value,key) in roleMap" :key="key" >{{key}} :  {{value}}</div>
        <!--
            <br>主播:<br> 60001->约战主播 <br> 40011->宝贝视频主播 40012->宝贝声音主播 <br>
            50011->交友女视频 50012->交友女声音 50014->交友天团 50017->交友男视频 50018->交友男声音
            <br> 公会:<br>
            40004->宝贝公会 <br> 50004->交友公会 <br> 60004->约战公会 <br>-->


        <br>
        <hr/>
        工具配置说明: <br> 1.在hdzt_manager.rank_adjust表进行配置 <br>
        示例:{"rankContributeMap":{1:1001,2:1002,3:1003,201:1201,301:1301,202:1202,302:1302,21:1021,22:1022,31:1031,32:1032,33:1033,34:1034,44:1044,45:1045,46:1046,61:1061,62:1062,63:1063},
        "rankGroup":[[21,22,23],[31,32,33,34],[44,45,46],[61,62,63]],"roleGroup":[[40011,40012,60001,50011,50012,50014,50017,50018],[40004,50004,60004]]}
        <br>
        说明: rankContributeMap: 主榜和贡献榜映射 <br>
        rankGroup: 榜单分组,当原角色和目标角色都在该组,则生成迁移命令 <br>
        roleGroup: 角色分组,规定哪些角色可以相互转换 <br>
        2.当榜单产生晋级后,原角色和目标角色必须在同一榜单(该规则程序自动检查) <br>
        <span style="color: red">3.贡献榜不需要分组</span><br>
        4.榜单配置需和角色一对一对应,如宝贝公会转换成交友公会,交友公会榜单不可缺失,<span style="color: red">此规则需人工确认</span>
      </div>

      <Modal width="800" :closable="false" :mask-closable="false" v-model="showCalculateResult">

        角色说明:
        角色ID : 角色名
        <!--<div style="border: 1px" v-for="(value,key) in roleMap" :key="key">{{key}} :  {{value}}</div>-->
        <div style="border: 1px" v-for="(value,key) in roleMap" :key="key" v-if="key == srcRole || key == targetRole">{{key}} :  {{value}}</div>
        <!-- {{roleMap.adjustFromItem.srcRoleId}}-->
        <!--
        <br>主播:<br> 60001->约战主播 <br> 40011->宝贝视频主播 40012->宝贝声音主播 <br>
        [50011->交友女视频] &nbsp;&nbsp; [50012->交友女声音] &nbsp;&nbsp; [50014->交友天团] &nbsp;&nbsp; [50017->交友男视频] &nbsp;&nbsp; [50018->交友男声音]
        <br> 公会:<br>
        40004->宝贝公会 <br> 50004->交友公会 <br> 60004->约战公会 <br>-->
        <Form :model="adjustFromItem">
          <FormItem label="活动ID" prop="actId">
            <Input v-model="adjustFromItem.actId" placeholder="actId" required style="width: 300px"/>
          </FormItem>
          <FormItem label="成员ID" prop="memberId">
            <Input v-model="adjustFromItem.memberId" placeholder="memberId" required style="width: 300px"/>
          </FormItem>
          <FormItem label="原角色" prop="srcRoleId">
            <Input v-model="adjustFromItem.srcRoleId" placeholder="srcRoleId" required style="width: 300px"/>
          </FormItem>
          <FormItem label="目标角色" prop="targetRoleId">
            <Input v-model="adjustFromItem.targetRoleId" placeholder="targetRoleId" required style="width: 300px"/>
          </FormItem>
          <FormItem label="榜单迁移情况" prop="rankMoveList" style="width: 750px;">
            <Input v-model="adjustFromItem.rankMoveList" type="textarea" placeholder="rankMoveList" :rows="10"/>
          </FormItem>
        </Form>

        <div slot="footer">
          <span style="color: red">请截图让开发确认是否有问题!!!!</span>
          <Button :loading="executeLoadingState" type="primary" @click="executeLoadingState=true;executeChangeRole()">
            确认调整
          </Button>
          <Button @click="closeModal()">
            取消
          </Button>
        </div>
      </Modal>

      <Modal width="800" :closable="false" :mask-closable="false" v-model="showExeResult">
        <span style="color: red">榜单迁移结果,若有失败的榜单,请截图给开发!!!!</span>
        <Form :model="exeFromItem">
          <FormItem label="所有需迁移的榜单" prop="allList" style="width: 750px;">
            <Input v-model="exeFromItem.allList" type="textarea" placeholder="srcRoleId" :rows="8"/>
          </FormItem>
          <FormItem label="迁移成功的榜单" prop="successList" style="width: 750px;">
            <Input v-model="exeFromItem.successList" type="textarea" placeholder="actId" :rows="8"/>
          </FormItem>
          <FormItem label="迁移失败的榜单" prop="failList" style="width: 750px;">
            <Input v-model="exeFromItem.failList" type="textarea" placeholder="memberId" :rows="6"/>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button type="primary" @click="closeExeModal()">
            确认
          </Button>
        </div>
      </Modal>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/roleChange'
  export default {
    name: "RoleChange",
    components: {},
    data: function () {
      return {
        forceExe: false,
        formItem: {
          actId: '',
          memberId: '',
          srcRole: '',
          roleType: '200'
        },
        roleMap: {},
        file: null,
        loadingStatus: false,
        calculateLoadingStatus: false,
        executeLoadingState: false,
        canSelectRoles: [],
        srcRole: '',
        targetRole: '',
        adjustFromItem: {},
        exeFromItem: {},
        showCalculateResult: false,
        showExeResult: false,
        inputMemberId: '',
      };
    },

    watch: {
      inputMemberId () {
        this.canSelectRoles = [];
        this.targetRole = '';
      }
    },


    methods: {
      change(status){
        this.forceExe = status;
      },
      queryUserRole() {
        const self = this;
        const formItem = self.formItem;
        let reqData = {"actId": formItem.actId ,"memberId": self.inputMemberId ,"roleType": formItem.roleType}
        self.loadingStatus = true;
        Api.queryUserRole(reqData).then(
          res => {
              this.srcRole = res.data.srcRoleId;
              self.canSelectRoles = res.data.roleList;
              self.roleMap = res.data.roleMap;
              self.loadingStatus = false;
          }).catch(() => {
          //alert("查询失败");
          self.loadingStatus = false;
          self.$Message.warning('error');
        });
        self.loadingStatus = false
      },

      calculateRoleChange() {
        const self = this;
        if (!self.formItem.roleType) {
          alert("角色类型不可为空")
        }
        let reqData = {
          actId: self.formItem.actId,
          memberId: self.inputMemberId,
          roleType: self.formItem.roleType,
          targetRole: self.targetRole,
          forceExe: self.forceExe
        };
        self.calculateLoadingStatus = true;
        reqData = JSON.stringify(reqData);
        Api.calculateRoleChange(reqData).then(res => {
            self.adjustFromItem = res.data
            self.adjustFromItem.rankMoveList = JSON.stringify(res.data.rankMoveList)
            self.showCalculateResult = true
        })
        self.calculateLoadingStatus = false;
      },


      executeChangeRole() {
        const self = this;
        let reqData = {
          actId: self.adjustFromItem.actId,
          memberId: self.adjustFromItem.memberId,
          srcRoleId: self.adjustFromItem.srcRoleId,
          targetRoleId: self.adjustFromItem.targetRoleId,
          rankMoveList: JSON.parse(self.adjustFromItem.rankMoveList)
        };
       // reqData = JSON.stringify(reqData);
        self.executeLoadingState = true
        Api.executeChangeRole(reqData).then(res => {
            self.exeFromItem.successList = res.data.successList
            self.exeFromItem.failList = res.data.failList
            self.exeFromItem.allList = JSON.stringify(res.data.allList)
            self.showCalculateResult = false;
            self.showExeResult = true;
        }).catch(() => {
          alert("接口请求错误");
          self.executeLoadingState = false
          self.$Message.warning('error');
        });
        self.executeLoadingState = false
      },

      closeModal() {
        const self = this;
        self.executeLoadingState = false;
        self.showCalculateResult = false;
      },

      closeExeModal() {
        const self = this;
        self.showExeResult = false;
      }
    }
  };
</script>
