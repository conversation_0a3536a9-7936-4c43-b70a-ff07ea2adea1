<template>
  <div class="app-container">
    <div>
      <p>
        样例:<br>
        redis数据清理
        {"actId":2021062002,"dataType":"hash","delMainKey":false,"fuzzyMatch":true,"mainKey":"hdzt_ranking:2021062002:_:EventNotify","memberKey":"RankingTimeStart#2021062002:25","sourceFrom":"redis"}<br>
        说明: actId 活动ID<br>
        sourceFrom: 数据来源,目前只有 redis/mysql两个<br>
        dataType: 数据类型: set zset hash<br>
        mainKey: redis key 如hash名<br>
        memberKey: 成员key 如hash下的key名<br>
        fuzzyMatch: 是否模糊查找,默认false 取值 false/true<br>
        delMainKey: 是否清除主key 如整个hash数据 默认false<br>
        mysql 数据清除<br>
        {"actId":2021062002,"afterWhere":"act_id=2021062002 and
        member_id=2574016105","platform":"hdzt","sourceFrom":"mysql","tableName":"hdzt2_west.ranking_enrollment"}<br>
        说明:<br>
        actId 活动ID<br>
        sourceFrom: 数据来源,目前只有 redis/mysql两个<br>
        tableName: 数据库表名<br>
        afterWhere: where后面的条件,不需加where, 必须保证该条件是正确的,删除的数据无法恢复<br>
        platform: 平台 目前是活动中台/中控 hdzt/ge<br>
      </p>

      <Form :label-width="90" :model="formItem">
        <FormItem label="活动ID" prop="actId">
          <Input v-model="formItem.actId"/>
        </FormItem>
        <FormItem label="命令名称" prop="commandName">
          <Input v-model="formItem.commandName"/>
        </FormItem>
        <FormItem label="具体命令" prop="commandList">
          <Input v-model="formItem.commandList"  type="textarea" placeholder="请输入多条命令行, 用 ; 隔开"/>
        </FormItem>
      </Form>
      <div slot="footer">
        <!-- <Button :loading="loadingState" type="primary" @click="formValidate('formValidate')">-->
        <Button :loading="loadingState" type="primary" @click="commandListAdd()">
          添加
        </Button>
      </div>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/cleanup'
  export default {
    name: "Cleanup",
    components: {},
    data: function () {
      return {
        data: [],
        formItem: {},
        loadingState: false
      }
    },

    methods:{
      commandListAdd(){

        const self = this;
        const formItem = self.formItem;
        let reqData = {
          actId: formItem.actId,
          commandName: formItem.commandName,
          commandList: formItem.commandList
        };
        self.loadingState = true;
        Api.commandListAdd(reqData).then(
          res => {
            if (res.result === 0) {
              self.$Message.success('操作成功');
            } else {
              self.$Message.warning('操作失败');
            }
            self.loadingState = false;
            this.$router.push('success')
          }
        )/*.catch(() => {
          self.loadingState = false;
          self.$Message.warning('error');
          this.$router.push('fail');
        });*/
      }

    }
  };
</script>
