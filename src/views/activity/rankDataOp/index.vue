<template>
  <div class="app-container">
    <div>
      <Form inline :model="queryFrom">
        查询榜单数据 方式1:<br>
        <!--<FormItem>
          <span>活动ID：</span>
          <Input v-model="queryFrom.actId" style="width:180px"/>
        </FormItem>-->
        <FormItem   prop="actId" > <span>活动ID：</span>
          <el-select v-model="queryFrom.actId" placeholder="请选择新活动" filterable required="true" style="width: 184px" >
            <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                       :label='item.act_name+"("+item.act_id+")"'>
            </el-option>
          </el-select>
        </FormItem>

        <FormItem>
          <span>榜单ID：</span>
          <Input v-model="queryFrom.rankId" style="width:180px"/>
        </FormItem>
        <FormItem>
          <span>阶段ID：</span>
          <Input v-model="queryFrom.phaseId" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>时间：</span>
          <Input placeholder="日榜/小时榜必填" v-model="queryFrom.dateStr" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>被贡献member：</span>
          <Input placeholder="贡献榜必填" v-model="queryFrom.srcMember" style="width:180px"/>
        </FormItem>
        <FormItem>
          <span>晋级相关key：</span>
          <Select v-model="queryFrom.promote" style="width:180px">
            <Option v-for="item in promoteList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <span>其他后缀：</span>
          <Input v-model="queryFrom.otherSuffix" placeholder="自定义后缀,如task.YJXZ_001" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>查询数量：</span>
          <Input v-model="queryFrom.count" placeholder="指定返回数量,默认100个" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>成员member：</span>
          <Input v-model="queryFrom.memberId" placeholder="查询单个成员时必填" style="width:180px"/>
        </FormItem>

        <Button :loading="loadingState" type="primary" @click="queryRankData()"> 查询
        </Button>
        <Table :context="self" :columns="columns" :data="rankData1"></Table>

        <br>
      </Form>
      <Form inline>
        <hr>
        方式2:直接使用redisKey查询<br>
        <!--<FormItem>
          <span>活动ID：</span>
          <Input v-model="commandForm.actId" style="width:180px"/>
        </FormItem>-->
        <FormItem  prop="actId" > <span>活动ID：</span>
          <el-select v-model="commandForm.actId" placeholder="请选择新活动" filterable required="true" style="width: 184px" >
            <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                       :label='item.act_name+"("+item.act_id+")"'>
            </el-option>
          </el-select>
        </FormItem>
        <FormItem>
          <span>榜单ID：</span>
          <Input v-model="commandForm.rankId" style="width:180px"/>
        </FormItem>
        <FormItem>
          <span>redisKey(zset)：</span>
          <Input v-model="commandForm.command" style="width:320px"/>
        </FormItem>
        <FormItem>
          <span>查询数量：</span>
          <Input v-model="commandForm.count" placeholder="指定返回数量,默认100个" style="width:180px"/>
        </FormItem>
        <Button :loading="loadingState" type="primary" @click="queryCommandData()"> 查询
        </Button>
      </Form>

      <Table :context="self" :columns="columns" :data="rankData"></Table>


      <hr>
      <Form inline>
        <!--<FormItem>
          <span>活动ID：</span>
          <Select v-model="redisForm.actId" style="width:200px">
            <Option v-for="item in actIds" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>-->
        <FormItem   prop="actId" > <span>活动ID：</span>
          <el-select v-model="redisForm.actId" placeholder="请选择新活动" filterable required="true" style="width: 184px" >
            <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                       :label='item.act_name+"("+item.act_id+")"'>
            </el-option>
          </el-select>
        </FormItem>


        <FormItem>
          <span>榜单ID：</span>
          <Input v-model="redisForm.rankId" style="width:180px"/>
        </FormItem>
        <Button type="primary" @click="queryRedisIP()"> 查询redis实例</Button> {{redisInfo}}
      </Form>

      <Form inline>
        <!--<FormItem>
          <span>活动ID：</span>
          <Select v-model="formItem.actId" style="width:200px">
            <Option v-for="item in actIds" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>-->
        <FormItem  prop="actId" > <span>活动ID：</span>
          <el-select v-model="formItem.actId" placeholder="请选择新活动" filterable required="true" style="width: 184px" >
            <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                       :label='item.act_name+"("+item.act_id+")"'>
            </el-option>
          </el-select>
        </FormItem>
        <br>
        <FormItem>
          <span>命令：</span>
          <Select v-model="formItem.command" style="width:180px">
            <Option v-for="item in commandList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>
        <FormItem>
          <span>榜单ID：</span>
          <Input v-model="formItem.rankId" style="width:180px"/>
        </FormItem>
        <FormItem>
          <span>阶段ID：</span>
          <Input v-model="formItem.phaseId" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>时间：</span>
          <Input placeholder="日榜/小时榜必填" v-model="formItem.dateStr" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>被贡献member：</span>
          <Input placeholder="贡献榜必填" v-model="formItem.srcMember" style="width:180px"/>
        </FormItem>
        <FormItem>
          <span>晋级相关key：</span>
          <Select v-model="formItem.promote" style="width:180px">
            <Option v-for="item in promoteList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>
        <br>
        <FormItem>
          <span>成员member：</span>
          <Input v-model="formItem.memberId" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>分值：</span>
          <Input v-model="formItem.score" style="width:180px"/>
        </FormItem>
        <Button type="primary" @click="addRankDataOp()"> 添加</Button>
        <br>
      </Form>
      <Input v-model="rankDataOpsStr" type="textarea" :rows="10"/>
      <Button :loading="loadingState" type="primary" @click="exeRankDataOp()"> 执行</Button>
      <br>
      执行结果:::
      <br>
      <span>成功：</span>
      <Input v-model="successList" type="textarea" :rows="8"/>
      <span>失败：</span>
      <Input v-model="failList" type="textarea" :rows="8"/>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/rankDataOp'

  import ActConfigVersion from '@/api/activity/actConfigVersion'
  export default {
    name: "RankDataOp",
    components: {},
    data: function () {
      return {
        self:this,
        rankDataOpsStr: '[]',
        loadingState: false,
        formItem: {
          actId: '',
          type: 'rank',
          command: '',
          rankId: '',
          phaseId: '',
          dateStr: '',
          srcMember: '',
          memberId: '',
          score: ''
        },
        commandTypeList: [{
          'type': 'rank',
          'name': '榜单类'
        }, {
          'type': 'task',
          'name': '任务类(暂未实现)'
        }],
        commandList: ['zincrby', 'zadd', 'zrem'],
        promoteList: ['contest', 'promot', 'revive', 'pksettle', 'pk'],
        successList: [],
        failList: [],
        //查询相关
        queryFrom: {
          actId: '',
          rankId: '',
          phaseId: '',
          dateStr: '',
          memberId: '',
          promote: '',
          count: ''
        },
        commandForm: {
          actId: '',
          rankId: '',
          command: '',
          count: ''
        },
        columns: [
          {
            title: '排名',
            width: 180,
            key: 'rank',
          },
          {
            title: '成员ID',
            width: 250,
            key: 'member'
          }, {
            title: '积分',
            width: 180,
            key: 'score',
          }],
        rankData: [],
        rankData1: [],
        //查询相关 end
        actIds: [],

        selectActList: [],

        //redis info
        redisForm: {
          actId: '',
          rankId: ''
        },
        redisInfo: ''


      }
    },

    created: function () {
      this.loadActInfos()
    },

/*    mounted() {
      Api.actIds().then(res => {
        if (res.result === '0') {
          this.actIds = res.data;
        }
      }).catch(()=>{});;

    },*/
    methods: {
      loadActInfos() {
        ActConfigVersion.loadActInfos().then(
          res => {
            this.selectActList = res.data
          }
        ).catch(() => {
          this.selectActList = []
        });
      },

      queryRedisIP() {
        const self = this;
        if (!self.redisForm.actId) {
          alert('活动ID不可为空');
          return;
        }
        if (!self.redisForm.rankId) {
          alert('榜单ID不可为空');
          return;
        }
        let reqData = self.redisForm;
        Api.queryRedisIP(reqData).then(res=>{
          if (res.result === '0') {
            this.redisInfo = res.data;
          } else {
            alert("执行失败!!!!" + res.data);
          }
        }).catch(()=>{});;
      },
      addRankDataOp() {
        const self = this;
        if (!self.formItem.actId) {
          alert('活动ID不可为空');
          return;
        }
        if (!self.formItem.rankId) {
          alert('榜单ID不可为空');
          return;
        }
        if (!self.formItem.phaseId) {
          alert('阶段ID不可为空');
          return;
        }
        if (!self.formItem.memberId) {
          alert('成员ID不可为空');
          return;
        }
        if (!self.formItem.score) {
          alert('分值不可为空');
          return;
        }
        let rankDataOps = JSON.parse(self.rankDataOpsStr)
        rankDataOps.push(self.formItem);
        self.rankDataOpsStr = JSON.stringify(rankDataOps);
      },

      exeRankDataOp() {
        const self = this;
        let reqData = self.rankDataOpsStr;
        //reqData = JSON.stringify(reqData);
        console.log("reqData==>" + reqData);
        Api.exeRankDataOp(reqData).then(res => {
          if (res.result == 0) {
            self.successList = res.data.success
            self.failList = res.data.fail
          } else {
            alert("执行失败!!!!" + res.data);
          }
        }).catch(()=>{});
      },

      queryCommandData() {
        const self = this
        const formItem = self.commandForm;
        if (!formItem.actId || !formItem.rankId) {
          alert('活动ID或者榜单ID不可为空!');
          return;
        }
        let reqData = formItem;
        self.loadingState = true;
        Api.queryCommandData(reqData).then(res => {
          if (res.result === '0') {
            self.$Message.success("查询成功")
            this.rankData = res.data
          } else {
            //self.$Message.warning("查询失败:" + res.data)
            alert("查询失败:" + res.data);
          }
        }).catch(()=>{});
        self.loadingState = false;
      },

      queryRankData() {
        const self = this
        const formItem = self.queryFrom;
        if (!formItem.rankId || !formItem.actId) {
          alert('榜单ID和活动ID不可为0');
          return;
        }
        let reqData = formItem;
        self.loadingState = true;
        Api.queryRankData(reqData).then(res => {
          if (res.result === '0') {
            self.$Message.success("查询成功")
            this.rankData1 = res.data
          } else {
            //
            alert("查询失败:" + res.data);
          }
        }).catch(()=>{});
        self.loadingState = false;
      },


    }
  };
</script>
