<template>
  <el-dialog :visible.sync="dialogVisible" fullscreen @close="clearDialog">
    <template slot="title">
      <span>展示相同列</span>
      <el-switch v-model="showNoDiff" style="margin-left: 5px;">展示相同列</el-switch>
    </template>
    <el-table :data="showTableData" row-key="key" border :tree-props="{children: 'comparingRows'}">
<!--      <el-table-column type="selection" width="50" />-->
      <el-table-column label="表名（主键）" width="350">
        <template slot-scope="scope">
          <span v-if="scope.row.rowKey">
            <vue-json-pretty :deep="2" :data="scope.row.rowKey" />
          </span>
          <span v-else>{{ scope.row.tbl }}</span>
        </template>
      </el-table-column>
      <el-table-column label="本地配置">
        <template slot-scope="scope">
          <span v-if="scope.row.rowKey">
            <vue-json-pretty :deep="2" :data="scope.row.baseRow" />
          </span>
          <span v-else>{{ scope.row.baseSize }}</span>
        </template>
      </el-table-column>
      <el-table-column label="远端配置">
        <template slot-scope="scope">
          <span v-if="scope.row.rowKey">
            <vue-json-pretty :deep="2" :data="scope.row.remoteRow" />
          </span>
          <span v-else>{{ scope.row.remoteSize }}</span>
        </template>

      </el-table-column>
      <el-table-column label="SQL语句">
        <template slot-scope="scope">
          <span v-if="scope.row.rowKey && scope.row.modifyStatement">
            <vue-json-pretty :deep="2" :data="scope.row.modifyStatement" />
          </span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="50">
        <template slot-scope="scope">
          <el-button type="text" v-if="scope.row.modifyStatement" @click="executeStatement(scope.row.modifyStatement)">执行</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>
<script>
import { getDbComparing, batchExecuteStatements } from '@/api/activity/actFastConfigDeploy'
import VueJsonPretty from 'vue-json-pretty'
import 'vue-json-pretty/lib/styles.css'
export default {
  name: 'configComparing',
  components: { VueJsonPretty },
  data() {
    return {
      tableData: [],
      dialogVisible: false,
      showNoDiff: false,
      actId: '',
      db: '',
      compareEnv: ''
    }
  },
  computed: {
    showTableData() {
      if (!this.tableData) {
        return []
      }

      if (this.showNoDiff) {
        return [ ...this.tableData ]
      }

      const showTableData = []
      for (const table of this.tableData) {
        const rows = table.comparingRows
        if (!rows) {
          continue
        }

        const showTable = { ...table }
        showTable.comparingRows = rows.filter(row => row.modifyStatement)

        showTableData.push(showTable)

      }

      return showTableData
    }
  },
  methods: {
    showComparing(actId, db, compareEnv) {
      this.actId = actId
      this.db = db
      this.compareEnv = compareEnv
      this.loadComparing()
    },
    loadComparing() {
      const self = this
      getDbComparing(this.actId, this.db, this.compareEnv).then(res => {
        if (res && res.result === 200) {
          self.tableData = res.data
          self.dialogVisible = true
          return
        }

        self.$message({message: res.reason, type: 'error'})
      })
    },
    executeStatement(statement) {
      if (!statement) {
        this.$message({message: '无可执行操作', type: 'error'})
        return
      }

      const self = this
      this.$confirm('执行此变更？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        batchExecuteStatements(this.actId, this.db, [ statement ]).then(res => {
          if (res.result === 200) {
            self.$message({message: '执行成功', type: 'success'})
            self.loadComparing()
            return
          }

          self.$message({message: res.reason, type: 'error'})
        })
      }).catch(() => {
        self.$message({message: '执行已取消', type: 'info'})
      })
    },
    clearDialog() {
      this.actId = ''
      this.db = ''
      this.compareEnv = ''
    }
  }
}
</script>

<style scoped></style>
