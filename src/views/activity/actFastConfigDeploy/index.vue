<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="活动id">
            <el-select v-model="currentActId" placeholder="请选择活动"
                       filterable clearable style="width:180px">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-button type="primary" @click="onSearch()" icon="el-icon-search"
                     size="medium">
            查询
          </el-button>
          <el-tooltip class="item" effect="light" content="选择已上线活动进行操作" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>

      <el-card class="box-card">
        <step procId="1" ref="deployProc" />
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>灰度名设置</span></div>
        <el-button  type="text" @click="openGreyWhitelist">中台灰度名单  </el-button>

      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>外部依赖设置</span></div>
        <el-button type="text" @click="jumpToOuter">挂件设置 </el-button>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>配置检查</span>
        </div>
        <el-button type="text" @click="openConfigCenter">活动配置检查</el-button>
      </el-card>

      <el-card class="box-card">
        <div slot="header" class="clearfix">
          <span>增量上线</span>
        </div>
        <el-select v-model="incrementalDb" placeholder="选择DB">
          <el-option v-for="item in dbList" :key="item.db" :value="item.db" :label="item.desc" />
        </el-select>
        <el-button type="primary" round @click="onComparingConfig">配置对比</el-button>
      </el-card>

      <el-card class="box-card">
        <step procId="2" ref="cleanProc" />
      </el-card>

      <el-card class="box-card">
        <el-button type="primary" @click="onClickCleanGreyStatus" icon="el-icon-upload"
                   size="medium">
          活动对外发布
        </el-button>
      </el-card>
    </div>

    <Modal
      v-model="isShowGreyFlagMedal"
      title="清除确认"
      @on-ok="onCleanGreyStatus">
      <p>确认清除活动 {{currentActId}} 对应的灰度标识?请确保其他灰度数据已清理完毕</p>
    </Modal>

    <config-comparing ref="comparingDialog" />
  </div>
</template>

<script>
import ActConfigVersion from '@/api/activity/actConfigVersion'
import step from "@/views/components/step/index"
import configComparing from "./configComparing.vue"
import { cleanGreyStatus } from '@/api/activity/actFastConfigDeploy'

export default {
  name: "actFastConfigDeploy",
  components: { step, configComparing },
  data: function () {
    return {
      selectActList: [],
      currentActId: '',
      deploySteps: [],
      cleanSteps: [],
      isShowGreyFlagMedal: false,
      dbList: [
        {
          db: 'component',
          desc: '组件属性'
        },
        {
          db: 'hdzk',
          desc: '中控配置'
        },
        {
          db: 'hdzt',
          desc: '中台配置'
        },
        {
          db: 'currency',
          desc: '虚拟资产'
        },
        {
          db: 'shop',
          desc: '商城配置'
        }
      ],
      incrementalDb: '',
      compareEnv: process.env.ENV !== 'production' ? 'prod' : 'test'
    }
  },
  created() {
    this.loadActInfos()
    const _query = this.$route.query
    if (_query && _query.actId) {
      this.currentActId = _query.actId
      console.log('current actId=', this.currentActId)
    }
  },
  methods: {
    //加载活动数据
    loadActInfos() {
      const self = this
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            self.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    onSearch() {
      this.loadDeploySteps()
      this.loadCleanSteps()
    },
    loadDeploySteps() {
      if (!this.currentActId) {
        this.$message({message: '必须先选择一个活动', type: 'error'})
        return
      }

      this.$refs.deployProc.loadProcessInfo(this.currentActId)
    },
    loadCleanSteps() {
      if (!this.currentActId) {
        this.$message({message: '必须先选择一个活动', type: 'error'})
        return
      }

      this.$refs.cleanProc.loadProcessInfo(this.currentActId)
    },
    openGreyWhitelist() {
      if (!this.currentActId) {
        this.$message({message: '必须先选择一个活动', type: 'error'})
        return
      }

      this.$router.push({path: '/greyClean/actGreyWhiteList', query: {actId: this.currentActId}})
    },
    openConfigCenter() {
      if (!this.currentActId) {
        this.$message({message: '必须先选择一个活动', type: 'error'})
        return
      }

      this.$router.push({path: '/activityCheck/configCenter', query: {actId: this.currentActId}})
    },
    onClickCleanGreyStatus() {
      if (!this.currentActId) {
        this.$message({message: '必须先选择一个活动', type: 'error'})
        return
      }

      this.isShowGreyFlagMedal = true
    },
    onCleanGreyStatus() {
      if (!this.currentActId) {
        this.$message({message: '必须先选择一个活动', type: 'error'})
        return
      }

      const self = this
      cleanGreyStatus(this.currentActId).then(res => {
        if (res.result === 200) {
          self.$message({message: '清理成功', type: 'success'})
        } else {
          self.$message({message: res.reason, type: 'error'})
        }
      }).finally(() => {
        self.isShowGreyFlagMedal = false
      })
    },
    jumpToOuter() {
      window.open('https://jydev' + (process.env.ENV === 'production' ? '' : '-test') + '.yy.com/n/server_debug/activity_rank_config')
    },
    onComparingConfig() {
      if (!this.currentActId) {
        this.$message({message: '必须先选择一个活动', type: 'error'})
        return
      }
      if (!this.incrementalDb) {
        this.$message({message: '请先选择数据源', type: 'error'})
        return
      }

      this.$refs.comparingDialog.showComparing(this.currentActId, this.incrementalDb, this.compareEnv)
    }
  }
}
</script>

<style scoped lang="scss">
.box-card {
  margin-top: 15px;
}
</style>
