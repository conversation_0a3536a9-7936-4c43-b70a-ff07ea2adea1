<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="活动id">
            <el-select v-model="searchForm.actId" placeholder="请选择活动"
                       filterable style="width:180px">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>


          <el-form-item label="任务id">
            <el-input v-model="searchForm.jobId" style="width:100px"></el-input>
          </el-form-item>
          <el-form-item label="用户id">
            <el-input v-model="searchForm.uid" style="width:100px"></el-input>
          </el-form-item>
          <el-form-item label="发放状态">
            <el-select v-model="searchForm.status" placeholder="发放状态" style="width:120px" clearable>
              <el-option v-for="item in statusList" :value="item.code" :key="item.code" :label='item.desc'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="-" value-format="yyyy-MM-dd"
                            start-placeholder="开始日期" end-placeholder="结束日期" style="width: 250px;">
            </el-date-picker>
          </el-form-item>
          <el-button type="primary" :loading="loadingAwardIssueLog" @click="onSearch(true)" icon="el-icon-search"
                     size="medium">
            查询
          </el-button>
          <el-button type="primary" icon="el-icon-edit-outline" size="medium" @click="onEditAward(null)">添加补单
          </el-button>
          <el-button type="info" :loading="exportAwardIssueLog" @click="onExport" size="medium">
            导出Excel
          </el-button>
          <el-tooltip class="item" effect="light" content="操作表:award_issue" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>
      <el-row>
        <el-table :data="tableData" border v-loading="loadingAwardIssueLog">
          <el-table-column prop="jobId" label="任务id" align="center" width="150"></el-table-column>
          <el-table-column prop="name" label="任务名" align="center"></el-table-column>
          <el-table-column prop="uid" label="用户id" align="center" width="110"></el-table-column>
          <el-table-column prop="taskId" label="奖池" align="center" width="150"></el-table-column>
          <el-table-column prop="packageId" label="奖包" align="center" width="150"></el-table-column>
          <el-table-column prop="packageName" label="奖包名" align="center" width="80"></el-table-column>
          <el-table-column prop="cnt" label="发放数量" align="center" width="80"></el-table-column>
          <el-table-column prop="statusText" label="状态" align="center" width="80"></el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center"></el-table-column>
          <el-table-column prop="updateTime" label="更新时间" align="center"></el-table-column>
          <el-table-column prop="seq" label="seq" align="center"></el-table-column>
          <el-table-column prop="resp" label="resp" align="center"></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="[10,15,20,50,100]"
          @size-change="onSearch(false)"
          @current-change="onSearch(false)"
          layout="->, total, prev, pager, next, sizes"
          :current-page.sync="pageData.page"
          :page-size.sync="pageData.size"
          :total="pageData.total">
        </el-pagination>
      </el-row>

      <el-dialog title="活动补单" :visible.sync="issueJobFormVisible"
                 :close-on-click-modal="false" @closed="onCloseAwardForm">
        <el-form :model="issueJobFormData" label-width="150px">

          <el-form-item label="活动id">
            <el-select v-model="issueJobFormData.actId" placeholder="请选择活动" filterable style="width:100%;" @change="actIdChange">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id" :label='item.act_name+"("+item.act_id+")"' />
            </el-select>

          </el-form-item>

          <el-form-item label="奖池id">
            <el-select v-model="issueJobFormData.taskId" placeholder="请选择奖池" style="width:100%;" @change="awardPoolChange">
              <el-option v-for="item in awardPool" :value="item.code" :key="item.code" :label='item.desc+"("+item.code+")"' />
            </el-select>
          </el-form-item>

          <el-form-item label="奖包id">
            <el-select v-model="issueJobFormData.packageId" placeholder="请选择奖包" style="width: 100%">
              <el-option v-for="item in awardPackage" :value="item.code" :key="item.code" :label='item.desc+"("+item.code+")"' />
            </el-select>
          </el-form-item>

          <el-form-item label="数量">
            <el-input v-model="issueJobFormData.cnt" placeholder="奖包id"></el-input>
          </el-form-item>

          <el-form-item label="补单任务名称">
            <el-input v-model="issueJobFormData.name" placeholder="补单名称"></el-input>
          </el-form-item>

          <el-form-item label="补单开始时间">
            <el-col :span="11">
              <el-date-picker type="datetime" placeholder="选择开放时间" v-model="issueJobFormData.startTime"
                              style="width: 100%;" value-format="timestamp"></el-date-picker>
            </el-col>
          </el-form-item>

          <el-form-item label="用户UID">
            <el-input type="textarea" v-model="issueJobFormData.uids" placeholder="多个UID用,分割 或 每行一个"></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button @click="issueJobFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="onSaveAward">保 存</el-button>
        </div>
      </el-dialog>
    </div>


  </div>
</template>

<script>
import API from '@/api/activity/issueJobDetail'
import RankTaskConfigApi from '@/api/activity/rankTaskConfig'
import ActConfigVersion from '@/api/activity/actConfigVersion'

export default {
  name: "IssueJobDetail",
  components: {},
  data: function () {
    return {
      loadingAwardIssueLog: false,
      exportAwardIssueLog: false,
      selectActList: [],
      awardPool: [],
      awardPackage: [],
      statusList: [],
      searchForm: {
        actId: '',
        poolId: '',
        packageId: '',
        uid: '',
        status: '',
        dateRange: [],
        jobId: ''
      },
      pageData: {
        page: 1,
        size: 10,
        total: 0,
      },
      issueJobFormVisible: false,
      issueJobFormData: {
        actid: '',
        uids: '',
        taskId: '',
        packageId: '',
        busiId: '',
        cnt: '1',
        startTime: '',
        name: '',
      },
      tableData: []
    }
  },
  created: function () {
    this.loadActInfos()
    this.buildStatusList()
  },
  methods: {
    onSearch(fromBtn) {
      const req = this.searchForm
      if (req.actId === '' || isNaN(req.actId)) {
        this.$Message.warning("请选择要查询的活动")
        return
      }
      const self = this
      if (fromBtn) {
        self.pageData.page = 1
      }
      req.pageNo = self.pageData.page
      req.pageSize = self.pageData.size
      if (req.dateRange != null && req.dateRange.length === 2) {
        req.startDay = req.dateRange[0]
        req.endDay = req.dateRange[1]
      } else {
        this.$Message.warning("请选择时间范围")
        return
      }
      self.loadingAwardIssueLog = true
      API.issuejobdetailLog(req)
        .then(res => {
          if (res.code === 200) {
            self.pageData.total = res.total
            self.tableData = res.data
          } else {
            this.$Message.warning(res.reason, 5)
          }
          self.loadingAwardIssueLog = false
        })
        .catch(error => {
          self.loadingAwardIssueLog = false
          console.log(error)
          this.$Message.error(error.status + "," + error.statusText, 5)
        })
    },
    onExport() {
      const req = this.searchForm
      if (req.actId === '' || isNaN(req.actId)) {
        this.$Message.warning("请选择要查询的活动")
        return
      }
      const self = this
      if (req.dateRange != null && req.dateRange.length === 2) {
        req.startDay = req.dateRange[0]
        req.endDay = req.dateRange[1]
      }
      // self.exportAwardIssueLog = true

      this.download('awardIssueLog/exportExcel' + this.queryParams(req, true), {}, `奖品发放记录_${new Date().getTime()}.xlsx`)
    },
    // 新增or编辑奖池
    onEditAward: function () {
      this.issueJobFormVisible = true
      this.issueJobFormData.actId = this.searchForm.actId
    },
    // 保存奖池信息
    onSaveAward: function () {
      // 参数校验
      const self = this
      const issueJobFormData = self.issueJobFormData
      if (!this.checkActId(issueJobFormData.actId)) {
        return
      }
      if (issueJobFormData.taskId === '') {
        this.$Message.warning("请输入奖池id")
        return
      }
      if (issueJobFormData.packageId === '') {
        this.$Message.warning("请输入奖包id")
        return
      }
      if (issueJobFormData.startTime === '') {
        this.$Message.warning("请填写补单开始时间")
        return
      }
      if (issueJobFormData.uids === '') {
        this.$Message.warning("请填写补单用户")
        return
      }
      API.saveIssueJob(issueJobFormData).then(
        res => {
          if (res.result === 200) {
            self.resetAwardForm()
            self.issueJobFormVisible = false
            this.$Message.success("保存成功")
            if (self.searchForm.actId === '') {
              self.searchForm.actId = issueJobFormData.actId
            }
            self.onSearch()
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      )
    },
    // 重置数据
    resetAwardForm: function () {
      this.issueJobFormData = {
        actid: '',
        uids: '',
        taskId: '',
        packageId: '',
        busiId: '',
        cnt: '1',
        startTime: '',
        name: ''
      }
    },
    checkActId(actId) {
      if (actId === '' || actId === null) {
        this.$Message.warning("请输入活动ID")
        return false
      }
      if (actId === '' || actId <= 0 || isNaN(actId)) {
        this.$Message.warning("输入的活动ID有误,请检查")
        return false
      }
      return true
    },
    // 关闭弹窗
    onCloseAwardForm: function () {
      this.resetAwardForm()
    },
    //加载活动数据
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    actIdChange(_actId) {
      const self = this
      this.issueJobFormData.taskId = ''
      this.issueJobFormData.packageId = ''
      RankTaskConfigApi.listAwardTaskDropDown(_actId, "100,101,102,103,200,201,202,203")
        .then(res => {
          if (res.result === 200) {
            self.awardPool = res.data
          } else {
            console.log(res)
          }
        }).catch(error => {
        console.log(error)
      })
    },
    awardPoolChange(_taskId) {
      const self = this
      this.issueJobFormData.packageId = ''
      RankTaskConfigApi.listAwardPackageDropDown(self.issueJobFormData.actId, _taskId)
        .then(res => {
          if (res.result === 200) {
            self.awardPackage = res.data
          } else {
            console.log(res)
          }
        }).catch(error => {
        console.log(error)
      });
    },
    buildStatusList() {
      const status = [
        {
          code: '1',
          desc: '发放成功'
        }, {
          code: '2',
          desc: '失败'
        }]
      this.statusList = status
    },
    queryParams(data, isPrefix = false) {
      let prefix = isPrefix ? '?' : ''
      let _result = []
      for (let key in data) {
        console.log(key)
        if (key === 'dateRange') {
          continue
        }
        let value = data[key]
        // 去掉为空的参数
        if (['', undefined, null].includes(value)) {
          continue
        }
        if (value.constructor === Array) {
          value.forEach(_value => {
            _result.push(encodeURIComponent(key) + '[]=' + encodeURIComponent(_value))
          })
        } else {
          _result.push(encodeURIComponent(key) + '=' + encodeURIComponent(value))
        }
      }

      return _result.length ? prefix + _result.join('&') : ''
    },
  }
};
</script>
