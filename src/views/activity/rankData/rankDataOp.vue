<template>
  <div class="app-container">
    <div>
      <Form inline :model="formItem">
        查询榜单数据    方式1:<br>
        <FormItem>
          <span>活动ID：</span>
          <Input v-model="formItem.actId" style="width:180px" />
        </FormItem>
        <FormItem>
          <span>榜单ID：</span>
          <Input v-model="formItem.rankId" style="width:180px" />
        </FormItem>
        <FormItem>
          <span>阶段ID：</span>
          <Input v-model="formItem.phaseId" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>时间：</span>
          <Input placeholder="日榜/小时榜必填" v-model="formItem.dateStr" style="width:180px"/>
        </FormItem>

        <FormItem>
          <span>被贡献member：</span>
          <Input placeholder="贡献榜必填" v-model="formItem.srcMember" style="width:180px"/>
        </FormItem>
        <FormItem>
          <span>晋级相关key：</span>
          <Select v-model="formItem.promote" style="width:180px">
            <Option v-for="item in promoteList" :value="item" :key="item">{{ item }}</Option>
          </Select>
        </FormItem>
        <br>
        <FormItem>
          <span>成员member：</span>
          <Input v-model="formItem.member" placeholder="查询单个成员时必填" style="width:180px"/>
        </FormItem>

        <Button :loading="loadingState" type="primary" @click="queryRankData()"> 查询
        </Button>
        <!-- <Button :loading="loadingState" type="primary" @click="exportRankExcel()"> 导出
         </Button>-->
        <br>
        <hr>
        方式2:直接使用redisKey查询<br>
        <FormItem>
          <span>活动ID：</span>
          <Input v-model="commandForm.actId" style="width:180px" />
        </FormItem>
        <FormItem>
          <span>榜单ID：</span>
          <Input v-model="commandForm.rankId" style="width:180px" />
        </FormItem>
        <FormItem>
          <span>redisKey(zset)：</span>
          <Input v-model="commandForm.command" style="width:320px" />
        </FormItem>
        <Button :loading="loadingState" type="primary" @click="queryCommandData()"> 查询
        </Button>
      </Form>

      <Table :context="self" :columns="columns" :data="rankData"></Table>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/rankDataOp'

  export default {
    name: "RankDataOp",
    components: {},
    data: function () {
      return {
        self:this,
        rankData: [],
        columns: [
          {
            title: '排名',
            width: 180,
            key: 'rank',
          },
          {
            title: 'memberId',
            width: 250,
            key: 'member'
          }, {
            title: 'score',
            width: 180,
            key: 'score',

          }],
        loadingState: false,
        formItem: {
          actId: '',
          rankId: '',
          phaseId: '',
          dateStr: '',
          memberId: '',
          promote: ''
        },
        commandForm: {
          actId: '',
          rankId: '',
          command: ''
        },

        promoteList: ['contest', 'promot', 'revive', 'pksettle', 'pk'],
      }
    },

    methods: {
      exportRankExcel() {
        const formItem = this.formItem;
        if (formItem.rankId === 0 || formItem.phaseId === 0) {
          alert('榜单ID和阶段ID不可为0');
          return;
        }
        var reqdata = 'actId=' + formItem.actId + '&isFilterWord=' + formItem.isFilterWord
          + '&columns=' + formItem.columns + '&useNewWay=' + formItem.useNewWay + '&showType=' + formItem.showType
          + '&pointedMember=' + formItem.pointedMember + '&rankId=' + formItem.rankId + '&phaseId=' + formItem.phaseId;
        window.location.href = process.env.VUE_APP_BASE_API + '/excel/rankExcel.do?' + reqdata;
      },

      queryCommandData() {
        const self = this
        const formItem = self.commandForm;
        if (!formItem.actId || !formItem.rankId) {
          alert('活动ID或者榜单ID不可为空!');
          return;
        }
        let reqData = formItem;
        self.loadingState = true;
        Api.queryCommandData(reqData).then(res => {
            this.rankData = res.data
        });
        self.loadingState = false;
      },

      queryRankData() {
        const self = this
        const formItem = self.formItem;
        if (!formItem.rankId || !formItem.phaseId) {
          alert('榜单ID和阶段ID不可为0');
          return;
        }
        let reqData = formItem;
        self.loadingState = true;
        Api.queryRankData(reqData).then(res => {
            this.rankData = res.data
        })
        self.loadingState = false;
      },

    }
  };
</script>
