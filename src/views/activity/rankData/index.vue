<template>
  <div class="app-container">
    <div>
      <Form inline :model="formItem">
        <FormItem>
          <span>活动ID：</span>
          <Input v-model="formItem.actId" style="width:200px"/>
        </FormItem>

        <FormItem>
          <span>榜单ID：</span>
          <Input v-model="formItem.rankId" style="width:200px"/>
        </FormItem>
        <FormItem>
          <span>阶段ID：</span>
          <Input v-model="formItem.phaseId" style="width:200px"/>
        </FormItem>
        <FormItem>
          <span>extQuery(查主播家族填anchorFamilyName)：</span>
          <Input v-model="formItem.extQuery" style="width:200px"/>
        </FormItem>
        <FormItem>
          <span>时间：</span>
          <Input v-model="formItem.dateStr" placeholder="日榜/小时榜必填" style="width:200px"/>
        </FormItem>

        <FormItem>
          <span>贡献成员：</span>
          <Input v-model="formItem.findSrcMember" placeholder="贡献榜所属成员" style="width:200px"/>
        </FormItem>

        <FormItem>
          <span>数量：</span>
          <Input v-model="formItem.rankCount" style="width:200px"/>
        </FormItem>

        <Button :loading="loadingState" type="primary" @click="queryRank()"> 查询
        </Button>
        <Button :loading="loadingState" type="primary" @click="exportRankExcel()"> 导出
        </Button>
        <br>
      </Form>
      <Table :context="self" :columns="columns" :data="rankData"></Table>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/rankData'

  export default {
    name: "RankData",
    components: {},
    data: function () {
      return {
        self:this,
        rankData: [],
        columns: [{
          title: 'uid/sid',
          width: 180,
          key: 'key'
        }, {
          title: '昵称',
          width: 180,
          key: 'nick',
          render: (h, params) => {
            let content
            if (!params.row.nick) {
              content = params.row.name;
            } else {
              content = params.row.nick;
            }
            return h('p', content);
          }
        }, {
          title: '签约公会/公会ID',
          width: 180,
          key: 'contractSid',
          render: (h, params) => {
            let content
            if (!params.row.contractSid) {
              content = params.row.sid;
            } else {
              content = params.row.contractSid;
            }
            return h('p', content);
          }
        },{
          title: '签约家族',
          width: 180,
          key: 'familId',
          render: (h, params) => {
            let content
            if (params.row.viewExt.familyId) {
              content = params.row.viewExt.familyId;
            }
            return h('p', content);
          }
        }, {
          title: '签约公会短号/公会短号',
          width: 180,
          key: 'contractAsid',
          render: (h, params) => {
            let content
            if (!params.row.contractAsid) {
              content = params.row.asid;
            } else {
              content = params.row.contractAsid;
            }
            return h('p', content);
          }
        }, {
          title: '分数',
          width: 180,
          key: 'value'
        }, {
          title: '排名',
          width: 180,
          key: 'rank'
        },
        ],
        loadingState: false,
        formItem: {
          isFilterWord: 0,
          columns: '',
          actId: this.$route.query.actId || '',
          useNewWay: 1,
          showType: 1,
          pointedMember: 0,
          rankId: this.$route.query.rankId || '',
          phaseId: this.$route.query.phaseId || '',
          dateStr: '',
          findSrcMember: '',
          extQuery: '',
          rankCount:1000
        }
      }
    },

    methods: {

      exportRankExcel() {
        const formItem = this.formItem;
        if (formItem.rankId === 0 || formItem.phaseId === 0) {
          alert('榜单ID和阶段ID不可为0');
          return;
        }
        let reqData = formItem;
        this.download('excel/rankExcel.do', {
          ...reqData
        }, `榜单数据_${new Date().getTime()}.xlsx`)
      },

      queryRank() {
        const self = this
        const formItem = self.formItem;
        if (!formItem.rankId || !formItem.phaseId) {
          alert('榜单ID和阶段ID不可为0');
          return;
        }
        let reqData = formItem;
        self.loadingState = true;
        Api.queryRank(reqData).then(res => {
          this.rankData = res.data
          self.loadingState = false;
        }).catch(() => self.loadingState = false);

      },

    }
  };
</script>
