<template>
  <div class="app-container">
    <div xmlns:v-bind="http://www.w3.org/1999/xhtml">
      <!--<p class="admin-set-title2" v-if="!envTips">申请测试环境下的权限请直接找后台（朱国升）添加，选择权限，点击提交，五分钟后生效
      </p>
      <span class="admin-set-title">请选择您要申请的页面权限</span>
      <div class="admin-set">
        <div style="float: left">
          <Tree :data="data"
                show-checkbox expand-node multiple
                @on-check-change="checkChange"></Tree>
        </div>
        <div style="float: left">

          <Form :label-width="80" class="admin-set-right">
            <FormItem label="申请原因">
              <Input v-model="reason" type="textarea" :rows="4" placeholder="请输入原因"/>
            </FormItem>
            <FormItem label="选择有效期">
              <Select v-model="duration" style="width:200px">
                <Option v-for='item in selectDays' :key="item.value" :value="item.value">{{item.name}}</Option>
              </Select>
            </FormItem>
          </Form>
        </div>
      </div>

      <Button :loading="loadingState" type="primary" @click="applyPermission" class="admin-set-button">
        提交申请
      </Button>-->

      <iframe name="permissionFrame" v-bind:src="permissionPageUrl" width="100%" height="800px"></iframe>
    </div>

  </div>
</template>

<script>
  import Api from '@/api/activity/actConfigVersion'
  export default {
    name: "Adminset",
    components: {},
    data () {
      return {
        accessKey: 'hdzt-admin',
        data: [],
        mapList: {
          title: 'name',
          children: 'children',
          id: 'id',
          nodeType: 'nodeType'// 节点类型 1-菜单，2-权限
        },
        expand: false,
        items: [],
        reason: '',
        duration: null, // 天
        selectDays: [
          { name: '一天',
            value: '1' },
          { name: '一周',
            value: '7' },
          { name: '一个月',
            value: '30' },
          { name: '三个月',
            value: '90' },
          { name: '一年',
            value: '365' },
          { name: '永久',
            value: '-1' }
        ],
        loadingState: false,
        envTips: false,
        permissionPageUrl: 'https://zhuiya-test.yy.com/admin-static/adminset.html?accessKey=hdzt-admin'

      }
    }
  };
</script>
