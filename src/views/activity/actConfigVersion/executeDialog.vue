<template>
  <el-dialog :title="title" :visible.sync="dialogShow" fullscreen >
    <el-steps :active="step" finish-status="success" simple style="margin-top: 20px">
      <el-step title="预览" :status="step===1? status:'success'"></el-step>
      <el-step title="执行" :status="step===2? status:'wait'"></el-step>
    </el-steps>
    <!--预览-->
    <div v-show="step ===1 &&status==='success'">
      <el-button-group>
        <el-button type="primary"
                   @click="switchCollapse(collapseIndex.length!==collapseAllKey.length)">
          {{collapseIndex.length===collapseAllKey.length?'折叠':'展开'}}
        </el-button>
        <el-button type="primary"
                   @click="showZero = !showZero">
          {{showZero?'隐藏0项':'显示0项'}}
        </el-button>
        <el-button type="primary" @click="$emit('confirm')">确认执行</el-button>


      </el-button-group>
      <el-row v-for="model in models" :key = "model.key">
        {{ model.name }}
        <el-collapse v-model="collapseIndex">
          <el-collapse-item v-for="item in preViewData[model.key]" :title="item.title"  :key = "item.key"
                            :name="item.key" v-show="item.contexts.length!==0 || showZero">
            <div v-for="context in item.contexts"> {{ context }}</div>
          </el-collapse-item>
        </el-collapse>
      </el-row>


    </div>
    <!--   执行结果   -->
    <div v-show="step ===2 &&status==='success'">

      <el-row v-for="model in models" :key="model.key">
        {{ model.name }}
        <el-row v-for="item in executeData[model.key]" :key="item.key"
                :name="item.key">
          <div> {{ item.title }}</div>
        </el-row>
      </el-row>
    </div>
    <!--异常结果-->
    <div v-show="status==='error'">
      <el-result title="请求异常" icon="error" :subTitle="errorMessage">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="dialogShow = false">关闭</el-button>
        </template>
      </el-result>
    </div>

    <div v-show="status==='wait'" v-loading="status==='wait'"
         element-loading-text="拼命加载中">
    </div>
  </el-dialog>


</template>

<script>
  import Api from '@/api/activity/actConfigVersion'
  import codeDiff from './codeDiff'
  import executeDialog from './executeDialog'

export default {
  name: 'ExecuteDialog',
  props: {
    title: String,
    models: {
      key: {
        type: String,
        required: true
      },
      name: {
        type: String,
        required: true
      }
    }
  },
  data: function () {
    return {
      dialogShow: false,
      showZero: true,
      step: 1,
      status: "",
      errorMessage: "",
      preViewData: {},
      executeData: {},
      collapseIndex: [],
      collapseAllKey: []
    }
  },
  watch: {
    dialogShow: function (val) {
      if (!val) {
        this.closeDialog()
      }
    }
  },
  methods: {
    openWait(step) {
      this.dialogShow = true
      this.step = step
      this.status = 'wait'
    },
    setPreViewData(data) {
      const allKeys = []
      const models = this.models
      for (const i in models) {
        const modelKey = models[i].key
        const dataItem = data[modelKey]
        for (const j in dataItem) {
          allKeys.push(dataItem[j].key)
        }
      }

      this.collapseAllKey = allKeys
      this.collapseIndex = []
      this.preViewData = data
      this.step = 1;
      this.status = 'success'
    },
    setExecuteData(data) {
      this.executeData = data
      this.step = 2;
      this.status = 'success'
    },
    setError(message) {
      this.errorMessage = message;
      this.status = 'error'
    },
    /*关闭的时候清除数据*/
    closeDialog() {
      this.showZero = true
      this.collapseAllKey = []
      this.collapseIndex = []
      this.preViewData = {}
      this.executeData = {}
      this.step = 1;
      this.status = ''
      this.errorMessage = '';
      this.status = ''
    },
    /*展开折叠*/
    switchCollapse(open) {
      this.collapseIndex= open ? this.collapseAllKey:[];
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
