<template>
  <el-dialog :visible.sync="dialogShow" :fullscreen='dialogFull' width="60%">
    <template slot="title">
      <el-row type="flex">
        <el-col :span="12">
          <div> 版本对比</div>
        </el-col>
        <el-col :span="1" :offset="11">
          <el-button type="text" size="mini" :icon="dialogFull ? 'el-icon-crop' : 'el-icon-full-screen'"
                     @click="dialogFull? dialogFull=false: dialogFull=true"></el-button>
        </el-col>
      </el-row>

    </template>
    <span>【注】W定位上一处不同，S定位下一处不同，不修改全局数据</span>
    <el-row type="flex">
      <el-col :span="12">
        <div>{{title}}</div>
      </el-col>
      <el-col :span="12">
        <div style="float:right">{{rightTitle}}</div>
      </el-col>
    </el-row>
    <!-- <div id="codeView"></div> -->
    <codemirror ref="myCm" :merge="true" :options="cmOption"></codemirror>

    <br>
    <el-row>
      <el-button type="primary" @click="submit()" :disabled="!cmOption.revertButtons" style="float:right; margin-left: 10px;">提交</el-button>

      <el-button type="primary" @click="coverAll()"  :disabled="!cmOption.revertButtons" style="float:right; ">全覆盖</el-button>
    </el-row>
  </el-dialog>
</template>

<script>
  import { codemirror } from 'vue-codemirror'
  import 'codemirror/lib/codemirror.css'
  import 'codemirror/addon/merge/merge.js'
  import 'codemirror/addon/merge/merge.css'
  import DiffMatchPatch from 'diff-match-patch'


  export default {
    name: 'CodeDiff',
    data: function () {
      return {
        dialogFull: false,
        dialogShow: false,
        title: "",
        rightTitle: "",
        cmOption: {
          value: '', // 上次内容
          origLeft: null,
          orig: '', // 本次内容
          lineNumbers: true, // 显示行号
          mode: 'text/html',
          highlightDifferences: true,
          foldGutter: true,
          lineWrapping: true,
          styleActiveLine: true,
          matchBrackets: true,
          revertButtons: false,
          showDifferences: true,
          collapseIdentical: false,
          connect: 'align',
          readOnly: true, // 只读 不可修改
          extraKeys: {
            "W": "goPrevDiff",
            "S": "goNextDiff"
          }
        }
      }
    },
    components: {
      codemirror
    },
    mounted() {
      window.diff_match_patch = DiffMatchPatch
      window.DIFF_DELETE = -1
      window.DIFF_INSERT = 1
      window.DIFF_EQUAL = 0
    },
    watch: {
      dialogFull: function (val) {
        const divs = document.getElementsByClassName("CodeMirror");
        for (let i = 0;i<divs.length;i++) {
         const div = divs[i];
          if (this.dialogFull) {
          //  this.codemirror.setSize('600px')
          }else{
            // this.codemirror.setSize('350px')
           // div.style.height = "350px";
          }
        }
      },
      dialogShow: function(val) {
        if (!val) {
          this.cmOption.value = ''
          this.cmOption.orig = ''
          console.log(typeof(this.codemirror))
        }
      }
    },
    computed: {
      codemirror() {
        return this.$refs.myCm.codemirror
      }
    },
    methods: {
      submit() {
        if (this.codemirror.edit.getValue() === this.cmOption.value) {
          this.$message({message: '没有修改无需提交', duration: 1000});
          return
        }
        this.$emit('submit', this, this.codemirror.edit.getValue())
      },
      coverAll() {
        this.$modal.confirm('将覆盖全部配置，请确认').then(() => {
          this.$emit('submit', this, this.cmOption.orig)
        }) .catch(() => {});
      },
      open(title, value, rightTitle, rightValue, revert) {
        this.cmOption.value = value
        this.dialogShow = true
        this.title = title
        this.rightTitle = rightTitle
        this.cmOption.revertButtons = revert
        this.cmOption.orig = rightValue
      }
    }
  }
</script>

<style lang="scss" scoped>
  .CodeMirror {
    border: 1px solid #eee;
    height: auto;
  }
  .CodeMirror-scroll {
    height: auto;
    overflow-y: hidden;
    overflow-x: auto;
  }
</style>
