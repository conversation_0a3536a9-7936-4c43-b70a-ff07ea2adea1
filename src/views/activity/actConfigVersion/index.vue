<template>
  <div class="app-container">
    <el-form :inline="true" :model="fromProps" label-width="90" class="demo-form-inline">
      <el-form-item label="活动ID">
        <el-select v-model="fromProps.actId" filterable placeholder="请选择活动" @change="queryAllVersions"
                   allow-create default-first-option
                   style="width:300px">
          <el-option
            v-for="item in selectActList"
            :key="item.act_id"
            :label="item.act_name+'('+item.act_id+')'"
            :value="item.act_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库">
        <el-select v-model="fromProps.dataBase" style="width:200px" @change="queryAllVersions"
                   allow-create default-first-option>
          <el-option
            v-for="item in selectDataBaseList"
            :key="item.code"
            :label="item.desc"
            :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="showComparer" :disabled="!canCompare" style="margin-left: 8px">对比</el-button>
      </el-form-item>
    </el-form>

    <div>
      <el-table
        ref="multipleTable"
        :data="page.tableData"
        border
        style="width: 100%"
        @selection-change="handleSelect">
        <el-table-column
          type="selection"
          width="60">
        </el-table-column>

        <el-table-column
          fixed
          prop="id"
          label="Id">
        </el-table-column>
        <el-table-column
          prop="versionDesc"
          label="版本说明"
        >
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="时间"
        >
        </el-table-column>
        <el-table-column
          prop="createPassport"
          label="操作人"
        >
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.id===0"
              size="mini"
              @click="backup()">备份
            </el-button>
            <el-button
              v-if="scope.row.id===0"
              size="mini"
              type="danger"
              @click="synToDeploy(false)">上线
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        background
        @size-change="changePage"
        @current-change="changePage"
        :current-page.sync="page.current"
        :page-size.sync="page.pageSize"
        :page-sizes="page.pageSizeOpts"
        layout="total, prev, pager, next,sizes"
        :total="page.total"
        style="float:right;">
      </el-pagination>

    </div>
    <executeDialog ref="synDialog" title="上线同步" :models="synToDeployData.models"
                   @confirm="synToDeploy(true)"></executeDialog>
    <executeDialog ref="diffDialog" title="版本覆盖" :models="coverData.models" @confirm="recoverConfig"></executeDialog>
    <codeDiff ref="diff" @submit="recoverPreview"></codeDiff>
  </div>
</template>

<script>
  import Api from '@/api/activity/actConfigVersion'
  import codeDiff from './codeDiff'
  import executeDialog from './executeDialog'

export default {
  name: 'ActConfigVersion',
  components: {
    codeDiff,
    executeDialog
  },
  data: function () {
    return {
      fromProps: {
        actId: '',
        dataBase: "",
      },
      selectActList: [],
      selectDataBaseList: [
        {
          code: 'hdzt',
          desc: '中台'
        },
        {
          code: 'gameecology',
          desc: '中控'
        },
        {
          code: 'stream',
          desc: 'Stream'
        },
        {
          code: 'currency',
          desc: '货币'
        },
        {
          code: 'shop',
          desc: '商城'
        },
        {
          code: 'template',
          desc: '交友强厅'
        }
      ],
      page: {
        total: 0,
        current: 1,
        pageSize: 5,
        pageSizeOpts: [1, 5, 10, 15, 20, 25],
        tableData: [],
        multipleSelection: []
      },
      tableData: [],
      canCompare: false,
      configComparer: false,
      synToDeployData: {
        models: [
          {
            key: "global",
            name: "全局数据同步"
          },
          {
            key: "presuppose",
            name: "预设数据同步（灰度状态）"
          },
          {
            key: "actConfig",
            name: "活动数据同步"
          }
        ]
      },
      coverData: {
        models: [
          {
            key: "actConfig",
            name: "活动数据"
          }
        ],
        value: {}

      },
    }
  },

  created: function () {
    this.loadActInfos()
  },
  watch: {
    tableData: function (val) {
      this.page.total = this.tableData.length;
      this.page.current = 1;
      this.changePage();
    }
  },
  methods: {
    handleCreate(val) {
      debugger
      let actId = val;
      if (!/^\d+$/g.test(val) && !/^.+\(\d+\)$/g.test(val)) {
        this.$Message.error("活动id添加失败");
        return;
      }
      let actName = val;
      if (/^.+\(\d+\)$/g.test(val)) {
        actName = val.replace(/\(\d+\)/g, "");
        actId = val.replace(actName, "").replace(/\(|\)/g, "");
      }
      for (let i in this.selectActList) {
        if (this.selectActList[i].act_id === actId) {
          return;
        }
      }
      this.selectActList.unshift({
        act_id: actId,
        act_name: actName
      });
    },
    //加载活动数据
    loadActInfos() {
      Api.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    // 查询所有版本
    queryAllVersions() {
      const self = this;
      const fromProps = this.fromProps;
      if (fromProps.actId === "" || fromProps.actId == null) {
        return;
      }
      if (fromProps.dataBase === "" || fromProps.dataBase == null) {
        return;
      }
      this.$Loading.start();
      Api.queryActConfigVersions(fromProps.actId, fromProps.dataBase).then(
        res => {
          if (res.result === 200) {
            this.tableData = res.data;
            self.$Loading.finish();
          } else {
            this.tableData = [];
            alert("请求配置版本错误," + res.reason);
          }
        }
      ).catch((error) => {
        this.tableData = [];
        this.$Message.error(error.status + "," + error.statusText, 5);
      })
    },

    showCompareButton() {
      this.canCompare =  this.page.multipleSelection.length === 2;
    },
    handleSizeChange(val) {
      this.page.pageSize = val
    },
    changePage() {
      const current = this.page.current;
      const pageSize = this.page.pageSize;
      const start = (current - 1) * pageSize;
      let end = (current) * pageSize;
      end = end > this.tableData.length ? this.tableData.length : end;
      this.page.tableData = this.tableData.slice(start, end);
      this.page.multipleSelection = []
      this.showCompareButton();
    },

    showComparer() {
      const select = this.page.multipleSelection;
      if (select.length !== 2) {
        return
      }
      const id1 = select[0].id;
      const id2 = select[1].id;
      const fromProps = this.fromProps;
      const self = this
      let loadingInstance = this.$loading({body: true})
      Api.queryCompare(fromProps.actId, fromProps.dataBase, id1, id2).then(
        res => {
          if (res.result === 200) {
            this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
              loadingInstance.close();
            });
            self.$refs.diff.open(res.data[0].versionDesc, res.data[0].config, res.data[1].versionDesc, res.data[1].config, res.data[0].id === 0)
          } else {
            this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
              loadingInstance.close();
            });
            alert("拉取数据错误," + res.reason)
          }
        }
      ).catch(() => {
        this.$nextTick(() => { // 以服务的方式调用的 Loading 需要异步关闭
          loadingInstance.close();
        });
      })
    },

    handleSelect(row) {
      this.page.multipleSelection = row
      this.showCompareButton();
    },
    // 备份
    backup() {
      const self = this;
      if (!self.checkParam()) {
        return;
      }
      this.$Modal.confirm({
        render: (h) => {
          return h('Input', {
            props: {
              value: "",
              autofocus: true,
              placeholder: '请输入版本说明'
            },
            on: {
              input: (val) => {
                this.versionDesc = val;
              }
            }
          })
        },
        onOk: () => {
          const fromProps = self.fromProps
          Api.backup(fromProps.actId, fromProps.dataBase, this.versionDesc).then(
            res => {
              debugger
              if (res.result === 200) {
                this.$Message.success('备份成功');
              } else {
                self.$Loading.error();
                this.$Message.error({content: "备份失败," + res.reason, duration: 5, closable: true});
              }
            }
          )/*.catch((error) => {
            self.$Loading.error();
            this.$Message.error(error.status + "," + error.statusText, 5);
          })*/
        },
        onCancel: () => {
          this.versionDesc = ""
        }
      })
    },
    recoverPreview(editor, value) {
      const self = this;
      const fromProps = this.fromProps;
      let param = {
        'actId': fromProps.actId,
        'dataBase': fromProps.dataBase,
        'execute': false,
        'config': value
      }
      const diffDialog = this.$refs.diffDialog
      diffDialog.openWait(1)
      Api.recoverActConfig(param)
        .then(
          res => {
            if (res.result === 200) {
              self.coverData.value = value
              const data = self.toExecuteDialogData(res.data, self.coverData.models, '覆盖')
              diffDialog.setPreViewData(data)
            } else {
              diffDialog.setError(res.reason)
            }
          }
        ).catch(() => {
      })
    },
    recoverConfig() {
      const self = this;
      const fromProps = this.fromProps;
      let param = {
        'actId': fromProps.actId,
        'dataBase': fromProps.dataBase,
        'execute': true,
        'config': self.coverData.value
      }
      const diffDialog = this.$refs.diffDialog
      diffDialog.openWait(2)
      Api.recoverActConfig(param)
        .then(
          res => {
            if (res.result === 200) {
              const data = self.toExecuteDialogData(res.data, self.coverData.models, '覆盖')
              diffDialog.setExecuteData(data)
            } else {
              diffDialog.setError(res.reason)
            }
          }
        ).catch(() => {
      })
    },

    //上线
    synToDeploy(exceute) {
      const self = this;
      if (!self.checkParam()) {
        return;
      }
      const fromProps = this.fromProps;
      const currentStep = !exceute ? 1 : 2;
      const synDialog = this.$refs.synDialog
      synDialog.openWait(currentStep)
      Api.synToDeploy(fromProps.actId, fromProps.dataBase, exceute ? 1 : 0).then(
        res => {
          if (res.result === 200) {
            const data = self.toExecuteDialogData(res.data, self.synToDeployData.models, '同步')
            !exceute ? synDialog.setPreViewData(data) : synDialog.setExecuteData(data)
          } else {
            synDialog.setError(res.reason)
          }
        }
      )
      this.configComparer = true;
    },
    checkParam() {
      const fromProps = this.fromProps
      if (fromProps.actId === "" || fromProps.actId == null) {
        this.$Message.warning("请选择活动")
        return false;
      }
      if (fromProps.dataBase === "" || fromProps.dataBase == null) {
        this.$Message.warning("请选择数据库")
        return false;
      }
      return true
    },
    toExecuteDialogData(data, models, op) {
      let dialogData = {}
      for (const i in models) {
        const modelKey = models[i].key
        const dataItems = data[modelKey]
        const dialogDataItems = []
        for (const j in dataItems) {
          const dataItem = dataItems[j]
          const dialogItem = {
            'key': modelKey + "." + dataItem.tableName,
            'title': op + dataItem.tableName + '表，预计执行' + dataItem.predictCount + '，成功执行' + dataItem.successCount,
            'contexts': dataItem.sqls
          }
          dialogDataItems.push(dialogItem)
        }
        dialogData[modelKey] = dialogDataItems
      }
      return dialogData
    }
  }
}
</script>

<style lang="scss" scoped>

</style>
