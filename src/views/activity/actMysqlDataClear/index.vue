<template>
  <div class="app-container">
    <div style="height: 800px;">
      <div>
        <h3>说明</h3>
        <p style="color: #722ed1;">
          清理数据步骤：
          <br/>1、【redis数据清理】清除虚拟时间（防止有新的数据进来）
          <br/>2、【mysql数据清理】清除mysql数据（防止报名数据重新同步到redis）
          <br/>3、【redis数据清理】清除redis数据
          <br/>4、【mysql数据清理】清除灰度标识
        </p>
        <p>
        <h5>中台数据-报名数据</h5>
        <label style="color: #2b85e4;">ranking_enrollment：</label>
        <label style="color: red">需要确保MySQL的报名数据在Redis之前清除</label>
        </p>
        <p>
        <h5>中台数据-抽发奖</h5>
        <p><label style="color: #2b85e4">award_model：</label>恢复consumed字段为0</p>
        <p><label
          style="color: #2b85e4">award_record、award_issue、award_daily_cost、ranking_task_award_issue、award_consume：</label>删除
        </p>
        </p>
        <p>
        <h5>中控数据-抽发奖</h5>
        <p><label
          style="color: #2b85e4">ge_award_record、act_task_award_record：</label>删除
        </p>
        </p>
        <p>
        <h5>中控数据-荣耀殿堂</h5>
        <p><label
          style="color: #2b85e4">act_result：</label>恢复member_id字段为空
        </p>
        </p>

        <p>
        <h5>灰度标识<label style="color: red;">(最后清除)</label></h5>
        <p><label
          style="color: #2b85e4">中台灰度标识：</label>hdzt_activity_attr表award_grey_status和activity_grey_status重置为0
        </p>
        <p><label
          style="color: #2b85e4">中控灰度标识：</label>ge_parameter表activity_grey_env_{活动id}和ge_act_attr表activity_grey_status重置为0
        </p>
        </p>
        <br/>
        <label style="color: red;font-weight: bold">Remark:上述所有的数据清除，都会以活动id作为数据筛选范围</label>
        <br/>
        <br/>
      </div>

      <Form :label-width="90" :model="formItem" inline>
        <FormItem label="活动ID" prop="actId">
          <Input v-model="formItem.actId" placeholder="Enter actId" required="true" style="width: 184px"/>
        </FormItem>
        <FormItem>
          <Button :loading="cleanAlling" type="primary" @click="showAllMedal()" style="margin-left: 8px">清除灰度数据</Button>
          <Button :loading="cleanGreyFlaging" type="primary" @click="showGreyFlagMedal()" style="margin-left: 8px">
            清除灰度标识
          </Button>
        </FormItem>
      </Form>

      <Modal
        v-model="isShowAllMedal"
        title="清除确认"
        @on-ok="cleanGreyData">
        <p>确认一键清除活动 {{formItem.actId}} 的灰度数据?</p>
      </Modal>

      <Modal
        v-model="isShowGreyFlagMedal"
        title="清除确认"
        @on-ok="cleanGreyStatus">
        <p>确认清除活动 {{formItem.actId}} 对应的灰度标识?请确保其他灰度数据已清理完毕</p>
      </Modal>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/actMysqlDataClear'
  export default {
    name: "ActMysqlDataClear",
    components: {},
    data: function () {
      return {
        formItem: {
          actId: ''
        },
        cleanAlling: false,
        cleanGreyFlaging: false,
        isShowAllMedal: false,
        isShowGreyFlagMedal: false
      }
    },
    methods: {
      showAllMedal() {
        const self = this;
        if (!this.checkActId()) {
          return
        }
        self.isShowAllMedal = true
      },
      showGreyFlagMedal() {
        const self = this
        if (!this.checkActId()) {
          return
        }
        self.isShowGreyFlagMedal = true
      },
      checkActId() {
        const self = this
        const formItem = self.formItem
        if (formItem.actId === ""
          || formItem.actId == null) {
          this.$Message.warning("请输入活动ID")
          return false;
        }
        if (isNaN(formItem.actId)
          || formItem.actId <= 0) {
          this.$Message.warning("输入的活动ID有误,请检查")
          return false
        }

        return true
      },
      cleanGreyData() {
        if (!this.checkActId()) {
          return
        }
        const self = this
        const formItem = self.formItem
        self.cleanAlling = true;
        Api.cleanGreyData(formItem.actId).then(
          res => {
            self.cleanAlling = false;
            if (res.result === 200) {
              this.$Message.success("清除成功")
            } else {
              this.$Message.warning(res.reason, 5)
            }
          }
        )
      },
      cleanGreyStatus() {
        if (!this.checkActId()) {
          return
        }
        const self = this
        const formItem = self.formItem
        self.cleanGreyFlaging = true;
        Api.cleanGreyStatus(formItem.actId).then(
          res => {
            self.cleanGreyFlaging = false;
            if (res.result === 200) {
              this.$Message.success("清除成功")
            } else {
              this.$Message.warning(res.reason, 5)
            }
          }
        )
      }
    }
  }
</script>
