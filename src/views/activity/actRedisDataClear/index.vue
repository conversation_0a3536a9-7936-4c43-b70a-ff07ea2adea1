<template>
  <div class="app-container">
    <div>
      <h3>说明</h3>
      <p style="color: #722ed1;">
        清理数据步骤：
        <br/>1、【redis数据清理】清除虚拟时间（防止有新的数据进来）
        <br/>2、【mysql数据清理】清除mysql数据（防止报名数据重新同步到redis）
        <br/>3、【redis数据清理】清除redis数据
        <br/>4、【mysql数据清理】清除灰度标识
      </p>

      <Row>
        <Col span="12" style="padding-bottom: 0px;!important;">
          <Tag color="success">Redis数据清理说明</Tag>
          <br/>
          <p><label style="color: rebeccapurple;font-weight: bold">group分组：</label>根据活动id读取数据库配置的分组,包括活动级别&榜单级别</p>
          <p><label style="color: rebeccapurple;font-weight: bold">数据清除范围：</label>每个数据主题对应到一个清除key前缀，该前缀+活动id,则限定了最后清除的数据范围,
            <br/>
            如：发奖流水前缀为"hdzt_award:welfare_async_issue:" <br/>
            加上活动id：2021103002，则最后会使用模式：hdzt_award:welfare_async_issue:*2021103002* <br/>
            使用scan命令从redis从匹配满足条件的key
          </p>
          <p>
            <label style="color: rebeccapurple;font-weight: bold">keys个数：</label>所有满足模式的key的数量
          </p>
          <p>
            <label style="color: rebeccapurple;font-weight: bold">key详情：</label>只会展示前100个
          </p>
          <p>
            <label style="color: rebeccapurple;font-weight: bold">一键清理：</label>同时删除中台抽发奖流水、中台榜单累榜数据和报名信息、中控组件标识、中控Stream缓存
          </p>
          <p>
            <label style="color: rebeccapurple;font-weight: bold">检查缓存数据：</label>展示所有主题的缓存数量
          </p>
          <p>
            <label style="color: red;font-weight: bold">特别注意：</label>删除报名信息时需要Mysql先行,否则缓存可能重新从数据库加载
          </p>
          <br/>
        </Col>
        <Col span="12" style="padding-bottom: 0px;!important;">
          <Tag color="success">Mysql数据清理说明</Tag>
          <br/>
          <p>
          <h5>中台数据-报名数据</h5>
          <label style="color: #2b85e4;">ranking_enrollment：</label>
          <label style="color: red">需要确保MySQL的报名数据在Redis之前清除</label>
          <p>
          <h5>中台数据-抽发奖</h5>
          <p><label style="color: #2b85e4">award_model：</label>恢复consumed字段为0</p>
          <p><label
            style="color: #2b85e4">award_record、award_issue、award_daily_cost、ranking_task_award_issue、award_consume：</label>删除
          </p>

          <p>
          <h5>中控数据-抽发奖</h5>
          <p><label
            style="color: #2b85e4">ge_award_record、act_task_award_record：</label>删除
          </p>

          <p>
          <h5>中控数据-荣耀殿堂</h5>
          <p><label
            style="color: #2b85e4">act_result：</label>恢复member_id字段为空
          </p>
          <p>
          <h5>灰度标识<label style="color: red;">(最后清除)</label></h5>
          <p><label
            style="color: #2b85e4">中台灰度标识：</label>hdzt_activity_attr表award_grey_status和activity_grey_status重置为0
          </p>
          <p><label
            style="color: #2b85e4">中控灰度标识：</label>ge_parameter表activity_grey_env_{活动id}和ge_act_attr表activity_grey_status重置为0
          </p>
          <p>
            <label style="color: rebeccapurple;font-weight: bold">检查数据库数据：</label>展示所有待清理表的数据量或对应的值,灰度状态值为-1表示没有设置过
          </p>
          <label style="color: red;font-weight: bold">Remark:上述所有的数据清除，都会以活动id作为数据筛选范围</label>
          <br/>
          <br/>
        </Col>
      </Row>
    </div>
    <Row>
      <Tag color="success">Redis数据清理</Tag>
      <br/>
      <br/>
      <Form :label-width="82" :model="formItem" inline>
        <FormItem label="活动ID" prop="actId">
          <Input v-model="formItem.actId" placeholder="Enter actId" required="true" style="width: 184px"/>
        </FormItem>

        <FormItem label="数据源" prop="selectDataSource">
          <Select v-model="formItem.selectDataSource" style="width:200px" @on-change="loadDataScope">
            <Option v-for="item in formItem.dataSource" :value="item.code" :key="item.code">[{{item.code}}]{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <FormItem label="数据主题" prop="selectDataScope">
          <Select v-model="formItem.selectDataScope" style="width:200px">
            <Option v-for="item in formItem.dataScope" :value="item.code" :key="item.code">[{{item.code}}]{{ item.desc }}</Option>
          </Select>
        </FormItem>

        <!--
        <FormItem label="group分组" prop="groupCode">
          <Input v-model="formItem.groupCode" placeholder="Enter groupCode" required="false" style="width: 184px"/>
        </FormItem>
        -->

        <FormItem>
          <Button :loading="previewing" type="primary" @click="preview()">预览</Button>
          <Button :loading="cleaning" type="primary" @click="showMedal()" style="margin-left: 8px">清除</Button>
          <Button :loading="cleanAlling" type="primary" @click="showAllMedal()" style="margin-left: 8px">一键清除</Button>
          <Button :loading="previewAlling" type="primary" @click="previewAll()">检查缓存数据</Button>
        </FormItem>

        <div v-if="isPreview">
          <div>
            <FormItem label="keys个数" prop="previewData.keySize">
              <Input v-model="formItem.previewData.keySize" style="width: 800px"/>
            </FormItem>
          </div>

          <div v-if="formItem.previewData.msg !== undefined && formItem.previewData.msg != ''">
            <FormItem label="提示" prop="previewData.msg" style="color: red !important;">
              <p style="width: 800px;color:red!important;">
                {{ formItem.previewData.msg }}
              </p>
            </FormItem>
          </div>
          <div>
            <FormItem label="key详情">
              <Input type="textarea" v-model="formItem.previewData.keys" :autosize="true" style="width: 800px"/>
            </FormItem>
          </div>
        </div>
      </Form>
      <Modal
        v-model="isShowMedal"
        title="清除确认"
        @on-ok="clean">
        <p>确认清除该Redis灰度数据?</p>
      </Modal>

      <Modal
        v-model="isShowAllMedal"
        title="清除确认"
        @on-ok="cleanAll">
        <p>确认一键清除该活动的Redis灰度数据?</p>
      </Modal>

      <Modal v-model="isPreviewRedisAll"
             title="数据汇总"
             @on-ok="isPreviewRedisAll = false"
             @on-cancel="isPreviewRedisAll = false">
        <table style="font-family: verdana, arial, sans-serif;
              font-size: 11px;
              color: #333333;
              border-width: 1px;
              border-color: #666666;
              border-collapse: collapse;">
          <tr>
            <td
              style=" border-width: 1px;padding: 8px;border-style: solid;border-color: #666666;background-color: #dedede;">
              数据主题
            </td>
            <td
              style=" border-width: 1px;padding: 8px;border-style: solid;border-color: #666666;background-color: #dedede;">
              缓存数量
            </td>
          </tr>
          <tr v-for="(val,key,index) in redisAllData" :key="index">
            <td
              style=" border-width: 1px;padding: 8px;border-style: solid;border-color: #666666;background-color: #dedede;">
              {{ key }}
            </td>
            <td
              style=" border-width: 1px;padding: 8px;border-style: solid;border-color: #666666;background-color: #dedede;">
              {{ val }}
            </td>
          </tr>
        </table>
      </Modal>
    </Row>
    <Divider/>
    <Row>
      <Tag color="success">Mysql数据清理</Tag>
      <br/>
      <br/>
      <Form :label-width="90" :model="formItem" inline>
        <FormItem label="活动ID" prop="actId">
          <Input v-model="formItem.actId" placeholder="输入活动id" required="true" style="width: 184px"/>
        </FormItem>
        <FormItem>
          <Button :loading="cleanMysqlAlling" type="primary" @click="showMysqlAllMedal()" style="margin-left: 8px">
            清除灰度数据
          </Button>
          <Button :loading="cleanGreyFlaging" type="primary" @click="showGreyFlagMedal()" style="margin-left: 8px">
            清除灰度标识
          </Button>

          <Button :loading="previewMysqlAlling" type="primary" @click="previewMysqlAll()" style="margin-left: 8px">
            检查数据库数据
          </Button>
        </FormItem>
      </Form>

      <Modal
        v-model="isShowMysqlAllMedal"
        title="清除确认"
        @on-ok="cleanGreyData">
        <p>确认一键清除活动 {{ formItem.actId }} 的Mysql灰度数据?</p>
      </Modal>

      <Modal
        v-model="isShowGreyFlagMedal"
        title="清除确认"
        @on-ok="cleanGreyStatus">
        <p>确认清除活动 {{ formItem.actId }} 对应的灰度标识?请确保其他灰度数据已清理完毕</p>
      </Modal>

      <Modal v-model="isPreviewMysqlAll"
             title="数据汇总"
             @on-ok="isPreviewMysqlAll = false"
             @on-cancel="isPreviewMysqlAll = false">
        <table style="font-family: verdana, arial, sans-serif;
              font-size: 11px;
              color: #333333;
              border-width: 1px;
              border-color: #666666;
              border-collapse: collapse;">
          <tr>
            <td
              style=" border-width: 1px;padding: 8px;border-style: solid;border-color: #666666;background-color: #dedede;">
              数据主题
            </td>
            <td
              style=" border-width: 1px;padding: 8px;border-style: solid;border-color: #666666;background-color: #dedede;">
              数值
            </td>
          </tr>
          <tr v-for="(val,key,index) in mysqlAllData" :key="index">
            <td
              style=" border-width: 1px;padding: 8px;border-style: solid;border-color: #666666;background-color: #dedede;">
              {{ key }}
            </td>
            <td
              style=" border-width: 1px;padding: 8px;border-style: solid;border-color: #666666;background-color: #dedede;">
              {{ val }}
            </td>
          </tr>
        </table>
      </Modal>
    </Row>
    <Divider/>
  </div>
</template>

<script>
import Api from '@/api/activity/actRedisDataClear'
import MysqlApi from '@/api/activity/actMysqlDataClear'

export default {
  name: 'ActRedisDataClear',
  data: function () {
    return {
      formItem: {
        actId: '',
        groupCode: '',
        dataScope: [],
        dataSource: [{
          code: 'ZTRanking',
          desc: '中台榜单'
        }, {
          code: 'ZTAward',
          desc: '中台抽发奖'
        }, {
          code: 'Ecology',
          desc: '中控Ecology'
        }, {
          code: 'Stream',
          desc: '中控Stream'
        }],
        selectDataSource: '',
        selectDataScope: '',
        previewData: {}
      },
      previewing: false,
      cleaning: false,
      cleanAlling: false,
      previewAlling: false,

      isPreview: false,
      isShowMedal: false,
      isShowAllMedal: false,

      cleanMysqlAlling: false,
      cleanGreyFlaging: false,
      isShowMysqlAllMedal: false,
      isShowGreyFlagMedal: false,

      isPreviewRedisAll: false,
      redisAllData: {},
      isPreviewMysqlAll: false,
      previewMysqlAlling: false,
      mysqlAllData: {},
    }
  },

  methods: {
    loadDataScope() {
      const self = this
      const formItem = self.formItem
      formItem.selectDataScope = ''
      Api.loadDataScope(formItem.selectDataSource).then(
        res => {
          console.log(res)
          if (res.result === 200) {
            formItem.dataScope = res.data
          }
        }
      ).fail((error) => {
        alert(error.status + "," + error.statusText)
      })
    },
    // 预览
    preview() {
      const self = this;
      const formItem = self.formItem;
      if (!this.checkParam(false)) {
        return
      }

      let param = "actId=" + formItem.actId + "&groupCode=" + formItem.groupCode + "&dataScope=" + formItem.selectDataScope;
      formItem.previewData = {};
      self.previewing = true;
      Api.preview(param).then(
        res => {
          self.previewing = false;
          if (res.result === 200) {
            formItem.previewData = res.data
            self.isPreview = true
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      ).fail((error) => {
        this.$Message.error(error.status + "," + error.statusText, 5)
      })
    },
    // 清理
    clean() {
      const self = this;
      const formItem = self.formItem;
      if (!this.checkParam(false)) {
        return
      }
      if (formItem.previewData.token === undefined || formItem.previewData.token === "") {
        if (formItem.previewData.keySize !== undefined && formItem.previewData.keySize <= 0) {
          this.$Message.warning("本次预览无删除数据")
        } else {
          this.$Message.warning("请先进行数据预览")
        }

        return
      }
      self.isShowMedal = false;
      let param = {
        "actId": formItem.actId,
        "dataScope": formItem.selectDataScope,
        "token": formItem.previewData.token,
        "groupCode": formItem.groupCode,
      };
      self.cleaning = true;
      Api.clean(JSON.stringify(param)).then(
        res => {
          self.cleaning = false;
          if (res.result === 200) {
            formItem.previewData = {};
            this.$Message.success("清除成功")
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      ).fail((error) => {
        this.$Message.error(error.status + "," + error.statusText, 5);
      })
    },
    checkParam(isAll) {
      const self = this
      const formItem = self.formItem
      if (formItem.actId === ""
        || formItem.actId == null) {
        this.$Message.warning("请输入活动ID")
        return false;
      }
      if (isNaN(formItem.actId)
        || formItem.actId <= 0) {
        this.$Message.warning("输入的活动ID有误,请检查")
        return false
      }

      if (formItem.groupCode !== "" && isNaN(formItem.actId)) {
        this.$Message.warning("输入的group分组有误,请检查")
        return false
      }

      if (!isAll) {
        if (formItem.selectDataScope === '' || formItem.selectDataScope === undefined) {
          this.$Message.warning("请选择要清除的数据主题")
          return false
        }
      }
      return true
    },
    showMedal() {
      const self = this;
      self.isShowMedal = true;
    },
    showAllMedal() {
      const self = this;
      self.isShowAllMedal = true;
    },
    cleanAll() {
      const self = this;
      const formItem = self.formItem;
      if (!this.checkParam(true)) {
        return
      }
      self.isShowAllMedal = false;
      let param = {
        "actId": formItem.actId,
        "groupCode": formItem.groupCode,
      }
      self.cleanAlling = true
      Api.cleanAll(JSON.stringify(param)).then(
        res => {
          self.cleanAlling = false;
          if (res.result === 200) {
            this.$Message.success("清除成功")
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      ).fail((error) => {
        this.$Message.error(error.status + "," + error.statusText, 5)
      })
    },

    previewAll() {
      const self = this;
      const formItem = self.formItem;
      if (!this.checkParam(true)) {
        return
      }
      let param = {
        "actId": formItem.actId,
        "groupCode": formItem.groupCode,
      }
      self.previewAlling = true
      Api.scanKeys(JSON.stringify(param)).then(
        res => {
          self.previewAlling = false;
          self.redisAllData = res.data
          self.isPreviewRedisAll = true
        }
      )
    },

    // Mysql
    showMysqlAllMedal() {
      const self = this;
      if (!this.checkActId()) {
        return
      }
      self.isShowMysqlAllMedal = true
    },
    showGreyFlagMedal() {
      const self = this
      if (!this.checkActId()) {
        return
      }
      self.isShowGreyFlagMedal = true
    },
    checkActId() {
      const self = this
      const formItem = self.formItem
      if (formItem.actId === ""
        || formItem.actId == null) {
        this.$Message.warning("请输入活动ID")
        return false;
      }
      if (isNaN(formItem.actId)
        || formItem.actId <= 0) {
        this.$Message.warning("输入的活动ID有误,请检查")
        return false
      }

      return true
    },
    cleanGreyData() {
      if (!this.checkActId()) {
        return
      }
      const self = this
      const formItem = self.formItem
      self.cleanAlling = true;
      MysqlApi.cleanGreyData(formItem.actId).then(
        res => {
          self.cleanAlling = false;
          if (res.result === 200) {
            this.$Message.success("清除成功")
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      ).fail((error) => {
        this.$Message.error(error.status + "," + error.statusText, 5);
      })
    },
    cleanGreyStatus() {
      if (!this.checkActId()) {
        return
      }
      const self = this
      const formItem = self.formItem
      self.cleanGreyFlaging = true;
      MysqlApi.cleanGreyStatus(formItem.actId).then(
        res => {
          self.cleanGreyFlaging = false;
          if (res.result === 200) {
            this.$Message.success("清除成功")
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      ).fail((error) => {
        this.$Message.error(error.status + "," + error.statusText, 5);
      })
    },
    previewMysqlAll() {
      const self = this;
      const formItem = self.formItem;
      if (!this.checkParam(true)) {
        return
      }
      let param = {
        "actId": formItem.actId,
        "groupCode": formItem.groupCode,
      }
      self.previewMysqlAlling = true
      Api.loadMysqlAll(JSON.stringify(param)).then(
        res => {
          self.previewMysqlAlling = false;
          self.mysqlAllData = res.data
          self.isPreviewMysqlAll = true
        }
      )
    },
  }
};
</script>
