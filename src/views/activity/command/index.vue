<template>
  <div class="app-container">

    <div>
      <h3>数据清除指令</h3>
      <br><br>
      <!-- <Form ref="form" :model="form" :label-width="150" :rules="ruleValidate">-->
      <Form ref="form" :model="form" :label-width="150" >

        <FormItem
          label="活动ID"
          prop="actId"
          :rules="{required: true, type:'number',trigger: 'blur'}">
          <Input v-model="form.actId" placeholder="enter actId" style="width: 200px" @on-blur="queryCommandListByActId()" number/>
        </FormItem>
        <FormItem label="指令名称" prop="command" >
          <Select v-model="form.command" style="width: 200px" >
            <Option v-for="item in list" :key="item.id" :value="item.id" @click.native="handleSelect(item)" >{{ item.commandName }}</Option>
          </Select>
        </FormItem>
        <FormItem
          v-for="(item,index) in form.inputs"
          :key="index"
          v-if="item.label!=='actId'&& item.label!=='command'"
          :prop="'inputs.' + index + '.value'"
          :rules="{required: true, message: '' + item.label +' can not be empty', trigger: 'blur'}"
          :label="item.label">
          <Input type="text" v-model="item.value" style="width: 200px"/>
        </FormItem>
        <FormItem>
          <Button :loading="loadingState" type="primary" @click="submit('form')" v-if="isshow">执行</Button>
        </FormItem>
      </Form>

    </div>



    <!--
    <FormItem
      v-for="(item, index) in form.items"
      v-if="item.status"
      :key="index"
      :label="'Item ' + item.index"
      :prop="'items.' + index + '.value'"
      :rules="{required: true, message: 'Item ' + item.index +' can not be empty', trigger: 'blur'}">

      <Input type="text" v-model="item.value" placeholder="Enter something..."></Input>
    </FormItem>
    <FormItem>
      <Button type="primary" @click="handleSubmit('form')">Submit</Button>

    </FormItem>-->

  </div>
</template>

<script>
  import Api from '@/api/activity/command'
  export default {
    name: "Command",
    components: {},
    data: function () {
      return {

        /* ruleValidate: {
           actId: [
             { required: true, message: 'This field cannot be empty', trigger: 'blur' }
           ],
         },*/
        form: {
          inputs:
            [
              {
                value: '',
                label: 'actId',
              },
            ],
        },
        list:{},
        map: {},
        inputs:[],
        isshow:false,
        loadingState:false,

      }
    },

    methods:{
      handleSubmit (name) {
        this.$refs[name].validate((valid) => {
          if (valid) {
            this.$Message.success('Success!');
          } else {
            this.$Message.error('Fail!');
          }
        })
      },
      queryCommandListByActId(){

        let reqData = {"actId":this.form.actId}
        Api.queryCommandListByActId(reqData).then(
          res => {
            this.list = res;
          }
        ).catch(() => {
          self.$Message.warning('error');
          this.$router.push('fail');
        });
      },
      handleSelect(item){
        this.map = item.paramMap;
        //将map转换为数组
        let arr=[];
        for (let key in this.map) {
          arr.push(
            {
              label:key,
              value:''
            }
          );
        }
        this.form.inputs=arr;
        //显示按钮
        this.isshow=true;
      },
      submit(name){

        this.$refs[name].validate((valid) => {
          if (valid) {
            this.submitToServer();
          } else {
            this.$Message.error('Fail!');
          }
        })

      },
      submitToServer(){
        //let reqData = "actId="+this.form.actId+"&command="+this.form.command;
        let reqData = {
          actId: this.form.actId,
          command: this.form.command,
        };
        for (let i=0;i<this.form.inputs.length;i++){
          let name=this.form.inputs[i].label;
          let value=this.form.inputs[i].value;
          if(name!=="actId" && name!=="command"){
            // reqData=reqData+'&'+name+'='+value;
            reqData[name] = value;
          }
        }
        //reqData = JSON.stringify(reqData);
        this.loadingState = true;
        Api.submitToServer(reqData).then(
          res => {
            alert(res.data);
            this.loadingState = false;
            //this.$router.push('success')
          }
        ).catch(() => {
          this.loadingState = false;
          this.$Message.warning('error');
          //this.$router.push('fail');
        });
      }
    }

  };
</script>
