<template>
  <div class="app-container">
    <div>
      该配置定义了挂件的一些属性,如状态转换,主播任务等.
      <Form inline :model="formItem">
        <FormItem>
          <span>活动ID：</span>
          <Input v-model="formItem.actId" style="width:200px"/>
        </FormItem>


        <Button :loading="loadingState" type="primary" @click="queryViewDefineAttr()"> 查询
        </Button>
        <Button :loading="loadingState" type="primary" @click="addTask()"> 新建
        </Button>

        <Button :loading="loadingState" type="primary" @click="copyConfig()"> 复制</Button>
      </Form>
      <br>

      <Modal :value="copyLayerState" width="600" :closable="false" :mask-closable="false">
        <span>该复制仅限于相关榜单结构的活动</span>
        <Form :label-width="100" :model="copyFormItem" ref="copyFormItem">
          <FormItem label="原活动ID" prop="srcActId">
            <Input  v-model="copyFormItem.srcActId" style="width:200px"/>
          </FormItem>
          <FormItem label="目标活动ID" prop="targetActId">
            <Input   v-model="copyFormItem.targetActId" style="width:200px"/>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button :loading="loadingCopyState" type="primary" @click="loadingCopyState=true; copyViewDefineAttr()"> 确认复制
          </Button>
          <Button @click="closeCopyModal()"> 取消</Button>
        </div>
      </Modal>


      <Modal :value="addLayerState" width="600" :closable="false" :mask-closable="false">
        <p slot="header" style="text-align:center">
          <span v-if="opt_type===1">添加挂件定义</span>
          <span v-if="opt_type===2">编辑挂件定义</span>
        </p>
        <Form :label-width="100" :model="editFormItem" ref="editFormItem" :rules="viewDefineAttrValidate">
          <FormItem label="活动ID" prop="actId">
            <Input v-if="opt_type===2" disabled v-model="editFormItem.actId" style="width:200px"/>
            <Input v-else v-model="editFormItem.actId" style="width:200px"/>
          </FormItem>

          <FormItem label="属性名">
            <Input v-model="editFormItem.attrName" style="width:200px"/>
          </FormItem>
          <FormItem label="属性值">
            <Input v-model="editFormItem.attrValue" style="width:200px"/>
          </FormItem>

          <FormItem label="备注">
            <Input v-model="editFormItem.remark" style="width:200px"/>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button :loading="loadingState" type="primary" @click="loadingState=true;addViewDefineAttr('editFormItem')"
                  v-if="opt_type==1">
            确认添加
          </Button>
          <Button :loading="loadingState" type="primary"
                  @click="loadingState=true;updateViewDefineAttrWithValid('editFormItem')"
                  v-if="opt_type==2">
            确认修改
          </Button>
          <Button @click="closeModal()">
            取消
          </Button>
        </div>
      </Modal>


      <Table  :columns="columns" :data="viewDefineAttrList"></Table>
      <br>

    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/viewDefineAttr'
  export default {
    name: "ViewDefineAttr",
    components: {},
    data: function () {
      return {
        viewDefineAttrList: [],
        columns: [{
          title: '活动ID',
          width: 180,
          key: 'actId'
        }, {
          title: '属性名',
          width: 250,
          key: 'attrName'
        }, {
          title: '属性值',
          width: 250,
          key: 'attrValue',
        }, {
          title: '备注',
          width: 250,
          key: 'remark',

        }, {
          title: '操作',
          align: 'center',
          width: 180,
          render: (h, params) => {
            let update = h('Button', {
              style: {
                marginLeft: '5px'
              },
              props: {
                type: 'primary'
              },
              on: {
                click: () => {
                  this.editItem(params.index)
                }
              }
            }, '编辑');
            return h('p', [update])
          }
        }],


        formItem: {},
        editFormItem: {},

        loadingState: false,
        loadingStatus: false,
        opt_type: 1,
        addLayerState: false,
        viewDefineAttrValidate: {
          actId: [{required: true, message: '不可为空', trigger: 'blur'}],
          attrName: [{required: true, message: '不可为空', trigger: 'blur'}],
          attrValue: [{required: true, message: '不可为空', trigger: 'blur'}],
          remark: [{required: true, message: '不可为空', trigger: 'blur'}],
        },
        copyFormItem: {},
        copyLayerState: false,
        loadingCopyState: false,
      }

    },

    methods: {
      queryViewDefineAttr() {
        const self = this;
        let formItem = this.formItem
        let reqData = formItem
        self.loadingState = true;
        Api.queryViewDefineAttr(reqData).then(res => {
          if (res.result === '0') {
            this.viewDefineAttrList = res.data
          } else {
            self.$Message.warning("查询失败:" + res.data)
          }
        });
        self.loadingState = false;
      },

      editItem: function (index) {
        debugger
        const self = this
        let data = self.viewDefineAttrList[index]
        console.log(JSON.stringify(data))
        self.setEditData(data)
      },
      // 父组件触发编辑操作，设置表单数据
      setEditData(data) {
        const self = this
        self.editFormItem = data
        self.opt_type = 2
        self.addLayerState = true
      },
      closeModal() {
        this.addLayerState = false
        this.loadingState = false
      },

      closeCopyModal() {
        this.copyLayerState = false
        this.loadingCopyState = false
      },

      addTask() {
        this.editFormItem = {};
        this.opt_type = 1;
        this.addLayerState = true;
      },

      copyConfig() {
        this.copyFormItem = {
          srcActId: this.formItem.actId,
          targetActId: ''
        };
        this.copyLayerState = true;
      },

      addViewDefineAttr(name) {
        const self = this
        let reqData = self.editFormItem
        let flag = false;
        this.$refs[name].validate((valid) => {
          if (valid) {
            flag = true;
          }
        });
        if (!flag) {
          this.loadingState = false
          return;
        }

       // reqData = JSON.stringify(reqData);
        Api.addViewDefineAttr(reqData).then(res => {
          if (res.result == '0') {
            self.$Message.success("添加成功:" + res.data);
          } else {
            alert(("添加失败:" + res.data));
          }
        });

        this.addLayerState = false
        this.loadingState = false,
          this.queryViewDefineAttr()
      },

      updateViewDefineAttrWithValid(name) {
        this.$refs[name].validate().then(() => {
          this.updateViewDefineAttr();
          this.editFormItem = {};
          this.queryViewDefineAttr()
        }).catch(() => {
          this.loadingState = false
          this.editFormItem = {};
          this.queryViewDefineAttr()
        });

      },

      updateViewDefineAttr() {
        debugger
        const self = this
        let reqData = self.editFormItem;

        //reqData = JSON.stringify(reqData);
        console.log('reqdata->' + JSON.stringify(reqData))
        Api.updateViewDefineAttr(reqData).then(res => {
          if (res.result === '0') {
            self.$Message.success("修改成功:" + res.data);
          } else {
            alert(("修改失败:" + res.data));
          }
        });
        this.addLayerState = false
        this.loadingState = false
      },

      copyViewDefineAttr(){
        const self = this
        let reqData = self.copyFormItem;

        //reqData = JSON.stringify(reqData);
        console.log('reqdata->' + JSON.stringify(reqData))
        Api.copyViewDefineAttr(reqData).then(res => {
          if (res.result === '0') {
            self.$Message.success("修改成功:" + res.data);
            this.formItem.actId = self.copyFormItem.targetActId;
            this.queryViewDefineAttr();
          } else {
            alert(("修改失败:" + res.data));
          }
        });
        this.copyLayerState = false
        this.loadingCopyState = false
      }

    }
  };
</script>
