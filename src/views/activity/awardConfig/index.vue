<template>
  <div class="app-container">

    <div>
      <el-container>
        <el-header class="header">
          <el-row>
            <el-form :inline="true" :model="searchForm">
              <el-form-item label="活动id">
                <el-select v-model="searchForm.actId" placeholder="请选择活动"
                           filterable style="width:300px">
                  <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                             :label='item.act_name+"("+item.act_id+")"'>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-button type="primary" :loading="loadingTableData" @click="onSearch" icon="el-icon-search"
                         size="medium">
                查询
              </el-button>
              <el-button type="primary" icon="el-icon-edit-outline" size="medium" @click="onEditAward(null)">添加奖池
              </el-button>
              <el-button type="primary" icon="el-icon-edit-download" size="medium" @click="onAwardToExcel()">下载配置excel
              </el-button>
              <el-tooltip class="item" effect="light" content="操作表:award_task" placement="right-start"
                          style="margin-left: 10px;">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-form>
          </el-row>
        </el-header>

        <el-main>
          <el-table :data="tableData" border row-key="taskId">
            <el-table-column prop="taskId" label="奖池id" align="center" width="100"></el-table-column>
            <el-table-column prop="actId" label="活动id" align="center" width="120"></el-table-column>
            <el-table-column prop="taskName" label="奖池名称" align="center" width="250"></el-table-column>
            <el-table-column prop="openTime" label="开放时间" align="center" width="160">
              <template slot-scope="scope">
                <span>{{ dateFormat(scope.row.openTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="endTime" label="结束时间" align="center" width="160">
              <template slot-scope="scope">
                <span>{{ dateFormat(scope.row.endTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="奖池状态" align="center" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.status === '0' ? '无效' : '有效' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="model" label="抽奖模型" align="center" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.model === '100' || scope.row.model === '101' ? '抽奖' : '发奖' }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" align="center" width="160">
              <template slot-scope="scope">
                <span>{{ dateFormat(scope.row.createTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="updateTime" label="更新时间" align="center" width="160">
              <template slot-scope="scope">
                <span>{{ dateFormat(scope.row.updateTime) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" fixed="right" align="center">
              <template slot-scope="scope">
                <el-link type="warning" @click="onCopy(scope.row)">
                  复制
                </el-link>
                <el-link type="success" @click="onEditAward(scope.row)" style="margin-left: 10px;">
                  编辑
                </el-link>
                <el-link type="primary" @click="handleAwardPackage(scope.row)" style="margin-left: 10px;"> 配置奖包
                </el-link>
              </template>
            </el-table-column>
          </el-table>

          <el-dialog title="奖池" :visible.sync="awardFormVisible"
                     :close-on-click-modal="false" @closed="onCloseAwardForm">
            <el-form :model="awardFormData" label-width="150px">

              <el-form-item label="活动id">
                <el-select v-model="awardFormData.actId" placeholder="请选择活动"
                           filterable style="width:100%;" :disabled="!awardFormData.add">
                  <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                             :label='item.act_name+"("+item.act_id+")"'>
                  </el-option>
                </el-select>

              </el-form-item>

              <el-form-item label="奖池名称">
                <el-input v-model="awardFormData.taskName" placeholder="请填写任务名称"></el-input>
              </el-form-item>

              <el-form-item label="奖池时间">
                <el-col :span="11">
                  <el-date-picker type="datetime" placeholder="选择开放时间" v-model="awardFormData.openTime"
                                  style="width: 100%;" value-format="timestamp"></el-date-picker>
                </el-col>
                <el-col style="text-align: center;" :span="2">--</el-col>
                <el-col :span="11">
                  <el-date-picker type="datetime" placeholder="选择结束时间" v-model="awardFormData.endTime"
                                  style="width: 100%;" value-format="timestamp"></el-date-picker>
                </el-col>
              </el-form-item>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="任务状态">
                    <el-switch v-model="awardFormData.status" active-value="1"
                               inactive-value="0"></el-switch>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="抽奖模型">
                    <el-select v-model="awardFormData.model" placeholder="请选择抽奖模型">
                      <el-option label="抽奖模型-固定概率(100)" value="100"></el-option>
                      <el-option label="抽奖模型-动态概率(101)" value="101"></el-option>
                      <el-option label="发奖模型2(201)" value="201"></el-option>
                      <el-option label="发奖模型-redis控制限额(202)" value="202"></el-option>
                      <el-option label="发奖模型-指定发放seq(203)" value="203"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-form-item label="奖池备注">
                <el-input type="textarea" v-model="awardFormData.remark"></el-input>
              </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
              <el-button @click="awardFormVisible = false">取 消</el-button>
              <el-button type="primary" @click="onSaveAward">保 存</el-button>
            </div>
          </el-dialog>

          <el-dialog title="复制奖池" :visible.sync="copyVisible" width="30%"
                     :close-on-click-modal="false" @closed="copyVisible = false">
            <el-form :model="copyItem" label-width="150px">

              <el-form-item label="奖池id">
                <el-input :disabled="true" v-model="copyItem.fromTaskId"
                          placeholder="请填写奖池id"></el-input>
              </el-form-item>

              <el-form-item label="活动id">
                <el-select v-model="copyItem.fromActId" placeholder="请选择活动"
                           filterable style="width:100%;" :disabled="true">
                  <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                             :label='item.act_name+"("+item.act_id+")"'>
                  </el-option>
                </el-select>

              </el-form-item>

              <el-form-item label="奖池名称">
                <el-input :disabled="true" v-model="copyItem.fromTaskName" placeholder="请填写任务名称"></el-input>
              </el-form-item>

              <!--
              <el-form-item label="新奖池id">
                <el-input v-model="copyItem.toTaskId" placeholder="请填写新奖池id"></el-input>
              </el-form-item>
              -->

              <el-form-item label="新活动id">
                <el-select v-model="copyItem.toActId" placeholder="请选择新活动"
                           filterable style="width:100%;">
                  <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                             :label='item.act_name+"("+item.act_id+")"'>
                  </el-option>
                </el-select>

              </el-form-item>

              <el-form-item label="新奖池名称">
                <el-input v-model="copyItem.toTaskName" placeholder="请填写新任务名称"></el-input>
              </el-form-item>
            </el-form>

            <div slot="footer" class="dialog-footer">
              <el-button @click="copyVisible = false">取 消</el-button>
              <el-button type="primary" @click="onSaveCopy">保 存</el-button>
            </div>
          </el-dialog>
        </el-main>

        <el-footer>

        </el-footer>
      </el-container>
    </div>

  </div>
</template>

<script>
import Api from '@/api/activity/awardConfig'
import ActConfigVersion from '@/api/activity/actConfigVersion'
import {parseTime} from "@/utils/systemUtils";

export default {
  name: "Award",
  components: {},
  data: function () {
    return {
      searchForm: {
        actId: ''
      },
      tableData: [],
      loadingTableData: false,
      awardFormVisible: false,
      awardFormData: {
        taskId: '',
        actId: '',
        taskName: '',
        openTime: '',
        endTime: '',
        status: '1',
        model: '100',
        remark: '',
        add: true
      },
      selectActList: [],
      copyVisible: false,
      copyItem: {
        fromTaskId: '',
        fromTaskName: '',
        fromActId: '',
        toTaskId: '',
        toTaskName: '',
        toActId: ''
      }
    }
  },
  created: function () {
    this.loadActInfos()
  },
  mounted: function () {
    console.log(this.$route.query)
    if (this.$route.query.actId === undefined) {
      return
    }
    this.searchForm.actId = this.$route.query.actId
    this.onSearch()
  },
  methods: {
    /** 奖包配置操作 */
    handleAwardPackage: function (row) {
      this.$router.push({path: 'awardPackage', query: {taskId: row.taskId, actId: row.actId}});
    },
    dateFormat: function (time) {
      if (!time) {
        return ''
      }
      return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');
    },
    // 根据活动id查询奖池
    onSearch: function () {
      const self = this
      if (self.searchForm.actId === '') {
        self.tableData = []
        return;
      }
      if (!this.checkActId(self.searchForm.actId)) {
        return
      }
      self.loadingTableData = true
      Api.listAwardTask(self.searchForm.actId).then(
        res => {
          if (res.result === 200) {
            self.tableData = res.data
          } else {
            this.$Message.warning(res.reason, 5)
          }
          self.loadingTableData = false
        }
      ).catch((error) => {
        this.$Message.error(error.status + "," + error.statusText, 5)
        self.loadingTableData = false
      })
    },
    checkActId(actId) {
      if (actId === '' || actId === null) {
        this.$Message.warning("请输入活动ID")
        return false
      }
      if (actId === '' || actId <= 0 || isNaN(actId)) {
        this.$Message.warning("输入的活动ID有误,请检查")
        return false
      }

      return true
    },
    // 新增or编辑奖池
    onEditAward: function (item) {
      this.awardFormVisible = true
      if (item) {
        this.awardFormData.taskId = item.taskId
        this.awardFormData.taskName = item.taskName
        this.awardFormData.status = item.status
        this.awardFormData.actId = item.actId
        this.awardFormData.openTime = item.openTime
        this.awardFormData.endTime = item.endTime
        this.awardFormData.model = item.model
        this.awardFormData.remark = item.remark
        this.awardFormData.add = false
      } else {
        this.awardFormData.actId = this.searchForm.actId
      }
    },
    onAwardToExcel() {
      const req = this.searchForm
      if (req.actId === '' || isNaN(req.actId)) {
        this.$Message.warning("请选择要查询的活动")
        return
      }
      this.download('awardConfig/awardTask2Excel?actId=' + req.actId, {
      }, `奖品配置记录_${new Date().getTime()}.xlsx`)
    },
    // 弹出复制奖池窗
    onCopy: function (item) {
      this.copyVisible = true
      const self = this
      self.copyItem.fromTaskId = item.taskId
      self.copyItem.fromTaskName = item.taskName
      self.copyItem.fromActId = item.actId
      self.copyItem.toTaskId = ''
      self.copyItem.toTaskName = item.taskName
      self.copyItem.toActId = ''
    },
    onSaveCopy: function () {
      const self = this
      const copyItem = self.copyItem
      if (!this.checkActId(copyItem.toActId)) {
        return
      }
      if (copyItem.toTaskName === '') {
        this.$Message.warning("请输入新奖池名称")
        return
      }
      // if (copyItem.toTaskId === '') {
      //   this.$Message.warning("请输入新奖池id")
      //   return
      // }
      // if (copyItem.toTaskId <= 0 || isNaN(copyItem.toTaskId)) {
      //   this.$Message.warning("新奖池id输入有误")
      //   return
      // }
      Api.saveCopyAwardTask(copyItem).then(
        res => {
          if (res.result === 200) {
            self.copyVisible = false
            this.$Message.success("复制成功")
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      )
    },
    // 关闭弹窗
    onCloseAwardForm: function () {
      this.resetAwardForm()
    },
    // 保存奖池信息
    onSaveAward: function () {
      // 参数校验
      const self = this
      const awardFormData = self.awardFormData
      if (!this.checkActId(awardFormData.actId)) {
        return
      }
      if (awardFormData.taskName === '') {
        this.$Message.warning("请输入奖池名称")
        return
      }
      // if (awardFormData.taskId === '' || awardFormData.taskId <= 0 || isNaN(awardFormData.taskId)) {
      //   this.$Message.warning("请输入奖池id")
      //   return
      // }
      // if (awardFormData.taskId <= 0 || isNaN(awardFormData.taskId)) {
      //   this.$Message.warning("奖池id输入有误")
      //   return
      // }
      if (awardFormData.openTime === '' || awardFormData.endTime === '') {
        this.$Message.warning("请填写奖池时间")
        return
      }
      if (awardFormData.openTime >= awardFormData.endTime) {
        this.$Message.warning("奖池结束时间不能早于开始时间")
        return
      }
      Api.saveAwardTask(awardFormData).then(
        res => {
          if (res.result === 200) {
            self.resetAwardForm()
            self.awardFormVisible = false
            this.$Message.success("保存成功")
            if (self.searchForm.actId === '') {
              self.searchForm.actId = awardFormData.actId
            }
            self.onSearch()
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      )/*.catch((error) => {
          console.log(error)
          this.$Message.error(error.status + "," + error.statusText, 5)
        })*/
    },
    // 重置数据
    resetAwardForm: function () {
      this.awardFormData = {
        taskId: '',
        actId: '',
        taskName: '',
        openTime: '',
        endTime: '',
        status: '1',
        model: '100',
        remark: '',
        add: true
      }
    },
    //加载活动数据
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
  }
};
</script>
