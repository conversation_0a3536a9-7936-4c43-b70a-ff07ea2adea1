<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form>
          <el-form-item label="活动ID">
            <el-select v-model="actId" placeholder="请选择活动" filterable clearable style="width:300px">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </el-row>
    </div>
  </div>
</template>
<script>
import ActConfigVersion from "@/api/activity/actConfigVersion"
export default {
  name: "RoleRemove",
  data: () => {
    return {
      selectActList: [],
      actId: ''
    }
  },
  created() {
    this.loadActInfos()
  },
  methods: {
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    }
  }
}
</script>
<style scoped lang="scss">

</style>
