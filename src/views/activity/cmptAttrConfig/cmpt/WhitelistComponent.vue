<template>
  <div>
    <el-row>
      <el-form :inline="true">
        <el-form-item label="成员ID">
          <el-input v-model="searchForm.member" />
        </el-form-item>

        <el-button type="primary" :loading="loadingWhitelist" @click="onSearch(true)" size="medium">
          搜索
        </el-button>
        <el-button type="success" @click="onClickAddWhitelist">添加</el-button>
        <input ref="uploadInput" type="file" name="file" class="upload-input" @change="handleImport" />
        <el-button type="info" @click="onClickImport">导入</el-button>
        <el-tooltip class="item" effect="light" content="点击可下载导入模板"
                    placement="right-start"
                    style="margin-left: 10px;">
          <a href="/component/whitelist/export" target="_blank"><i class="el-icon-download" /></a>
        </el-tooltip>
      </el-form>
    </el-row>
    <el-row>
      <el-table :data="tableData" :loading="loadingWhitelist" border>
        <el-table-column label="成员ID" prop="member" align="center" width="270" />
        <el-table-column label="配置值" prop="configValue"  align="center" />
        <el-table-column label="操作" width="250"  align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="onClickModWhitelist(scope.row)">修改</el-button>
            <el-popconfirm style="margin-left: 10px"
                           confirm-button-text="确认" cancel-button-text="取消"
                           @confirm="onClickDelWhitelist(scope.row)"
                           :title="'确定删除【' + scope.row.member + '】此白名单吗'">
              <el-button slot="reference" type="text" style="color: #f56c6c;">删除</el-button>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="[15,20,50,100]"
        @size-change="onSearch(false)"
        @current-change="onSearch(false)"
        layout="->, total, prev, pager, next, sizes"
        :current-page.sync="pageData.page"
        :page-size.sync="pageData.size"
        :total="pageData.total">
      </el-pagination>
    </el-row>
    <json-modal ref="whitelistEditModal" @submit="doModWhitelist" />
    <whitelist-modal ref="whitelistAddModal" @submit="doAddWhitelist" />
  </div>
</template>
<script>
import { addCmptWhitelist, queryPageCmptWhitelist, updateCmptWhitelist, deleteCmptWhitelist, importCmptWhitelist } from '@/api/activity/configCenter'
import JsonModal from "@/components/Json/JsonModal.vue"
import WhitelistModal from "./WhitelistModal.vue"
export default {
  name: "WhitelistComponent",
  components: {JsonModal, WhitelistModal},
  data() {
    return {
      loadingWhitelist: false,
      searchForm: {
        member: ''
      },
      tableData: [],
      pageData: {
        page: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  props: {
    actId: {
      type: String,
      required: true
    },
    useIndex: {
      type: String,
      required: true
    }
  },
  methods: {
    onSearch(fromBtn) {
      if (fromBtn) {
        this.pageData.page = 1
      }

      const self = this
      this.loadingWhitelist = true
      queryPageCmptWhitelist(this.actId, this.useIndex, this.searchForm.member, this.pageData.page, this.pageData.pageSize).then(res => {
        if (res.code !== 200) {
          self.$message({message: res.msg, type: 'error'})
          return
        }

        self.tableData = res.data
        self.pageData.total = res.total
      }).catch(err => {
        self.$message({message: '查询白名单列表出错了', type: 'error'})
      }).finally(() => {
        self.loadingWhitelist = false
      })
    },
    onClickModWhitelist(row) {
      const context = row.member
      this.$refs.whitelistEditModal.showModal(row.configValue, row.member + '的值', context)
    },
    doModWhitelist({ jsonValue, context }) {
      const self = this
      updateCmptWhitelist(this.actId, this.useIndex, context, jsonValue).then(res => {
        if (res.result === 200) {
          self.$message({message: '修改成功', type: 'success'})
          self.onSearch(false)
          return
        }

        self.$message({message: res.reason, type: 'error'})
      }).catch(err => {
        self.$message({message: '修改出错了', type: 'error'})
      })
    },
    onClickDelWhitelist(row) {
      const self = this
      deleteCmptWhitelist(this.actId, this.useIndex, row.member).then(res => {
        if (res.result === 200) {
          self.$message({message: '删除成功', type: 'success'})
          self.onSearch(false)
          return
        }

        self.$message({message: res.reason, type: 'error'})
      }).catch(err => {
        self.$message({message: '删除出错了', type: 'error'})
      })
    },
    onClickAddWhitelist() {
      this.$refs.whitelistAddModal.showModal()
    },
    doAddWhitelist({ member, configValue }) {
      const self = this
      addCmptWhitelist(this.actId, this.useIndex, member, configValue).then(res => {
        if (res.result === 200) {
          self.$message({message: '修改成功', type: 'success'})
          this.$refs.whitelistAddModal.cancel()
          self.onSearch(false)
          return
        }

        self.$message({message: res.reason, type: 'error'})
      }).catch(err => {
        self.$message({message: '添加出错了', type: 'error'})
      })
    },
    onClickImport() {
      // this.$message({message: '敬请期待', type: "info"})
      this.$refs.uploadInput.click()
    },
    handleImport(event) {
      const file = event.target.files?.[0]
      if (!file) {
        this.$message({message: '请选择一个文件', type: 'warning'})
        return
      }

      const self = this
      importCmptWhitelist(this.actId, this.useIndex, file).then(res => {
        if (res.result === 200) {
          self.$message({message: '导入成功，覆盖记录数' + res.data, type: 'success'})
          self.onSearch(false)
          return
        }

        self.$message({message: res.reason, type: 'error'})
      }).catch(err => {
        self.$message({message: '导入出错了', type: 'error'})
      })
    }
  },
  beforeMount() {
    this.onSearch(false)
  }
}
</script>

<style scoped lang="scss">
.upload-input {
  display: none;
}
</style>
