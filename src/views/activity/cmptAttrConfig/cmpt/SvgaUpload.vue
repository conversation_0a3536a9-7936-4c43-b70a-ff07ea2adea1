<template>
  <div class="image-upload">
    <el-input v-model="url" placeholder="点击右侧按钮上传SVGA">
      <i v-show="url" slot="suffix" class="el-icon-view el-input__icon upload-preview-icon" title="点击预览SVGA" @click="clickPreview" />
      <el-button slot="append" title="点击上传" icon="el-icon-upload" @click="onClickUpload" />
    </el-input>
    <input ref="uploadInput" type="file" name="file" class="upload-input" @change="handleFileUpload" />
    <el-dialog title="SVGA预览" :visible.sync="svgaDialogVisible" width="70%" append-to-body>
      <div class="view-image-container">
        <svga :src="url" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { upload } from '@/api/upload'
import { svga } from 'vue-svga'

export default {
  name: "SvgaUpload",
  components: { svga },
  data() {
    return {
      url: this.value,
      svgaDialogVisible: false
    }
  },
  props: {
    value: String
  },
  methods: {
    onClickUpload() {
      this.$refs.uploadInput.click()
    },
    handleFileUpload(event) {
      const file = event.target.files?.[0]
      if (!file) {
        this.$message({message: '请选择一个文件', type: 'warning'})
        return
      }

      const fileName = this.generateFileName()
      const self = this
      upload(file, fileName).then(res => {
        if (res.result === 200) {
          self.$message({message: '上传成功', type: 'success'})
          self.url = res.data.downloadUrl
          return
        }

        self.$message({message: res.reason, type: 'error'})
      })
    },
    generateFileName() {
      return this.randomString(20).toLowerCase()
    },
    randomString(len) {
      len = len || 32
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
      const maxPos = chars.length
      let pwd = ''
      for (let i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos))
      }

      return pwd
    },
    clickPreview() {
      this.svgaDialogVisible = true
    }
  },
  watch: {
    url: function (val) {
      this.$emit('input', val)
    },
    value: function (val) {
      this.url = val
    }
  }
}
</script>
<style scoped lang="scss">
.image-upload {
  display: inline-block;
}

.image-upload .upload-preview-icon {
  cursor: pointer;
}

.image-upload .upload-input {
  display: none;
}

.view-image-container {
  text-align: center;
}

.view-image {

}
</style>
