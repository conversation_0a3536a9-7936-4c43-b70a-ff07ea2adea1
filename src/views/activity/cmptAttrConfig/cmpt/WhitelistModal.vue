<template>
  <el-dialog :visible.sync="dialogVisible" :fullscreen="dialogFull" width="60%" :before-close="cancel">
    <template slot="title">
      <el-row type="flex">
        <el-col :span="12">
          <div>添加白名单</div>
        </el-col>
        <el-col :span="1" :offset="11">
          <el-button type="text" size="mini" :icon="dialogFull ? 'el-icon-crop' : 'el-icon-full-screen'"
                     @click="dialogFull = !dialogFull"></el-button>
        </el-col>
      </el-row>
    </template>
    <el-form>
      <el-form-item label="成员">
        <el-input v-model="member" placeholder="白名单Member" />
      </el-form-item>
      <el-form-item label="配置值">
      </el-form-item>
      <codemirror ref="myCm" v-model="configValue" :options="cmOption" />
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="submitJson">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>

// require component
import { codemirror } from 'vue-codemirror'
import 'codemirror/mode/xml/xml'
import { addCmptWhitelist } from '@/api/activity/configCenter'

// require styles
import 'codemirror/theme/monokai.css'
import 'codemirror/lib/codemirror.css'
import {add} from "script-ext-html-webpack-plugin/lib/custom-attributes";

export default {
  name: "WhitelistModal",
  data: () => ({
    dialogVisible: false,
    dialogFull: false,
    member: '',
    configValue: '',
    cmOption: {
      lineNumbers: true, // 显示行号
      styleActiveLine: true,
      theme: 'monokai',
      mode: 'xml'
    }
  }),
  components: {
    codemirror
  },
  methods: {
    showModal(_actId, _) {
      this.dialogVisible = true
    },
    cancel() {
      this.member = ''
      this.configValue = ''
      this.dialogVisible = false
    },
    submitJson() {
      if (!this.member || this.member.trim() === '') {
        this.$message({message: '成员不能为空', type: "error"})
        return
      }
      this.$emit('submit', {member: this.member, configValue: this.configValue})
    }
  }
}
</script>

<style>
.CodeMirror {
  border: 1px solid #eee;
  height: 600px;
}
</style>
