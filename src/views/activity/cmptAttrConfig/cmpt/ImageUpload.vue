<template>
  <div class="image-upload">
    <el-input v-model="url" placeholder="点击右侧按钮上传图片">
      <i v-show="previewVisible" slot="suffix" class="el-icon-view el-input__icon upload-preview-icon" title="点击预览图片" @click="dialogVisible = true" />
      <el-button slot="append" title="点击上传" icon="el-icon-upload" @click="onClickUpload" />
    </el-input>
    <input ref="uploadInput" type="file" name="file" class="upload-input" @change="handleFileUpload" />
    <el-dialog title="图片预览" :visible.sync="dialogVisible" width="30%" :append-to-body="true" :destroy-on-close="true">
      <div class="view-image-container">
        <el-image fit="fill" class="view-image" :src="url" />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { upload } from '@/api/upload'
export default {
  name: "ImageUpload",
  data() {
    return {
      url: this.value,
      dialogVisible: false
    }
  },
  computed: {
    previewVisible: function () {
      if (!this.url) {
        return false
      }

      const imageExtensions = /\.(jpe?g|png|gif|bmp|webp|svg|tiff?|avif)(\?.*)?$/i;
      return imageExtensions.test(this.url);
    }
  },
  props: {
    value: String,
    prefix: {
      type: String,
      default: '',
      required: false
    },
    addTimeTag: {
      type: String,
      default: '0',
      required: false
    }
  },
  methods: {
    onClickUpload() {
      this.$refs.uploadInput.click()
    },
    handleFileUpload(event) {
      const file = event.target.files?.[0]
      if (!file) {
        this.$message({message: '请选择一个文件', type: 'warning'})
        return
      }

      const fileName = this.generateFileName()
      const self = this
      upload(file, fileName, this.addTimeTag).then(res => {
        if (res.result === 200) {
          self.$message({message: '上传成功', type: 'success'})
          self.url = res.data.downloadUrl
          return
        }

        self.$message({message: res.reason, type: 'error'})
      })
    },
    generateFileName() {
      const filename = this.randomString(20).toLowerCase()
      if (!this.prefix) {
        return filename
      }

      return this.prefix + '/' + filename
    },
    randomString(len) {
      len = len || 32
      const chars = 'abcdefghijklmnopqrstuvwxyz0123456789'
      const maxPos = chars.length
      let pwd = ''
      for (let i = 0; i < len; i++) {
        pwd += chars.charAt(Math.floor(Math.random() * maxPos))
      }

      return pwd
    }
  },
  watch: {
    url: function (val) {
      this.$emit('input', val)
    },
    value: function (val) {
      this.url = val
    }
  }
}
</script>
<style scoped lang="scss">
.image-upload {
  display: inline-block;
}

.image-upload .upload-preview-icon {
  cursor: pointer;
}

.image-upload .upload-input {
  display: none;
}

.view-image-container {
  text-align: center;
}

.view-image {

}
</style>
