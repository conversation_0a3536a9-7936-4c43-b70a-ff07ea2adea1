<template>
  <div class="resource-selector">
    <el-input placeholder="左侧选择资源或直接配置URL" v-model="resourceKey" class="input-with-select">
      <el-select v-model="resourceKey" slot="prepend" placeholder="选择资源">
        <i slot="prefix" class="el-input__icon el-icon-s-unfold" />
        <el-option v-for="actResource in actResources" :value="actResource.resourceKey" :key="actResource.resourceKey" :label="actResource.resourceDesc" />
      </el-select>
      <el-button slot="append" icon="el-icon-setting" title="活动资源管理" @click="openActResourceConfig" />
    </el-input>
  </div>
</template>
<script>
import { queryActResources } from '@/api/activity/actResource'
export default {
  name: "ResourceSelector",
  data() {
    return {

      actResources: [],
      resourceKey: this.value
    }
  },
  props: {
    actId: Number,
    value: String
  },
  beforeMount() {
    this.loadAllResources()
  },
  methods: {
    loadAllResources() {
      const self = this
      queryActResources(this.actId).then(res => {
        if (res && res.result === 200) {
          self.actResources = res.data
        }
      })
    },
    openActResourceConfig() {
      this.$router.push({path: '/actAttr/actResource', query: {actId: this.actId}})
    }
  },
  watch: {
    resourceKey: function(val) {
      this.$emit('input', val)
    },
    value: function(val) {
      this.resourceKey = val
    }
  }
}
</script>

<style scoped lang="scss">
.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
</style>
