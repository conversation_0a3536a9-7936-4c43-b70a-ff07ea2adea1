<template>
  <div class="app-container">
    <div>
      <el-container>
        <el-main v-if="componentId === '2055'">
          <el-row v-if="actName">
            <h4>
              <el-button v-if="!fromIframe" @click="close()">返回</el-button>
              {{ actName }} -- {{ componentTitle }} -- 组件属性配置
            </h4>
          </el-row>
          <el-row v-if="actName">
            <el-divider content-position="center">白名单</el-divider>
          </el-row>
          <whitelist-component :act-id="actId" :use-index="useIndex" />
        </el-main>

        <el-main v-else>
          <div style="margin-bottom: 10px;">
            <h4>
              <el-button v-if="!fromIframe" @click="close()">返回</el-button>
              {{ actName }} -- {{ componentTitle }} -- 组件属性配置
            </h4>
          </div>

          <el-tabs v-if="fromIframe" v-model="activeTagName">
            <el-tab-pane label="功能设置" name="功能设置"></el-tab-pane>
            <el-tab-pane label="基础设置" name="基础设置"></el-tab-pane>
            <el-tab-pane label="系统设置" name="系统设置"></el-tab-pane>
            <el-tab-pane label="所有设置" name="所有设置"></el-tab-pane>
          </el-tabs>

          <el-form v-if="filterAttrs.length > 0" label-width="150px">
            <el-form-item v-for="(item,index) in filterAttrs" :key="index" :label="item.labelText"
                          :prop="item.propName">
              <!-- 输入框样式 -->
              <el-input v-model="item.data" v-if="item.propType === 'Text'"
                        :placeholder="item.placeholder" style="width: 50%;" />
              <el-input v-model="item.data" v-if="item.propType === 'Textarea'" type="textarea"
                        :rows="10" :placeholder="item.placeholder" style="width: 50%;" />
              <!-- 图片类型  -->
              <image-upload v-if="item.propType === 'Image'" v-model="item.data" :prefix="actId+'/'+componentId" style="width: 50%" />
              <!-- svga类型 -->
              <svga-upload v-model="item.data" v-if="item.propType === 'Svga'" style="width: 50%" />
              <!-- 数字样式 -->
              <el-input-number v-model="item.data" v-if="item.propType === 'Number'" :controls=false
                               :placeholder="item.placeholder" style="width: 50%;" :max="item.max" :min="item.min" />

              <!-- 下拉选项 -->
              <el-select v-model="item.data" v-if="item.propType === 'DropDown'" style="width: 50%;"
                         :placeholder="item.placeholder" filterable @change="$forceUpdate()">
                <el-option
                  v-for="option in item.options"
                  :key="option.code"
                  :label="option.desc"
                  :value="option.code">
                </el-option>
              </el-select>

              <!-- 下拉选项2 -->
              <el-select v-model="item.data" v-if="item.propType === 'DropDownV2'" style="width: 50%;" allow-create
                         :placeholder="item.placeholder" filterable @change="$forceUpdate()" >
                <el-option
                  v-for="option in item.ext.list"
                  :key="option.value"
                  :label="option.show"
                  :value="option.value">
                </el-option>
              </el-select>

              <!-- 表格 -->
              <el-table v-if="item.propType === 'Table'" size="mini" :data="item.data" border highlight-current-row
                        style="width: 95%;">
                <el-table-column type="index" />
                <el-table-column
                  v-for="(prop,index) in item.props"
                  :label="prop.labelText"
                  :prop="prop.propName"
                  :key="index">
                  <template slot="header" slot-scope="scope">
                    <span>{{ prop.labelText }}</span>
                    <el-tooltip effect="light" placement="right-start"
                                style="margin-left: 10px;" v-if="prop.remark">
                      <div slot="content">{{ prop.remark }}</div>
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>
                  <template slot-scope="scope">
                    <span v-if="item.editRowId !== undefined && item.editRowId === scope.row.id">
                      <span v-if="prop.propType === 'DropDownV2' ">
                        <el-select
                         v-model="item.selectRow[prop.propName]"
                          allow-create :placeholder="item.placeholder" filterable @change="$forceUpdate()">
                          <el-option
                            v-for="option in item.ext.list"
                            :key="option.value"
                            :label="option.show"
                            :value="option.value">
                          </el-option>
                        </el-select>
                      </span>
                      <image-upload v-else-if="prop.propType === 'Image'" v-model="item.selectRow[prop.propName]" :prefix="actId+'/'+componentId" />
                      <el-input-number v-else-if="prop.propType === 'Number'" size="mini" placeholder="请输入数字" v-model="item.selectRow[prop.propName]" :controls=false :max="prop.max" :min="prop.min"  />
                      <resource-selector v-else-if="prop.propType === 'Resource'" v-model="item.selectRow[prop.propName]" :act-id="actId" />
                      <el-input v-else size="mini" placeholder="请输入内容" v-model="item.selectRow[prop.propName]" />
                    </span>
                    <span v-else>{{ scope.row[prop.propName] }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button v-show="item.editRowId !== undefined && item.editRowId === scope.row.id" size="small" class="button" type="text" @click="saveRow(item, scope.row, scope.$index)"
                               style="color: #67c23a;">保存
                    </el-button>
                    <el-button v-show="item.editRowId === undefined" size="small" class="button" type="text" @click="cloneRow(item, scope.row, scope.$index)"
                               style="color: #67c23a;">复制
                    </el-button>
                    <el-button size="small" class="button" type="text" @click="editRow(item, scope.row, scope.$index)"
                               v-if="!item.useDialog && item.props.length <= 6">
                      编辑
                    </el-button>
                    <el-button v-if="item.useDialog || item.props.length > 6" size="small" class="button" type="text"
                               @click="editRowWithDialog(item, scope.row, scope.$index)">
                      编辑
                    </el-button>
                    <el-button size="small" class="button" type="text" @click="deleteRow(item, scope.row, scope.$index)"
                               style="color: #f56c6c;">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-col v-if="item.propType === 'Table'">
                <div class="table-add-row" @click="onAddRow(item)"><span>+ 添加</span></div>
              </el-col>

              <!-- 开关 -->
              <el-switch v-model="item.data" v-if="item.propType === 'Switch'" active-value="true"/>

              <!-- 日期 -->
              <el-date-picker v-model="item.data" type="datetime" v-if="item.propType === 'DateTime'"
                              :placeholder="item.placeholder"
                              :format="item.format" :value-format="item.valueFormat" />

              <el-time-picker v-model="item.data" v-if="item.propType === 'TimePicker'"
                              :placeholder="item.placeholder"
                              value-format="HH:mm:ss" />

              <!--    TODO: duration类型控件          -->
              <el-input v-model="item.data" v-if="item.propType === 'Duration'"
                        :placeholder="item.placeholder" style="width: 50%;" />
              <resource-selector v-model="item.data" v-if="item.propType === 'Resource'" :act-id="actId" style="width: 50%" />
              <!-- :content="item.remark" -->
              <el-tooltip effect="light" placement="right-start"
                          style="margin-left: 10px;" v-if="item.remark">
                <div v-html="toBreak(item.remark)" v-if="componentId == 1006" slot="content"></div>
                <div v-if="componentId != 1006" slot="content">{{ item.remark }}</div>
                <i class="el-icon-question" />
              </el-tooltip>
            </el-form-item>
          </el-form>

          <div style="text-align: center; font-size: 22px" v-else>暂无配置</div>

          <div v-if="filterAttrs.length > 0">
            <el-button type="primary" @click="onSave">保 存</el-button>
          </div>
        </el-main>

        <el-dialog
          :title="selectItem.labelText"
          :visible.sync="dialogVisible"
          width="50%">
          <el-form label-width="150px">
            <el-form-item v-for="(item, index) in selectItem.props" :key="index" :label="item.labelText"
                          :prop="item.propName" :inline="true">
              <span slot="label">
                <span>{{ item.labelText }}</span>
                  <el-tooltip effect="light" placement="right-start"
                              style="margin-left: 10px;" v-if="item.remark">
                  <label v-html="toBreak(item.remark)" v-if="componentId == 1006" slot="content"></label>
                  <label v-if="componentId != 1006" slot="content">{{ item.remark }}</label>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </span>

              <el-input v-if="item.propType === 'Textarea'" type="textarea"
                        v-model="selectItem.selectRow[item.propName]" style="width: 85%;" />
              <!-- 图片类型  -->
              <image-upload v-else-if="item.propType === 'Image'" v-model="selectItem.selectRow[item.propName]" :prefix="actId+'/'+componentId" style="width: 85%" />
              <!-- svga类型 -->
              <svga-upload v-else-if="item.propType === 'Svga'" v-model="selectItem.selectRow[item.propName]" style="width: 85%" />
              <!-- 活动资源  -->
              <resource-selector v-else-if="item.propType === 'Resource'" v-model="selectItem.selectRow[item.propName]" :act-id="actId" />
              <!-- 数字样式 -->
              <el-input-number v-else-if="item.propType === 'Number'" v-model="selectItem.selectRow[item.propName]" :controls=false
                               :placeholder="item.placeholder" style="width: 85%;" :max="item.max" :min="item.min" />
              <el-input v-else v-model="selectItem.selectRow[item.propName]" style="width: 85%;" />
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <el-button @click="onDialogClose()">取 消</el-button>
            <el-button type="primary" @click="onDialogSave()">确 定</el-button>
          </span>
        </el-dialog>
      </el-container>
    </div>
  </div>
</template>

<script>
import Api from '@/api/activity/cmptAttrConfig'
import WhitelistComponent from "./cmpt/WhitelistComponent.vue"
import ImageUpload from './cmpt/ImageUpload.vue'
import SvgaUpload from './cmpt/SvgaUpload.vue'
import ResourceSelector from "./cmpt/ResourceSelector.vue";

export default {
  name: "CmptAttrConfig",
  components: {ResourceSelector, ImageUpload, SvgaUpload, WhitelistComponent },
  data: function () {
    return {
      componentId: '',
      actId: '',
      useIndex: '',
      componentTitle: '',
      actName: '',
      defineAttrs: [],
      dialogVisible: false,
      selectItem: {},
      fromIframe: '',
      activeTagName: '功能设置'
    }
  },
  mounted: function () {
    const query = this.$route.query
    this.componentId = query.componentId
    this.actId = query.actId
    this.useIndex = query.useIndex
    this.actName = query.actName
    this.componentTitle = query.componentTitle
    this.fromIframe = query.fromIframe
    this.loadAttr();
  },
  computed: {
    // 返回符合tag条件的数据
    filterAttrs: function() {
      if(!this.fromIframe) {
        return this.defineAttrs
      }
      if(this.activeTagName === '所有设置'){
        return this.defineAttrs
      } else {
        const temp = this.defineAttrs.filter((item) => {
          return item.tag === this.activeTagName;
        })

        return temp
      }
    },
  },
  methods: {
    loadAttr() {
      const self = this
      Api.listComponentAttrDefine(self.actId, self.componentId, self.useIndex).then(
        res => {
          if (res.result === 200) {
            self.defineAttrs = res.data
          } else {
            alert("拉取属性列表错误," + res.reason)
          }
        }
      )
    },
    onSave() {
      const param = {
        actId: this.actId,
        cmptId: this.componentId,
        useIndex: this.useIndex
      }
      const values = []
      for (let attr of this.defineAttrs) {
        values.push({propName: attr.propName, data: attr.data})
      }
      param["values"] = values

      Api.saveComponentAttrValue(param).then(
        res => {
          if (res.result === 200) {
            this.$message.success(res.data);
          } else {
            this.$message.error(res.reason);
          }
        }
      )
    },
    addRow(item) {
      if (item.editRowId !== undefined) {
        return this.$message.error("请先保存当前编辑项")
      }

      const data = item.data
      const newId = data.length
      let newRow = {
        id: newId
      }

      const props = item.props
      for (let prop of props) {
        newRow[prop.propName] = ''
      }
      data.push(newRow)
      this.$set(item, 'selectRow', { ...newRow })
      this.$set(item, 'editRowId', newId)
    },
    onAddRow(item) {
      if (item.useDialog || item.props.length > 6) {
        this.addRowByDialog(item)
      } else {
        this.addRow(item)
      }
    },
    editRow(item, row, index) {
      if (item.editRowId !== undefined) {
        if (row.id === item.editRowId) {
          console.info('already in edit mode')
          return
        }
        return this.$message.error("请先保存当前编辑项")
      }

      row.id = index
      this.$set(item, 'selectRow', { ...row })
      this.$set(item, 'editRowId', index)
    },
    saveRow(item, row, index) {
      // 保存表格行
      if (item.editRowId === undefined) {
        console.warn('unexpected item editRowId')
        return
      }

      row.id = index
      if (row.id !== item.editRowId) {
        console.warn('unexpected row id')
        return
      }

      const props = item.props
      for (let prop of props) {
        row[prop.propName] = item.selectRow[prop.propName]
      }

      this.$set(item, 'editRowId', undefined)
      this.$set(item, 'selectRow', undefined)
    },
    cloneRow(item, row) {
      if (item.editRowId !== undefined) {
        return this.$message.error("请先保存当前编辑项")
      }

      const data = item.data
      const newId = data.length

      let newRow = {
        id: newId
      }

      const props = item.props
      for (let prop of props) {
        newRow[prop.propName] = row[prop.propName]
      }

      data.push(newRow)
      this.$set(item, 'selectRow', { ...newRow })
      this.$set(item, 'editRowId', newId)
    },
    deleteRow(item, row, index) {
      if (item.editRowId !== undefined) {
        return this.$message.error("请先保存当前编辑项")
      }

      item.data.splice(index, 1)
    },
    addRowByDialog(item) {
      if (item.editRowId !== undefined) {
        return this.$message.error("请先保存当前编辑项")
      }

      const data = item.data
      const newId = data.length
      const newRow = {
        id: newId
      }

      const props = item.props
      for (let prop of props) {
        newRow[prop.propName] = ''
      }

      this.$set(item, 'selectRow', newRow)

      if (this.selectItem === undefined || this.selectItem === null || this.selectItem.propName !== item.propName) {
        this.selectItem = item
      }

      this.dialogVisible = true
    },
    editRowWithDialog(item, row) {
      if (item.editRowId !== undefined) {
        return this.$message.error("请先保存当前编辑项")
      }

      this.$set(item, 'selectRow', { ...row })

      if (this.selectItem === undefined || this.selectItem === null || this.selectItem.propName !== item.propName) {
        this.selectItem = item
      }

      this.dialogVisible = true
    },
    toBreak(content) {
      return content.split("\\n").join("<br/>")
    },
    /** 关闭按钮 */
    close() {
      const obj = {path: "cmptConfig"};
      this.$tab.closeOpenPage(obj);
    },
    onDialogClose() {
      this.dialogVisible = false
    },
    onDialogSave() {
      // 保存表格行
      if (this.selectItem === undefined || this.selectItem === null) {
        console.warn('unexpected selectItem')
        return
      }

      if (this.selectItem.selectRow === undefined || this.selectItem.selectRow === null) {
        console.warn('unexpected selectItem selectRow')
        return
      }

      const editRow = { ...this.selectItem.selectRow }

      for (const row of this.selectItem.data) {
        if (row.id === editRow.id) {
          for (const prop of this.selectItem.props) {
            row[prop.propName] = editRow[prop.propName]
          }

          this.dialogVisible = false
          return
        }
      }

      this.selectItem.data.push(editRow)
      this.dialogVisible = false
    }
  }
};
</script>
<style scoped>
.table-add-row {
  margin-top: 5px;
  height: 30px;
  width: 95%;
  border: 1px dashed #c1c1cd;
  border-radius: 3px;
  cursor: pointer;
  justify-content: center;
  display: flex;
  line-height: 30px;
}
</style>
