<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="活动id">
            <el-select v-model="searchForm.actId" placeholder="请选择活动"
                       filterable style="width:180px" @change="actIdChange">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="榜单名称">
            <el-select v-model="searchForm.rankId" placeholder="榜单名称"
                       filterable style="width:180px" clearable>
              <el-option v-for="item in rankList" :value="item.code" :key="item.code" :label='item.desc+"("+item.code+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="用户id">
            <el-input v-model="searchForm.uid" style="width:100px"></el-input>
          </el-form-item>
          <el-form-item label="发放状态">
            <el-select v-model="searchForm.status" placeholder="发放状态" style="width:120px" clearable>
              <el-option v-for="item in statusList" :value="item.code" :key="item.code" :label='item.desc'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker v-model="searchForm.dateRange" type="daterange" range-separator="-" value-format="yyyy-MM-dd"
                            start-placeholder="开始日期" end-placeholder="结束日期" style="width: 250px;">
            </el-date-picker>
          </el-form-item>
          <el-button type="primary" :loading="loadingAwardIssueLog" @click="onSearch(true)" icon="el-icon-search"
                     size="medium">
            查询
          </el-button>
          <el-button type="info" :loading="exportAwardIssueLog" @click="onExport" size="medium">
            导出Excel
          </el-button>
          <el-tooltip class="item" effect="light" content="操作表:award_issue" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>
      <el-row>
        <el-table :data="tableData" border v-loading="loadingAwardIssueLog">
          <el-table-column prop="issueId" label="发放标识" align="center" width="150"></el-table-column>
          <el-table-column prop="actName" label="活动名称" align="center" width="120"></el-table-column>
          <el-table-column prop="rankName" label="榜单名称" align="center" width="120"></el-table-column>
          <el-table-column prop="phaseName" label="阶段名称" align="center" width="80"></el-table-column>
          <el-table-column prop="receiver" label="用户id" align="center" width="110"></el-table-column>
          <el-table-column prop="packageList" label="奖包内容" align="center" width="150"></el-table-column>
          <el-table-column prop="packageContent" label="奖包描述" align="center" width="250"></el-table-column>
          <el-table-column prop="status" label="状态" align="center" width="80"></el-table-column>
          <el-table-column prop="remark" label="备注" align="center" width="200"></el-table-column>
          <el-table-column prop="createTime" label="发放时间" align="center"></el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="[10,15,20,50,100]"
          @size-change="onSearch(false)"
          @current-change="onSearch(false)"
          layout="->, total, prev, pager, next, sizes"
          :current-page.sync="pageData.page"
          :page-size.sync="pageData.size"
          :total="pageData.total">
        </el-pagination>
      </el-row>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/rankTaskAwardIssue'
  import ActConfigVersion from '@/api/activity/actConfigVersion'

  export default {
    name: "RankTaskAwardIssue",
    components: {},
    data: function () {
      return {
        loadingAwardIssueLog: false,
        exportAwardIssueLog: false,
        selectActList: [],
        rankList: [],
        statusList: [],
        searchForm: {
          actId: '',
          rankId: '',
          packageId: '',
          uid: '',
          status: '',
          dateRange: []
        },
        pageData: {
          page: 1,
          size: 10,
          total: 0,
        },
        tableData: []
      }
    },
    created: function () {
      this.loadActInfos()
      this.buildStatusList()
    },
    methods: {
      onSearch(fromBtn) {
        const req = this.searchForm
        console.log(111)
        if (req.actId === '' || isNaN(req.actId)) {
          this.$Message.warning("请选择要查询的活动")
          return
        }
        const self = this
        if (fromBtn) {
          self.pageData.page = 1
        }
        req.pageNo = self.pageData.page
        req.pageSize = self.pageData.size
        if (req.dateRange != null && req.dateRange.length === 2) {
          req.startDay = req.dateRange[0]
          req.endDay = req.dateRange[1]
        }
        self.loadingAwardIssueLog = true
        Api.pageRankTaskAwardIssue(JSON.stringify(req))
          .then(res => {
            if (res.code === 200) {
              self.pageData.total = res.total
              self.tableData = res.data
            } else {
              this.$Message.warning(res.reason, 5)
            }
            self.loadingAwardIssueLog = false
          })
          .catch(error => {
            self.loadingAwardIssueLog = false
            console.log(error)
            this.$Message.error(error.status + "," + error.statusText, 5)
          })
      },
      onExport() {
        const req = this.searchForm
        if (req.actId === '' || isNaN(req.actId)) {
          this.$Message.warning("请选择要查询的活动")
          return
        }
        if (req.dateRange != null && req.dateRange.length === 2) {
          req.startDay = req.dateRange[0]
          req.endDay = req.dateRange[1]
        }
        window.location.href = process.env.VUE_APP_BASE_API + '//rankingTaskAward/exportExcel' + this.queryParams(req, true);
      },
      //加载活动数据
      loadActInfos() {
        ActConfigVersion.loadActInfos().then(
          res => {
            if (res.result === 200) {
              this.selectActList = res.data
            } else {
              alert("拉取活动列表错误," + res.reason)
            }
          }
        )
      },
      actIdChange() {
        const self = this
        self.searchForm.rankId = ''
        API.listRankDropDown(self.searchForm.actId)
          .then(res => {
            if (res.result === 200) {
              self.rankList = res.data
            } else {
              console.log(res)
            }
          })
      },
      buildStatusList() {
        const status = [
          {
            code: '0',
            desc: '未发放'
          }, {
            code: '1',
            desc: '发放成功'
          }, {
            code: '-1',
            desc: '失败可重试'
          }, {
            code: '-2',
            desc: '失败不重试'
          }]
        this.statusList = status
      },
      queryParams(data, isPrefix) {
        let prefix = isPrefix ? '?' : ''
        let _result = []
        for (let key in data) {
          console.log(key)
          if (key === 'dateRange') {
            continue
          }
          let value = data[key]
          // 去掉为空的参数
          if (['', undefined, null].includes(value)) {
            continue
          }
          if (value.constructor === Array) {
            value.forEach(_value => {
              _result.push(encodeURIComponent(key) + '[]=' + encodeURIComponent(_value))
            })
          } else {
            _result.push(encodeURIComponent(key) + '=' + encodeURIComponent(value))
          }
        }

        return _result.length ? prefix + _result.join('&') : ''
      },
    }
  };
</script>
