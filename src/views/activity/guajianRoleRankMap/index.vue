<template>
  <div class="app-container">
    <div>
      <Form inline :model="formItem">
        <FormItem>
          <span>活动ID：</span>
          <Select v-model="formItem.actId" style="width:300px">
            <Option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                    :label='item.act_name+"("+item.act_id+")"'></Option>
          </Select>
        </FormItem>
        <Button :loading="loadingState" type="primary" @click="queryRoleRankMap()"> 查询
        </Button>
        <Button :loading="loadingState" type="primary" @click="addTask()"> 新建
        </Button>

        <Button :loading="loadingState" type="primary" @click="copyConfig()"> 复制</Button>
        该配置主要定义了活动挂件的参与者所涉及的榜单和阶段.
      </Form>
      <br>

      <Modal :value="copyLayerState" width="600" :closable="false" :mask-closable="false">
        <span>该复制仅限于相关榜单结构的活动</span>
        <Form :label-width="100" :model="copyFormItem" ref="copyFormItem">
          <FormItem label="原活动ID" prop="srcActId">
            <Select v-model="copyFormItem.srcActId" style="width:300px">
              <Option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                      :label='item.act_name+"("+item.act_id+")"'></Option>
            </Select>
          </FormItem>
          <FormItem label="目标活动ID" prop="targetActId">
            <Select v-model="copyFormItem.targetActId" style="width:300px">
              <Option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                      :label='item.act_name+"("+item.act_id+")"'></Option>
            </Select>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button :loading="loadingCopyState" type="primary" @click="loadingCopyState=true; copyRoleRankMap()"> 确认复制
          </Button>
          <Button @click="closeCopyModal()"> 取消</Button>
        </div>
      </Modal>

      <Modal :value="addLayerState" width="600" :closable="false" :mask-closable="false">
        <p slot="header" style="text-align:center">
          <span v-if="opt_type===1">添加挂件定义</span>
          <span v-if="opt_type===2">编辑挂件定义</span>
        </p>
        <Form :label-width="100" :model="editFormItem" ref="editFormItem" :rules="roleRankMapValidate">

          <FormItem label="活动ID" prop="actId">
            <Input v-if="opt_type===2" disabled v-model="editFormItem.actId" style="width:300px"/>
            <Select v-else v-model="editFormItem.actId" style="width:300px">
              <Option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                      :label='item.act_name+"("+item.act_id+")"'></Option>
            </Select>
          </FormItem>

          <FormItem label="挂件tab" prop="itemTypeKey">
            <Select v-model="editFormItem.itemTypeKey" style="width:300px">
              <Option value="anchor">主播</Option>
              <Option value="channel">公会</Option>
              <Option value="hall">厅</Option>
              <Option value="room">语音房房间</Option>
              <Option value="anchorFamily">家族</Option>
              <Option value="anchor2">主播分赛场1</Option>
              <Option value="anchor3">主播分赛场2</Option>
              <Option value="anchor4">主播分赛场3</Option>
              <Option value="anchor5">主播战队赛</Option>
              <Option value="peiwan_tuan">大神团</Option>
              <Option value="pw_peipei">大神陪陪</Option>
              <Option value="hall2">多厅</Option>
              <Option value="cp_top1">CP</Option>
              <Option value="cp_top1_2">CP1</Option>
              <Option value="channel2">公会分赛场1</Option>
              <Option value="channel3">公会分赛场2</Option>
              <Option value="contribute">贡献</Option>
            </Select>
          </FormItem>

          <FormItem label="角色IDs">
            <Input v-model="editFormItem.roleIds" style="width:300px"/>
          </FormItem>
          <FormItem label="单一榜单ID">
            <Input placeholder="如果挂件只有一个榜单.填这里" v-model="editFormItem.rankId" style="width:300px"/>
          </FormItem>

          <FormItem label="单一阶段ID">
            <Input placeholder="如果只有一个阶段.填这里" v-model="editFormItem.phaseId" style="width:300px"/>
          </FormItem>

          <FormItem label="多个榜单ID">
            <Input placeholder="如果挂件有多个榜单填这里,逗号隔开" v-model="editFormItem.rankIdRange" style="width:300px"/>
          </FormItem>

          <FormItem label="多阶段ID">
            <Input placeholder="如果有多个阶段填这里,逗号隔开" v-model="editFormItem.phaseIdRange" style="width:300px"/>
          </FormItem>

          <FormItem label="榜单类型">
            <Select v-model="editFormItem.rankMapType" style="width:300px">
              <Option value=1>赛程主榜</Option>
              <Option value=2>日榜/小时榜</Option>
              <Option value=3>PK榜</Option>
              <Option value=4>奖励</Option>
              <Option value=5>贡献榜</Option>
              <Option value=6>分组排名</Option>
              <Option value=7>主榜-荣耀值</Option>
              <Option value=8>主播任务</Option>
              <Option value=9>第一名辅助榜</Option>
              <Option value=10>淘汰赛制</Option>
              <Option value=11>总榜</Option>
              <Option value=12>CP榜</Option>
            </Select>
          </FormItem>

          <FormItem label="日期格式">
            <Input placeholder="日榜yyyyMMdd/小时榜yyyyMMddHH " v-model="editFormItem.dateKey" style="width:300px"/>
          </FormItem>
          <FormItem label="说明">
            <Input v-model="editFormItem.remark" style="width:300px"/>
          </FormItem>

          <FormItem label="状态">
            <Select v-model="editFormItem.status" style="width:300px">
              <Option value=1>有效</Option>
              <Option value=0>无效</Option>
            </Select>
          </FormItem>

          <FormItem label="分数来源">
            <Select v-model="editFormItem.scoreSource" style="width:300px">
              <Option value=0>promot</Option>
              <Option value=1>pk</Option>
              <Option value=2>贡献榜</Option>
            </Select>
          </FormItem>

        </Form>

        <div slot="footer">
          <Button :loading="loadingState" type="primary" @click="loadingState=true;addRoleRankMap('editFormItem')"
                  v-if="opt_type==1">
            确认添加
          </Button>
          <Button :loading="loadingState" type="primary"
                  @click="loadingState=true;updateRoleRankMapWithValid('editFormItem')"
                  v-if="opt_type==2">
            确认修改
          </Button>
          <Button @click="closeModal()">
            取消
          </Button>
        </div>
      </Modal>


      <Table :columns="columns" :data="roleRankMapList"></Table>
      <br>

    </div>


  </div>
</template>

<script>
import Api from '@/api/activity/roleRankMap'
import ActConfigVersion from '@/api/activity/actConfigVersion'

export default {
  name: "RoleRankMap",
  components: {},
  data: function () {
    return {
      selectActList: [],
      roleRankMapList: [],
      columns: [{
        title: 'id',
        width: 140,
        key: 'id'
      }, {
        title: '活动ID',
        width: 120,
        key: 'actId'
      }, {
        title: '挂件tab',
        width: 150,
        key: 'itemTypeKey',
        render: (h, params) => {
          let content
          const itemTypeKey = params.row.itemTypeKey
          switch (itemTypeKey) {
            case 'anchor':
              content = '主播'
              break
            case 'channel':
              content = '公会'
              break
            case 'hall':
              content = '厅'
              break
            case 'room':
              content = '房间'
              break
            case 'anchorFamily':
              content = '家族'
              break
            case 'anchor2':
              content = '主播分赛场1'
              break
            case 'anchor3':
              content = '主播分赛场2'
              break
            case 'anchor4':
              content = '主播分赛场3'
              break
            case 'anchor5':
              content = '主播战队赛'
              break
            case 'peiwan_tuan':
              content = '大神团'
              break
            case 'pw_peipei':
              content = '大神陪陪'
              break
            case 'hall2':
              content = '多厅'
              break
            case 'cp_top1':
              content = 'CP'
              break
            case 'cp_top1_2':
              content = 'CP1'
              break
            case 'channel2':
              content = '公会分赛场1'
              break
            case 'channel3':
              content = '公会分赛场2'
              break
            case 'contribute':
              content = '贡献'
              break
          }

          return h('p', content)
        }
      }, {
        title: '角色IDs',
        width: 100,
        key: 'roleIds',
      }, {
        title: '榜单ID',
        width: 100,
        key: 'rankId',
      }, {
        title: '阶段ID',
        width: 100,
        key: 'phaseId',
      }, {
        title: '多榜单ID',
        width: 120,
        key: 'rankIdRange',
      }, {
        title: '多阶段ID',
        width: 120,
        key: 'phaseIdRange',
      }, {
        title: '榜单类型',
        width: 100,
        key: 'rankMapType',
        render: (h, params) => {
          let content
          switch (params.row.rankMapType) {
            case 1 :
              content = "赛程主榜"
              break
            case 2:
              content = "日榜/小时榜"
              break
            case 3:
              content = "PK榜"
              break
            case 4:
              content = "奖励"
              break
            case  5 :
              content = "贡献榜"
              break
            case 6:
              content = "分组排名"
              break
            case 7:
              content = "主榜-荣耀值"
              break
            case 8:
              content = "主播任务"
              break
            case 9:
              content = "第一名辅助榜"
              break
            case 10:
              content = "淘汰赛制"
              break
            case 11:
              content = "总榜"
              break
            case 12:
              content = "CP榜"
              break
            default:
              content = params.row.rankMapType
              break
          }
          return h('p', content)
        }
      }, {
        title: '日期格式',
        width: 120,
        key: 'dateKey',

      }, {
        title: '说明',
        width: 100,
        key: 'remark',

      }, {
        title: '分数来源',
        width: 100,
        key: 'scoreSource',
        render: (h, params) => {
          let content = params.row.scoreSource
          if (params.row.scoreSource === 0) {
            content = 'promot'
          } else if (params.row.scoreSource === 1) {
            content = 'pk'
          } else if (params.row.scoreSource === 2) {
            content = '贡献榜'
          }
          return h('p', content)
        }
      }, {
        title: '状态',
        width: 80,
        key: 'status',
        render: (h, params) => {
          let content = '无效'
          if (params.row.status === 1) {
            content = '有效';
          }
          return h('p', content)
        }
      }, {
        title: '操作',
        align: 'center',
        width: 150,
        render: (h, params) => {
          let update = h('Button', {
            style: {
              marginLeft: '5px'
            },
            props: {
              type: 'primary'
            },
            on: {
              click: () => {
                this.editItem(params.index)
              }
            }
          }, '编辑');
          return h('p', [update])
        }
      }],


      formItem: {
        actId: ''
      },
      editFormItem: {},

      loadingState: false,
      loadingStatus: false,
      opt_type: 1,
      addLayerState: false,
      roleRankMapValidate: {
        // actId: [{required: true, message: '不可为空', trigger: 'blur'}],
        //
        // remark: [{required: true, message: '不可为空', trigger: 'blur'}],
      },
      copyFormItem: {},
      copyLayerState: false,
      loadingCopyState: false,
    }
  },
  created: function () {
    this.loadActInfos()
  },
  methods: {
    //加载活动数据
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    queryRoleRankMap() {
      const self = this;
      let formItem = this.formItem
      if (formItem.actId === '') {
        return
      }
      let reqData = formItem
      self.loadingState = true;
      Api.queryRoleRankMap(reqData).then(res => {
        if (res.result === '0') {
          this.roleRankMapList = res.data
        } else {
          self.$Message.warning("查询失败:" + res.data)
        }
      });
      self.loadingState = false;
    },

    editItem: function (index) {
      debugger
      const self = this
      let data = self.roleRankMapList[index]
      data.scoreSource = data.scoreSource + '';
      data.status = data.status + '';
      data.rankMapType = data.rankMapType + '';
      console.log(JSON.stringify(data))
      self.setEditData(data)
    },
    // 父组件触发编辑操作，设置表单数据
    setEditData(data) {
      const self = this
      self.editFormItem = data
      self.opt_type = 2
      self.addLayerState = true
    },
    closeModal() {
      this.addLayerState = false
      this.loadingState = false
    },

    closeCopyModal() {
      this.copyLayerState = false
      this.loadingCopyState = false
    },

    addTask() {
      this.editFormItem = {
        status: 1,
        scoreSource: 0
      };
      this.opt_type = 1;
      this.addLayerState = true;
    },

    copyConfig() {
      this.copyFormItem = {
        srcActId: this.formItem.actId,
        targetActId: ''
      };
      this.copyLayerState = true;
    },

    addRoleRankMap(name) {
      const self = this
      let reqData = self.editFormItem
      let flag = false;
      this.$refs[name].validate((valid) => {
        if (valid) {
          flag = true;
        }
      });
      if (!flag) {
        this.loadingState = false
        return;
      }

      //reqData = JSON.stringify(reqData);
      Api.addRoleRankMap(reqData).then(res => {
        if (res.result == '0') {
          self.$Message.success("添加成功:" + res.data);
          self.queryRoleRankMap()
        } else {
          alert(("添加失败:" + res.data));
        }
      })

      this.addLayerState = false
      this.loadingState = false,
        this.queryRoleRankMap()
    },

    updateRoleRankMapWithValid(name) {
      this.$refs[name].validate().then(() => {
        this.updateRoleRankMap();
        this.editFormItem = {};
        this.queryRoleRankMap()
      }).catch(() => {
        this.loadingState = false
        this.editFormItem = {};
        this.queryRoleRankMap()
      });

    },

    updateRoleRankMap() {
      debugger
      const self = this
      let reqData = self.editFormItem;

      //reqData = JSON.stringify(reqData);
      console.log('reqdata->' + JSON.stringify(reqData))
      Api.updateRoleRankMap(reqData).then(res => {
        if (res.result === '0') {
          self.$Message.success("修改成功:" + res.data);
          self.queryRoleRankMap()
        } else {
          alert(("修改失败:" + res.data));
        }
      });
      this.addLayerState = false
      this.loadingState = false
    },

    copyRoleRankMap() {
      const self = this
      let reqData = self.copyFormItem;

      //reqData = JSON.stringify(reqData);
      console.log('reqdata->' + JSON.stringify(reqData))
      Api.copyRoleRankMap(reqData).then(res => {
        if (res.result === '0') {
          self.$Message.success("修改成功:" + res.data);
          this.formItem.actId = self.copyFormItem.targetActId;
          this.queryRoleRankMap();
        } else {
          alert(("修改失败:" + res.data));
        }
      });
      this.copyLayerState = false
      this.loadingCopyState = false
    }

  }
};
</script>
