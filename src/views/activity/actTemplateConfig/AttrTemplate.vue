<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-button icon="el-icon-search" size="medium" @click="loadTemplateAttr">查询</el-button>
          <el-button icon="el-icon-plus" type="primary" size="medium" @click="showAddDialog">添加</el-button>
        </el-form>
      </el-row>
      <el-row style="margin-top: 10px">
        <el-table :data="tableData" border>
          <el-table-column label="库" prop="db" align="center" width="120" />
          <el-table-column label="表" prop="tbl" align="center" width="200" />
          <el-table-column label="列" prop="col" align="center" width="150" />
          <el-table-column label="行" prop="rowIndex" align="center" width="60" />
          <el-table-column label="行条件" prop="templateCondition" align="center" width="500" />
          <el-table-column label="模板值" align="center" width="200">
            <template v-slot="scope">
              <el-link type="info" :underline="false" @click="showJson(scope.row)">点击查看模板值</el-link>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-link type="success" :underline="false" @click="showEditDialog(scope.row)">编辑</el-link>
              <el-link type="danger" :underline="false" @click="deleteTemplateAttr(scope.row.db, scope.row.tbl, scope.row.col, scope.row.rowIndex)">删除</el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <json-modal contentType="text" ref="previewModal" @submit="updateTemplate" />

      <el-dialog :title="editItem.edit ? '修改' : '新增'" :visible.sync="showDialog" :close-on-click-modal="false">
        <el-form label-width="150px" :model="editItem" :rules="rules" ref="editForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="库：">
                <el-input v-model="editItem.db" :readonly="editItem.edit" placeholder="填写DB" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="表：">
                <el-input v-model="editItem.tbl" :readonly="editItem.edit" placeholder="填写表" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="列：">
                <el-input v-model="editItem.col" :readonly="editItem.edit" placeholder="填写列（字段）" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="行：">
                <el-input v-model="editItem.rowIndex" :readonly="editItem.edit" placeholder="填写行号（从0开始）" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="条件：">
                <el-input v-model="editItem.templateCondition" placeholder="取行使用的条件，优先级高于“行”" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="模板：">
                <el-alert title="模板在列表中点击编辑" type="warning" :closable="false" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="saveTemplateAttr">保存</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>

// require component
import { queryTemplateAttrTemplate, addTemplateAttrTemplate, updateTemplateAttrTemplate, deleteTemplateAttrTemplate } from '@/api/activity/actTemplateConfig'
import JsonModal from "@/components/Json/JsonModal.vue"

export default {
  name: "AttrTemplate",
  components: { JsonModal },
  data: () => ({
    templateId: '',
    templateName: '',
    tableData: [],
    showDialog: false,
    editItem: {
      edit: false,
      db: '',
      tbl: '',
      col: '',
      rowIndex: 0,
      templateCondition: ''
    },
    rules: {
      db: [
        { required: true, message: '数据库不能为空', trigger: 'blur' }
      ],
      tbl: [
        { required: true, message: '表名不能为空', trigger: 'blur' }
      ],
      col: [
        { required: true, message: '列名不能为空', trigger: 'blur' }
      ],
      rowIndex: [
        { required: true, message: '行号不能为空', trigger: 'blur' },
        { type: 'number', message: '行号必须为数字' }
      ]
    }
  }),
  beforeMount() {
    this.loadTemplateAttr()
  },
  methods: {
    getTemplateId() {
      const _query = this.$route.query
      return _query.templateId
    },
    loadTemplateAttr() {
      const self = this
      queryTemplateAttrTemplate(this.getTemplateId()).then(res => {
        if (res.result !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.tableData = res.data
      }).catch(err => {
        self.$message({message: 'something came wrong', type: 'error'})
      })
    },
    showJson(row) {
      const title = `【${row.db}-${row.tbl}-${row.col}-${row.rowIndex}】模板值`
      this.$refs.previewModal.showModal(row.template, title, { db: row.db, tbl: row.tbl, col: row.col, rowIndex: row.rowIndex })
    },
    showAddDialog() {
      this.editItem.edit = false
      this.showDialog = true
    },
    showEditDialog(row) {
      this.editItem.edit = true
      this.editItem.db = row.db
      this.editItem.tbl = row.tbl
      this.editItem.col = row.col
      this.editItem.rowIndex = row.rowIndex
      this.editItem.templateCondition = row.templateCondition
      this.showDialog = true
    },
    addTemplateAttr() {
      if (this.editItem.edit) {
        this.$message({message: '当前是编辑模式，不能执行put操作', type: 'error'})
        return
      }
      const self = this
      const templateId = this.getTemplateId()
      addTemplateAttrTemplate({ ... this.editItem, templateId }).then(res => {
        if (res.result !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.$message({message: '添加成功', type: 'success'})
        self.showDialog = false
        self.loadTemplateAttr()
      }).catch(err => {
        self.$message({message: 'something came wrong', type: 'error'})
      })
    },
    updateTemplateAttr() {
      if (!this.editItem.edit) {
        this.$message({message: '当前是新增模式，不能执行post操作', type: 'error'})
        return
      }

      const self = this
      const templateId = this.getTemplateId()
      updateTemplateAttrTemplate({ ... this.editItem, templateId }).then(res => {
        if (res.result !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.$message({message: '修改成功', type: 'success'})
        self.showDialog = false
        self.loadTemplateAttr()
      }).catch(err => {
        self.$message({message: 'something came wrong', type: 'error'})
      })
    },
    saveTemplateAttr() {
      const self = this
      this.$refs.editForm.validate((valid) => {
        if (!valid) {
          self.$message({message: '填写信息有误，请检查', type: 'error'})
          return
        }

        if (self.editItem.edit) {
          self.updateTemplateAttr()
        } else {
          self.addTemplateAttr()
        }
      })
    },
    updateTemplate({ jsonValue, context }) {
      const self = this
      const templateId = this.getTemplateId()
      updateTemplateAttrTemplate({ ...context, templateId, template: jsonValue}).then(res => {
        if (res.result !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.$message({message: '修改成功', type: 'success'})
        self.showDialog = false
        self.loadTemplateAttr()
      }).catch(err => {
        self.$message({message: 'something came wrong', type: 'error'})
      })
    },
    deleteTemplateAttr(db, tbl, col, rowIndex) {
      const self = this
      const templateId = this.getTemplateId()
      this.$confirm('确定删除该配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTemplateAttrTemplate(templateId, db, tbl, col, rowIndex).then(res => {
          if (res.result !== 200) {
            self.$message({message: res.reason, type: 'error'})
            return
          }

          self.$message({message: '删除成功', type: 'success'})
          self.loadTemplateAttr()
        }).catch(err => {
          self.$message({message: 'something came wrong', type: 'error'})
        })
      }).catch(() => {
        self.$message({message: '删除取消', type: 'info'})
      })
    }
  }
}
</script>

<style>
</style>
