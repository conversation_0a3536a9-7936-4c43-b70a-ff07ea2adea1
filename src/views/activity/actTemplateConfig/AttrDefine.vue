<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-button icon="el-icon-search" size="medium" @click="loadTemplateDefine">查询</el-button>
          <el-button icon="el-icon-plus" type="primary" size="medium" @click="showAddDialog">添加</el-button>
        </el-form>
      </el-row>
      <el-row style="margin-top: 10px">
        <el-table :data="tableData" border>
          <el-table-column label="库" prop="db" align="center" width="120" />
          <el-table-column label="表" prop="tbl" align="center" width="200" />
          <el-table-column label="列" prop="col" align="center" width="150" />
          <el-table-column label="行" prop="rowIndex" align="center" width="60" />
          <el-table-column label="类型" prop="attrType" align="center" width="100" />
          <el-table-column label="名称" prop="attrName" align="center" width="150" />
          <el-table-column label="描述" prop="attrDesc" align="center" width="250" />
          <el-table-column label="必填" prop="required" align="center" width="60">
            <template v-slot="scope">
              <el-tag v-if="scope.row.required" type="success">是</el-tag>
              <el-tag v-else type="danger">否</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="权重" prop="weight" align="center" width="60" />
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-link type="success" :underline="false" @click="showEditDialog(scope.row)">编辑</el-link>
              <el-link type="danger" :underline="false" @click="deleteTemplateDefine(scope.row.db, scope.row.tbl, scope.row.col, scope.row.rowIndex)">删除</el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-row>

      <el-dialog :title="editItem.edit ? '修改' : '新增'" :visible.sync="showDialog" :close-on-click-modal="false">
        <el-form label-width="150px" :model="editItem" :rules="rules" ref="editForm">
          <el-row>
            <el-col :span="12">
              <el-form-item label="库：">
                <el-input v-model="editItem.db" :readonly="editItem.edit" placeholder="填写DB" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="表：">
                <el-input v-model="editItem.tbl" :readonly="editItem.edit" placeholder="填写表" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="列：">
                <el-input v-model="editItem.col" :readonly="editItem.edit" placeholder="填写列（字段）" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="行：">
                <el-input v-model="editItem.rowIndex" :readonly="editItem.edit" placeholder="填写行号（从0开始）" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="类型：">
                <el-input v-model="editItem.attrType" placeholder="取行使用的条件，优先级高于“行”" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="名称：">
                <el-input v-model="editItem.attrName" placeholder="取行使用的条件，优先级高于“行”" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="描述：">
                <el-input v-model="editItem.attrDesc" placeholder="取行使用的条件，优先级高于“行”" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="必填：">
                <el-switch v-model="editItem.required" active-color="#13ce66" inactive-color="#ff4949" />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="权重：">
                <el-input v-model="editItem.attrName" placeholder="取行使用的条件，优先级高于“行”" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="showDialog = false">取消</el-button>
          <el-button type="primary" @click="saveTemplateDefine">保存</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>

// require component
import { queryTemplateAttrDefine, addTemplateAttrDefine, updateTemplateAttrDefine, deleteTemplateAttrDefine } from '@/api/activity/actTemplateConfig'

export default {
  name: "AttrDefine",
  data: () => ({
    templateId: '',
    templateName: '',
    tableData: [],
    showDialog: false,
    editItem: {
      edit: false,
      db: '',
      tbl: '',
      col: '',
      rowIndex: 0,
      attrType: '',
      attrName: '',
      attrDesc: '',
      required: true,
      weight: 0
    },
    rules: {
      db: [
        { required: true, message: '数据库不能为空', trigger: 'blur' }
      ],
      tbl: [
        { required: true, message: '表名不能为空', trigger: 'blur' }
      ],
      col: [
        { required: true, message: '列名不能为空', trigger: 'blur' }
      ],
      rowIndex: [
        { required: true, message: '行号不能为空', trigger: 'blur' },
        { type: 'number', message: '行号必须为数字' }
      ]
    }
  }),
  beforeMount() {
    this.templateId = this.getTemplateId()
    this.loadTemplateDefine()
  },
  methods: {
    getTemplateId() {
      const _query = this.$route.query
      return _query.templateId
    },
    loadTemplateDefine() {
      const self = this
      queryTemplateAttrDefine(this.templateId).then(res => {
        if (res.result !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.tableData = res.data
      }).catch(err => {
        self.$message({message: 'something came wrong', type: 'error'})
      })
    },
    showAddDialog() {
      this.editItem.edit = false
      this.showDialog = true
    },
    showEditDialog(row) {
      this.editItem.edit = true
      this.editItem.db = row.db
      this.editItem.tbl = row.tbl
      this.editItem.col = row.col
      this.editItem.rowIndex = row.rowIndex
      this.editItem.templateCondition = row.templateCondition
      this.showDialog = true
    },
    addTemplateDefine() {
      if (this.editItem.edit) {
        this.$message({message: '当前是编辑模式，不能执行put操作', type: 'error'})
        return
      }
      const self = this
      addTemplateAttrDefine({ ... this.editItem, templateId: this.templateId }).then(res => {
        if (res.result !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.$message({message: '添加成功', type: 'success'})
        self.showDialog = false
        self.loadTemplateDefine()
      }).catch(err => {
        self.$message({message: 'something came wrong', type: 'error'})
      })
    },
    updateTemplateDefine() {
      if (!this.editItem.edit) {
        this.$message({message: '当前是新增模式，不能执行post操作', type: 'error'})
        return
      }

      const self = this
      updateTemplateAttrDefine({ ... this.editItem, templateId: this.templateId }).then(res => {
        if (res.result !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.$message({message: '修改成功', type: 'success'})
        self.showDialog = false
        self.loadTemplateDefine()
      }).catch(err => {
        self.$message({message: 'something came wrong', type: 'error'})
      })
    },
    saveTemplateDefine() {
      const self = this
      this.$refs.editForm.validate((valid) => {
        if (!valid) {
          self.$message({message: '填写信息有误，请检查', type: 'error'})
          return
        }

        if (self.editItem.edit) {
          self.updateTemplateDefine()
        } else {
          self.addTemplateDefine()
        }
      })
    },
    deleteTemplateDefine(db, tbl, col, rowIndex) {
      const self = this
      this.$confirm('确定删除该配置吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTemplateAttrDefine(self.templateId, db, tbl, col, rowIndex).then(res => {
          if (res.result !== 200) {
            self.$message({message: res.reason, type: 'error'})
            return
          }

          self.$message({message: '删除成功', type: 'success'})
          self.loadTemplateDefine()
        }).catch(err => {
          self.$message({message: 'something came wrong', type: 'error'})
        })
      }).catch(() => {
        self.$message({message: '删除取消', type: 'info'})
      })
    }
  }
}
</script>

<style>
</style>
