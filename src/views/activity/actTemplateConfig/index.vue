<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="模板ID">
            <el-input v-model="searchForm.templateId" placeholder="模板ID" />
          </el-form-item>
          <el-form-item label="模板名称">
            <el-input v-model="searchForm.templateName" placeholder="模板名称" />
          </el-form-item>
          <el-form-item label="基础活动ID">
            <el-input v-model="searchForm.baseActId" placeholder="基础活动ID" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.state" placeholder="状态" style="width: 130px" clearable>
              <el-option value="1" label="有效" />
              <el-option value="0" label="无效" />
            </el-select>
          </el-form-item>
          <el-button icon="el-icon-search" size="medium" @click="onSearch(true)">查询</el-button>
          <el-button icon="el-icon-plus" type="primary" size="medium">添加</el-button>
        </el-form>
      </el-row>

      <el-row>
        <el-table :data="tableData" border>
          <el-table-column label="模板ID" prop="id" align="center" width="150" />
          <el-table-column label="模板名称" prop="name" align="center" width="250" />
          <el-table-column label="基活动ID" prop="baseActId" align="center" width="150" />
          <el-table-column label="状态" prop="state" align="center" width="90" />
          <el-table-column label="拓展字段" align="center" width="150">
            <template v-slot="scope">
              <el-link type="info" :underline="false" @click="showJson(scope.row.extJson, '拓展字段')">点击查看拓展字段</el-link>
            </template>
          </el-table-column>
          <el-table-column label="基础配置" align="center" width="150">
            <template v-slot="scope">
              <el-link type="info" :underline="false" @click="showJson(scope.row.baseConfig, '基础配置')">点击查看基础配置</el-link>
            </template>
          </el-table-column>
          <el-table-column label="详情" prop="remark" align="center" width="450" />
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-link type="primary" :underline="false" @click="goAttrDefine(scope.row.id, scope.row.name)">自定义字段</el-link>
              <el-link type="success" :underline="false" style="margin-left: 5px" @click="goAttrTemplate(scope.row.id, scope.row.name)">模板字段</el-link>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          :page-sizes="[10, 15, 20, 50, 100]"
          @size-change="onSearch(false)"
          @current-change="onSearch(false)"
          layout="->, total, prev, pager, next, sizes"
          :current-page.sync="pageData.pageNo"
          :page-size.sync="pageData.pageSize"
          :total="pageData.total"
        />
      </el-row>
    </div>
    <json-modal ref="previewModal" />
  </div>
</template>

<script>
import { queryPageTemplateInfo } from '@/api/activity/actTemplateConfig'
import JsonModal from "@/components/Json/JsonModal.vue"
export default {
  name: "actTemplateConfig",
  components: { JsonModal },
  data: () => {
    return {
      searchForm: {
        templateId: '',
        templateName: '',
        baseActId: '',
        state: ''
      },
      tableData: [],
      pageData: {
        pageNo: 1,
        pageSize: 15,
        total: 0
      }
    }
  },
  beforeMount() {
    this.onSearch(true)
  },
  methods: {
    onSearch(fromBtn) {
      if (fromBtn) {
        this.pageData.pageNo = 1
      }
      const self = this
      const params = { ...this.searchForm, ...this.pageData }
      queryPageTemplateInfo(params).then(res => {
        if (res.code !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.tableData = res.data
        self.pageData.total = res.total
      })
    },
    showJson(extJson, title) {
      this.$refs.previewModal.showModal(extJson, title)
    },
    goAttrTemplate(templateId, templateName) {
      this.$router.push({path: 'actTemplateTemplate', query: { templateId, templateName }})
    },
    goAttrDefine(templateId, templateName) {
      this.$router.push({path: 'actTemplateDefine', query: { templateId, templateName }})
    }
  }
}
</script>

<style scoped lang="scss">

</style>
