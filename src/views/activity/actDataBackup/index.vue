<template>
  <div class="app-container">
    <Form :label-width="90" :model="formItem">
      <FormItem label="活动ID" prop="actId" >
        <Input v-model="formItem.actId" placeholder="Enter actId" required="true" style="width: 184px"/>
        <Button :loading="loadingState" type="primary" @click="backUpActData()" style="margin-left: 8px">备份</Button>
      </FormItem>

    </Form>

    <Table :columns="columns" :data="tableNameList"></Table>

  </div>
</template>

<script>
  import Api from '@/api/activity/actDataBackup'

  export default {
    name: "ActDataBackup",
    components: {},
    data: function () {
      return {
        columns: [
          {
            title: '活动ID',
            key:'actId'
          },{
            title: '操作',
            align: 'center',
            render: (h, params) => {
              let edit = h('Button', {
                style: {
                  marginRight: '5px'
                },
                props: {
                  type: 'primary'
                },
                on: {
                  click: () => {
                    this.dropActDataTable(params.row.actId)
                  }
                }
              }, '删除备份数据');
              return h('p', [edit])
            }
          }
        ],
        tableNameList: [],
        loadingState: false,
        loadingState1: false,
        formItem: {

        }
      }
    },

    mounted() {
      this.listAllBackupAct()
    },


    methods: {
      backUpActData() {
        debugger;
        const self = this;
        if (!self.formItem.actId) {
          self.$Message.warning("请输入活动ID")
          return;
        }
        self.loadingState = true;
        Api.backupActData(self.formItem).then(res => {
          if (res.result === '0') {
            self.loadingState = false;
            self.$Message.success("操作成功")
          } else {
            self.$Message.warning(res.data)
          }
        })/*.catch((error) => {
          self.loadingState = false;
          alert(error.status + "," + error.statusText);
        });*/
      },

      dropActDataTable(tableName) {
        debugger;
        const self = this;
        let reqData = {"tableName": tableName}
        self.loadingState1 = true;
        Api.dropActDataTable(reqData).then(res => {
          if (res.result === '0') {
            self.$Message.success("操作成功")
            this.listAllBackupAct();
            self.loadingState1 = false;
          } else {
            self.$Message.warning(res.data)
          }
        })/*.catch((error) => {
          self.loadingState1 = false;
          alert(error.status + "," + error.statusText);
        });*/
      },

      listAllBackupAct() {
        const self = this;
        Api.listAllBackupAct().then(res => {
          debugger;
          if (res.result === '0') {
            this.tableNameList = res.data
          } else {
            self.$Message.warning(res.data)
          }
        })
      }

    }
  };
</script>
