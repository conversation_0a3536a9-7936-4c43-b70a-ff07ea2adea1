<template>
  <div class="app-container">
    <el-form :inline="true" label-width="90" class="demo-form-inline">
      <el-form-item label="活动ID">
        <el-select v-model="actId" filterable placeholder="请选择活动"
                   allow-create default-first-option
                   style="width:300px">
          <el-option
            v-for="item in selectActList"
            :key="item.act_id"
            :label="item.act_name+'('+item.act_id+')'"
            :value="item.act_id">
          </el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="">
        <el-radio-group v-model="configType" size="medium">
          <el-radio :label="0">中控</el-radio>
          <el-radio :label="1">奖包</el-radio>
        </el-radio-group>
      </el-form-item>-->
      <el-button type="primary" :loading="loadingTableData" @click="onSearch" icon="el-icon-search" size="medium">
        查询
      </el-button>
      <el-button type="primary" icon="el-icon-edit-outline" size="medium" @click="onAddResource">添加资源</el-button>
      <el-button type="primary" icon="el-icon-copy-document" size="medium" @click="copyModalVisible = true">复制以往</el-button>
    </el-form>

    <el-main>
      <el-table :data="tableData" border>
        <el-table-column label="资源标识" prop="resourceKey" align="center" width="270" />
        <el-table-column label="资源地址" prop="resourceUrl" align="center" />
        <el-table-column label="资源描述" prop="resourceDesc" align="center" width="350" />
        <el-table-column label="操作" align="center" width="120">
          <template v-slot="scope">
            <el-link type="success" @click="onEditResource(scope.row)">编辑</el-link>
            <el-link type="primary" @click="onCopyResource(scope.row)">复制</el-link>
            <el-link type="warning" @click="onDeleteResource(scope.row)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>
    </el-main>

    <el-dialog title="活动资源" :visible.sync="editFromVisible" @close="editFormMode = 0">
      <el-form :model="editForm" label-width="150px">
        <el-form-item label="活动id">
          <el-select v-model="editForm.actId" filterable placeholder="请选择活动" :disabled="editFormMode === 2" style="width:100%;">
            <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id" :label='item.act_name+"("+item.act_id+")"' />
          </el-select>
        </el-form-item>

        <el-form-item label="资源标识">
          <el-input v-model="editForm.resourceKey" :readonly="editFormMode === 2" />
        </el-form-item>

        <el-form-item label="资源URL">
          <image-upload style="width: 100%" v-model="editForm.resourceUrl" />
        </el-form-item>

        <el-form-item label="资源描述">
          <el-input v-model="editForm.resourceDesc" placeholder="请填写资源描述" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editFormMode = 0">取 消</el-button>
        <el-button type="primary" @click="onSaveResource">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="拷贝资源" width="80%" :visible.sync="copyModalVisible" @close="copyModalVisible = false">
      <el-form :inline="true">
        <el-form-item label="被拷贝活动id">
          <el-select v-model="copyActId" filterable placeholder="请选择活动" style="width:100%;" @change="loadCopyActResources">
            <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id" :label='item.act_name+"("+item.act_id+")"' />
          </el-select>
        </el-form-item>
        <el-main>
          <el-table :data="copyTableData">
            <el-table-column label="资源标识" prop="resourceKey" align="center" width="270" />
            <el-table-column label="资源地址" prop="resourceUrl" align="center" />
            <el-table-column label="资源描述" prop="resourceDesc" align="center" width="350" />
          </el-table>
        </el-main>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="copyModalVisible = false">取 消</el-button>
        <el-button type="primary" @click="onCopyActResources">拷 贝</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import ActConfigVersion from '@/api/activity/actConfigVersion'
import ImageUpload from '@/views/activity/cmptAttrConfig/cmpt/ImageUpload'
import {queryActResources, addActResource, updateActResource, deleteActResource, copyActResources} from '@/api/activity/actResource'
export default {
  name: "ActResource",
  components: { ImageUpload },
  data: function () {
    return {
      actId: '',
      // configType: 0,
      selectActList: [],
      tableData: [],
      loadingTableData: false,
      editFormMode: 0,
      editForm: {
        actId: '',
        resourceKey: '',
        resourceUrl: '',
        resourceType: '',
        resourceDesc: ''
      },
      copyModalVisible: false,
      copyActId: '',
      copyTableData: []
    }
  },
  computed: {
    editFromVisible: function () {
      return this.editFormMode > 0
    }
  },
  beforeMount() {
    const _query = this.$route.query
    const _actId = _query?.actId
    if (_actId) {
      this.actId = Number(_actId)
      this.onSearch()
    }
    this.loadActInfos()
  },
  methods: {
    //加载活动数据
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    onSearch() {
      const self = this
      queryActResources(this.actId).then(res => {
        if (res && res.result === 200) {
          self.tableData = res.data
          return
        }

        self.$message({message: '获取列表数据失败:' + res.reason, type: 'warning'})
      }).catch(e => {
        self.$message({message: '获取失败', type: 'warning'})
      })
    },
    onEditResource(_row) {
      this.editForm.actId = _row.actId
      this.editForm.resourceKey = _row.resourceKey
      this.editForm.resourceType = _row.resourceType
      this.editForm.resourceUrl = _row.resourceUrl
      this.editForm.resourceDesc = _row.resourceDesc

      this.editFormMode = 2
    },
    onAddResource() {
      this.editForm.actId = ''
      this.editForm.resourceKey = ''
      this.editForm.resourceType = ''
      this.editForm.resourceUrl = ''
      this.editForm.resourceDesc = ''

      this.editFormMode = 1
    },
    onCopyResource(_row) {
      this.editForm.actId = _row.actId
      this.editForm.resourceKey = _row.resourceKey
      this.editForm.resourceType = _row.resourceType
      this.editForm.resourceUrl = _row.resourceUrl
      this.editForm.resourceDesc = _row.resourceDesc

      this.editFormMode = 3
    },
    onSaveResource() {
      if (!this.editForm.actId || !this.editForm.resourceKey || !this.editForm.resourceUrl) {
        this.$message({message: '请先完善信息', type: 'warning'})
        return
      }
      const self = this
      let deferred = ((this.editFormMode === 1 || this.editFormMode === 3) ? addActResource(this.editForm) : updateActResource(this.editForm))
      deferred.then(res => {
        if (res && res.result === 200) {
          self.$message({message: '保存成功', type: 'success'})
          self.editFormMode = 0
          self.onSearch()
          return
        }

        self.$message({message: '保存失败:' + res.reason, type: 'warning'})
      }).catch(err => {
        self.$message({message: '保存失败', type: 'error'})
      })
    },
    onDeleteResource(_row) {
      const self = this
      this.$confirm('删除【' + _row.resourceDesc + '】资源？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '我再想想',
        type: 'info'
      }).then(() => {
        deleteActResource(_row.actId, _row.resourceKey).then(res => {
          if (res && res.result === 200) {
            self.$message({message: '删除成功', type: 'success'})
            self.onSearch()
            return
          }

          self.$message({message: '删除失败:' + res.reason, type: 'warning'})
        }).catch(err => {
          self.$message({message: err, type: 'error'})
        })
      }).catch(() => {})
    },
    loadCopyActResources() {
      if (!this.copyActId) {
        this.copyTableData = []
        return
      }
      const self = this
      queryActResources(this.copyActId).then(res => {
        if (res && res.result === 200) {
          self.copyTableData = res.data
        }
      })
    },
    onCopyActResources() {
      if (!this.actId) {
        this.$message({message: '请先在主页面选择活动id', type: 'error'})
        return
      }

      if (!this.copyActId) {
        this.$message({message: '请选择要被拷贝活动id', type: 'error'})
        return
      }

      const self = this
      copyActResources(this.actId, this.copyActId).then(res => {
        if (res && res.result === 200) {
          self.$message({message: '拷贝成功', type: 'success'})
          self.onSearch()
          self.copyModalVisible = false
          return
        }

        self.$message({message: '拷贝失败:' + res.reason, type: 'warning'})
      }).catch(err => {
        self.$message({message: err, type: 'error'})
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
