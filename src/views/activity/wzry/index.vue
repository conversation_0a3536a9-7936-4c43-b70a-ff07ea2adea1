<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="用户UID">
            <el-input v-model="uid" placeholder="输入用户Uid" style="width:150px" />
          </el-form-item>
          <el-form-item label="用户YY">
            <el-input v-model="yyno" placeholder="输入用户YY号" style="width:150px" />
          </el-form-item>
          <el-form-item label="比赛ID">
            <el-input v-model="gameId" placeholder="输入比赛ID" />
          </el-form-item>
          <el-form-item label="比赛状态">
            <el-select v-model="gameState" placeholder="选择游戏状态" style="width:140px">
              <el-option value="0">初始化</el-option>
              <el-option value="100">异常取消</el-option>
              <el-option value="200">快速赛</el-option>
              <el-option value="400">赛宝人齐</el-option>
              <el-option value="400">游戏进行中</el-option>
              <el-option value="900">正常结束</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="比赛时间">
            <el-date-picker
              v-model="gameTime"
              type="datetimerange"
              :picker-options="pickerOptions"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              align="right">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch()" icon="el-icon-search"
                       size="medium">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <el-table :data="tableData" border>
          <el-table-column type="expand">
            <template slot-scope="props">
              <div>
                <el-table :data="props.row.gameTeams" border stripe>
                  <el-table-column type="selection" width="50" align="center" />
                  <el-table-column label="UID" prop="uid" width="150" align="center" />
                  <el-table-column label="进赛宝房间" prop="inRoomRecordTime" width="200" align="center" />
                  <el-table-column label="进游戏房间" prop="inGameRecordTime" width="200" align="center" />
                  <el-table-column width="120" align="center">
                    <template slot="header">
                      <span>是否进游戏</span>
                      <el-tooltip effect="light" placement="right-start" >
                        <div slot="content">当前从腾讯读取的最新数据</div>
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </template>
                    <template slot-scope="scp">
                      <span v-if="scp.row.saibaoInGame" class="text-yes">是</span>
                      <span v-else class="text-no">否</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="是否已发奖" width="120" align="center">
                    <template slot-scope="scp">
                      <span v-if="scp.row.award" class="text-yes">是</span>
                      <span v-else class="text-no">否</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="是否已退票" width="120" align="center">
                    <template slot-scope="scp">
                      <span v-if="scp.row.refund" class="text-yes">是</span>
                      <span v-else class="text-no">否</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="座位编号" prop="seatId" width="100" align="center" />
                  <el-table-column label="所在队伍" prop="limitTeam" width="130" align="center">
                    <template slot-scope="scp">
                      <el-tag v-if="scp.row.limitTeam === 1" type="danger" effect="plain">A队</el-tag>
                      <el-tag v-if="scp.row.limitTeam === 2"  effect="plain">B队</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="saibaoWinTeam" width="130" align="center">
                    <template slot="header">
                      <span>胜利队伍</span>
                      <el-tooltip effect="light" placement="right-start" >
                        <div slot="content">当前从腾讯读取的最新数据</div>
                        <i class="el-icon-info"></i>
                      </el-tooltip>
                    </template>
                    <template slot-scope="scp">
                      <el-tag v-if="scp.row.saibaoWinTeam === 1" type="danger" effect="plain">A队</el-tag>
                      <el-tag v-else-if="scp.row.saibaoWinTeam === 2"  effect="plain">B队</el-tag>
                      <el-tag v-else type="info" effect="plain">无结果</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作">
                    <template slot-scope="scp">
                      <el-link type="primary" :underline="false" @click="onClickRefundUser(props.row.gameId, scp.row.uid, scp.row.quickRace)">退票</el-link>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="游戏ID" prop="gameId" width="350" align="center" />
          <el-table-column label="赛宝房间ID" prop="childId" width="200" align="center" />
          <el-table-column label="游戏模式" prop="battleMode" width="150" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.battleMode === 0" size="medium">5V5</el-tag>
              <el-tag v-else-if="scope.row.battleMode === 1" size="medium" type="info">1V1</el-tag>
              <el-tag v-else-if="scope.row.battleMode === 3" size="medium" type="warning">3V3</el-tag>
              <el-tag v-else size="medium" type="danger">未知</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="游戏开始时间" prop="gameStartTime" width="200" align="center" />
          <el-table-column label="游戏状态" prop="gameState" width="150" align="center">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.gameState === 0" type="info" effect="plain">初始化</el-tag>
              <el-tag v-else-if="scope.row.gameState === 100" type="danger" effect="dark">异常取消</el-tag>
              <el-tag v-else-if="scope.row.gameState === 50" effect="plain">1V1初始</el-tag>
              <el-tag v-else-if="scope.row.gameState === 200" type="warning" effect="plain">5V5快速赛</el-tag>
              <el-tag v-else-if="scope.row.gameState === 300" type="success" effect="plain">赛宝人齐</el-tag>
              <el-tag v-else-if="scope.row.gameState === 400" type="success">比赛中</el-tag>
              <el-tag v-else-if="scope.row.gameState === 900" type="success" effect="dark">正常结束</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template slot-scope="scope">
              <el-link v-if="scope.row.gameState === 100" type="primary" :underline="false" @click="onClickCloseGame(scope.row.gameId, scope.row.gameStartTime, scope.row.battleMode)">触发结算</el-link>
<!--              <el-link type="primary" :underline="false" style="margin-left: 5px">选中用户退票</el-link>-->
            </template>
          </el-table-column>
        </el-table>
      </el-row>
    </div>
  </div>
</template>
<script>
import { queryGameList, reCloseGame, refundUser } from '@/api/activity/wzry'
import { formatDate } from '@/utils/index'
export default {
  name: "WzryGameList",
  data() {
    return {
      actId: 2024057001,
      uid: '',
      yyno: '',
      gameId: '',
      gameState: '',
      gameTime: '',
      pickerOptions: {
        shortcuts: [{
          text: '最新1天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最新3天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
            picker.$emit('pick', [start, end])
          }
        }, {
          text: '最新7天',
          onClick(picker) {
            const end = new Date()
            const start = new Date()
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
            picker.$emit('pick', [start, end])
          }
        }]
      },
      tableData: []
    }
  },
  beforeMount() {
    const _query = this.$route.query
    const _actId = _query?.actId
    const _gameId = _query?.gameId
    const _uid = _query?.uid
    const _yyno = _query?.yyno
    const _days = _query?.days
    if (_actId) {
      this.actId = Number(_actId)
    }
    if (_gameId) {
      this.gameId = _gameId
      this.$nextTick(() => {
        this.onSearch()
      })
      return
    }

    if (_days) {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * parseInt(_days))
      this.gameTime = [formatDate(start), formatDate(end)]
    }

    if (_uid && _days) {
      this.uid = _uid
      this.$nextTick(() => {
        this.onSearch()
      })
      return
    }

    if (_yyno && _days) {
      this.yyno = _yyno
      this.$nextTick(() => {
        this.onSearch()
      })
    }
  },
  methods: {
    onSearch() {
      const startTime = this.gameTime?.[0]
      const endTime = this.gameTime?.[1]
      if (!this.gameId && (!startTime || !endTime || (!this.uid && !this.yyno))) {
        this.$message({message: '查询必须指定比赛ID 或 UID+时间范围', type: 'warning'})
        return
      }
      const self = this
      queryGameList(this.actId, this.uid, this.yyno, this.gameId, this.gameState, startTime, endTime).then(res => {
        if (res.result === 200) {
          self.tableData = res.data
          return
        }

        self.$message({message: res.reason, type: 'error'})
      })
    },
    onClickCloseGame(gameId, gameStartTime, battleMode) {
      const gameType = battleMode === 0 ? '5V5' : '1V1'
      const self = this
      this.$confirm(`重新触发结算${gameStartTime}这场${gameType}比赛？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        reCloseGame(this.actId, gameId).then(res => {
          if (res.result === 0) {
            self.$message({message: '处理成功', type: 'success'})
            self.$nextTick(() => {
              self.onSearch()
            })
            return
          }

          self.$message({message: res.reason, type: 'error'})
        }).catch(err => {
          console.log(err)
          self.$message({message: '出错了', type: 'error'})
        })
      }).catch(() => {
        self.$message({message: '结算已取消', type: 'info'})
      })
    },
    onClickRefundUser(gameId, uid, quickRace) {
      const self = this
      this.$prompt(`给【${gameId}】这场比赛的【${uid}】退票？并确认退票数量`, '退票提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: '5',
        inputPattern: /\d+/
      }).then(({value}) => {
        refundUser(this.actId, gameId, uid, quickRace ? 1 : 0, value).then(res => {
          if (res.result === 0) {
            // self.$message({message: '处理成功', type: 'success'})
            self.$alert(res.reason, '请求成功', {
              confirmButtonText: '确定',
              callback: action => {
                // self.onSearch()
                console.log(res)
              }
            })
            return
          }

          self.$message({message: res.reason, type: 'error'})
        }).catch(err => {
          console.log(err)
          self.$message({message: '出错了', type: 'error'})
        })
      }).catch(() => {
        self.$message({message: '退票已取消', type: 'info'})
      })
    }
  }
}
</script>

<style scoped lang="scss">
.text-yes {
  color: #111111;
}

.text-no {
  color: #848585;
  font-weight: lighter;
}
</style>
