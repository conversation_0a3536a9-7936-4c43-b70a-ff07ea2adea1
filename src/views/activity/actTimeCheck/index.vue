<template>
<div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="活动id">
            <el-select v-model="currentActId" placeholder="请选择活动"
                       filterable style="width:180px">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-button type="info" :loading="checkingActivity" @click="checkActivity()" size="medium">
            校验
          </el-button>
          <el-tooltip class="item" effect="light" content="选择需要离线操作的活动进行操作" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>
      <el-row>
        <div v-loading="checkingActivity">
          <div v-if="currentActId === ''">
            <p>请选择要校验的活动，并点击“校验”按钮</p>
          </div>
          <div v-if="currentActId !== ''" style="height: 650px;">
            <el-steps v-if="allCheckers" direction="vertical" :active="checkStep" finish-status="success">
              <el-step v-for="checker in allCheckers" :key="checker.checkerId" 
              :title="checker.checkerName" 
              :description="checker.checkState === -1 ? checker.checkResult : checker.checkerDesc" :status="toStepStatus(checker.checkState)">
              </el-step>
            </el-steps>
          </div>
        </div>
      </el-row>
    </div>
  </div>
</template>

<script>
import API from '@/api/activity/actTimeCheck'
import ActConfigVersion from '@/api/activity/actConfigVersion'

export default {
    name: 'ActTimeCheck',
    data: () => ({
        checkingActivity: false,
        checkStep: 0,
        selectActList: [],
        currentActId: '',
        allCheckers: []
    }),
    created: function() {
        this.loadActInfos()
    },
    methods: {
        loadActInfos: function() {
            const self = this
            ActConfigVersion.loadActInfos().then(res => {
                res.result === 200 && (self.selectActList = res.data)
                res.result === 200 || self.$message({'message': res.reason, 'type': 'warning'})
            })
        },
        loadAllCheckers: function(next) {
            const self = this
            self.allCheckers = []
            self.checkStep = 0
            self.checkingActivity = true
            API.queryAllCheckers().then(res => {
                self.checkingActivity = false
                if (res.result !== 200) {
                    self.$message({'message': res.reason, 'type': 'warning'})
                    return
                }

                self.allCheckers = res.data
                next && next()
                
            }).catch(error => {
                self.checkingActivity = false
                self.$message({'message': '请求所有checker失败', 'type': 'warning'})
                console.log(error)
            })
        },
        checkActivity: function() {
            if (this.currentActId === '') {
                this.$message({'message': '请先选择活动', 'type': 'warning'})
            }
            
            const self = this
            this.loadAllCheckers(() => {
                for(let checker of self.allCheckers) {
                    self.checkActivityTimeItem(checker)
                }
            })
        },
        checkActivityTimeItem: function(checker) {
            checker.checkState = 99
            checker.checkResult = ''
            const self = this
            API.checkActivity(this.currentActId, checker.checkerId).then(res => {
                if (res.result !== 200) {
                    self.$message({'message': res.reason, 'type': 'warning'})
                    return
                }

                checker.checkState = res.data.checkState
                checker.checkResult = res.data.checkResult
            }).catch(error => {
                self.$message({'message': '时间校验失败', 'type': 'warning'})
                console.log(error)
            })
        },
        toStepStatus: (checkState) => {
            if (checkState === 0) {
                return 'wait'
            } else if (checkState === 1) {
                return 'success'
            } else if (checkState === -1) {
                return 'error'
            }

            return 'process'
        }
    },
    watch: {
        currentActId(newVal, oldVal) {
            if (oldVal !== '' && newVal === '') {
                this.allCheckers = []
                this.checkStep = 0
                this.checkingActivity = false
            }
            
            if (newVal !== '') {
                this.loadAllCheckers()
            }
        }
    },
    filters: {
        
    }
}
</script>