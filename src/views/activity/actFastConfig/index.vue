<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="模板">
            <el-select v-model="searchForm.templateId" placeholder="请选择模板" clearable>
              <el-option v-for="tpl in templates" :key="tpl.id" :value="tpl.id" :label="tpl.name" />
            </el-select>
          </el-form-item>
          <el-form-item label="套数">
            <el-select v-model="searchForm.group" placeholder="请选择套数" style="width: 130px" clearable>
              <el-option value="1" label="第一套" />
              <el-option value="2" label="第二套" />
              <el-option value="3" label="第三套" />
              <el-option value="4" label="第四套" />
              <el-option value="5" label="第五套" />
              <el-option value="6" label="第六套" />
              <el-option value="7" label="第七套" />
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择活动状态" style="width: 180px" clearable>
              <el-option v-for="item in stateList" :value="item.dictValue" :label="item.dictLabel" :key="item.dictCode" />
            </el-select>
          </el-form-item>
          <el-form-item label="活动开始时间范围">
            <el-date-picker v-model="searchForm.timeRange" value-format="yyyy-MM-dd HH:mm:ss" type="datetimerange" range-separator="至" start-placeholder="开始时间" end-placeholder="结束时间" />
          </el-form-item>
          <el-button icon="el-icon-search" size="medium" @click="onSearch(true)">查询</el-button>
          <el-popover placement="bottom" width="200" trigger="click" v-model="addSelectVisible">
            <el-select value="" placeholder="选择模板" @change="onAddTemplateAct">
              <el-option v-for="tpl in templates" v-if="tpl.id > 1000000" :key="tpl.id" :value="tpl.id" :label="tpl.name" />
            </el-select>
            <el-button slot="reference" type="primary" icon="el-icon-plus" size="medium">从模板添加</el-button>
          </el-popover>
          <el-button type="info" icon="el-icon-plus" size="medium" @click="copyActivityVisible = true">直接拷贝</el-button>
        </el-form>
      </el-row>
      <el-row>
        <el-table :data="tableData" border>
          <el-table-column label="活动名称" prop="actName" align="center" />
          <el-table-column label="活动ID" prop="actId" align="center" />
          <el-table-column label="套数" prop="group" align="center" />
          <el-table-column label="状态" prop="status" align="center">
            <template v-slot="scope">
              {{ scope.row.status | getStatusText(stateList) }}
            </template>
          </el-table-column>
          <el-table-column label="开始时间" prop="beginTime" align="center" />
          <el-table-column label="结束时间" prop="endTime" align="center" />
          <el-table-column label="模板信息" align="center">
            <template v-slot="scope">
              <el-popover v-if="scope.row.templateInfo" placement="top-start" trigger="click" width="300">
                <el-descriptions :column="1" title="模板详情">
                  <el-descriptions-item label="模板ID：">{{ scope.row.templateInfo.id }}</el-descriptions-item>
                  <el-descriptions-item label="模板名称：">{{ scope.row.templateInfo.name }}</el-descriptions-item>
                  <el-descriptions-item label="基础活动：">{{ scope.row.templateInfo.baseActId }}</el-descriptions-item>
                </el-descriptions>
                <el-tag slot="reference" title="点击查看模板信息">{{ scope.row.templateInfo.name }}</el-tag>
              </el-popover>
              <el-tag v-else type="info">无模板</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center">
            <template v-slot="scope">
              <el-link type="success" v-if="scope.row.templateInfo" :underline="false" :href="'https://hd-admin-in-test.yy.com/ge_baby_month_admin/pc/html/index.html#/?actId=' + scope.row.actId + '&templateId=' + scope.row.actType" target="_blank">编辑</el-link>
              <el-link type="danger" v-if="scope.row.status < 10000" :underline="false" @click="onClickDeleteFlushedConfig(scope.row.actId)">擦除</el-link>
              <el-link type="warning" v-else :underline="false" @click="onDeleteTemplateActivity(scope.row.actId, scope.row.actName)">删除</el-link>
              <el-link type="success" v-if="scope.row.templateInfo && JSON.parse(scope.row.templateInfo.extJson).canExport" :underline="false" :href="`https://${isTest ? 'test-' : ''}manager-hdzt.yy.com/admin-static/commonTool/rankData?actId=${scope.row.actId }&templateId=${scope.row.actType}&phaseId=${JSON.parse(scope.row.templateInfo.extJson).phaseId}&rankId=${JSON.parse(scope.row.templateInfo.extJson).rankId}`" target="_blank"> 榜单导出</el-link>
              <el-link type="success" v-if="scope.row.templateInfo && JSON.parse(scope.row.templateInfo.extJson).webUrl" :underline="false" :href="scope.row.templateInfo && JSON.parse(scope.row.templateInfo.extJson).webUrl" target="_blank">榜单跳转</el-link>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          :page-sizes="[10,15,20,50,100]"
          @size-change="onSearch(false)"
          @current-change="onSearch(false)"
          layout="->, total, prev, pager, next, sizes"
          :current-page.sync="pageData.page"
          :page-size.sync="pageData.size"
          :total="pageData.total">
        </el-pagination>
      </el-row>
    </div>
    <el-dialog title="直接拷贝活动" :visible.sync="copyActivityVisible" :close-on-click-modal="false" width="50%">
      <el-form label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="源活动ID">
              <el-select v-model="copyActivityRequest.baseActId" placeholder="请选择活动" filterable>
                <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                           :label='item.act_name+"("+item.act_id+")"'>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="新活动ID">
              <el-input v-model="copyActivityRequest.actId" placeholder="请输入新活动ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="22">
            <el-form-item label="新活动名称">
              <el-input v-model="copyActivityRequest.actName" placeholder="请输入新活动名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="开始时间">
              <el-date-picker v-model="copyActivityRequest.beginTime" type="datetime" placeholder="请选择活动开始时间" value-format="yyyy-MM-dd HH:mm:ss" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间">
              <el-date-picker v-model="copyActivityRequest.endTime" type="datetime" placeholder="请选择活动结束时间" value-format="yyyy-MM-dd HH:mm:ss" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="copyActivityVisible = false">取 消</el-button>
        <el-button type="info" @click="onPreviewCopyActivity">预 览</el-button>
        <el-button type="primary" @click="onSubmitCopyActivity" v-loading="savingCopyRequest">保 存</el-button>
      </div>
    </el-dialog>
    <json-modal ref="previewModal" />
  </div>
</template>

<script>
import { deleteTemplateAct, deleteFlushedConfig, copyActivity, getActStateList, getActTemplateList, queryFastConfigActList, queryCopyActivityPreview } from '@/api/activity/actFastConfig'
import JsonModal from "@/components/Json/JsonModal.vue"
import ActConfigVersion from '@/api/activity/actConfigVersion'
export default {
  name: "actFastConfig",
  components: { JsonModal },
  data: () => {
    return {
      searchForm: {
        templateId: '',
        group: '',
        status: '',
        timeRange: []
      },
      tableData: [],
      pageData: {
        page: 1,
        size: 15,
        total: 0
      },
      stateList: [],
      templates: [
        {
          id: '1000000',
          name: '无模板'
        }
      ],
      selectActList: [],
      addSelectVisible: false,
      copyActivityVisible: false,
      copyActivityRequest: {
        baseActId: '',
        actId: '',
        actName: '',
        beginTime: '',
        endTime: ''
      },
      savingCopyRequest: false,
      isTest: window.location.host.includes('test-') || window.location.host.includes('local')
    }
  },
  beforeMount() {
    this.loadTemplateList()
    this.loadStateList(() => this.onSearch(true))
    this.loadActInfos()
  },
  filters: {
    getStatusText(status, stateList) {
      for (const stateItem of stateList) {
        if (stateItem.dictValue == status) {
          return '[' + status + ']' + stateItem.dictLabel
        }
      }
      return '未知'
    }
  },
  methods: {
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    onSearch(fromBtn) {
      if (fromBtn) {
        this.pageData.page = 1
      }
      const self = this
      const beginTime =  this.searchForm?.timeRange?.[0] || ''
      const endTime = this.searchForm?.timeRange[1] || ''
      const params = { ...this.searchForm, beginTime, endTime, page: this.pageData.page, pageSize: this.pageData.size }
      queryFastConfigActList(params).then(res => {
        if (res.code !== 200) {
          self.$message({message: res.reason, type: 'error'})
          return
        }

        self.tableData = res.data
        self.pageData.total = res.total
      })
    },
    loadStateList(callback) {
      const self = this
      getActStateList().then(res => {
        if (res.code === 200) {
          self.stateList = res.data
          if (callback) {
            callback()
          }
        }
      })
    },
    loadTemplateList() {
      const self = this
      getActTemplateList().then(res => {
        if (res.result === 200 && self.templates.length === 1) {
          res.data.forEach(template => self.templates.push(template))
        }
      })
    },
    onAddTemplateAct(templateId) {
      window.open('https://hd-admin-in-test.yy.com/ge_baby_month_admin/pc/html/index.html#/?templateId=' + templateId, '_blank')
      this.addSelectVisible = false
    },
    onPreviewCopyActivity() {
      const self = this
      queryCopyActivityPreview(this.copyActivityRequest).then(res => {
        if (res.result === 200) {
          self.$refs.previewModal.showModal(res.data, '配置预览')
        }
      })
    },
    onSubmitCopyActivity() {
      const self = this
      this.savingCopyRequest = true
      copyActivity(this.copyActivityRequest).then(res => {
        if (res.result === 200) {
          self.$message({message: '拷贝成功', type: 'success'})
          self.onSearch(true)
          self.copyActivityVisible = false
          return
        }

        self.$message({message: res.reason, type: 'error'})
      }).catch(err => {
        console.log(err)
      }).finally(() => {
        self.savingCopyRequest = false
      })
    },
    onClickDeleteFlushedConfig(_actId) {
      const self = this
      this.$prompt('是否确认删除所有（包括所有中台、中控、Stream等的配置）生成的活动配置？如果确认请再次输入你要删除的活动ID', '删除警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\d{4}[01][0-9][1-7]\d{3}/,
        inputErrorMessage: '活动ID格式错误',
        inputValidator: (value) => {
          if (_actId != value) {
            return '输入的活动ID不一致'
          }
        }
      }).then(({ value }) => {
        if (_actId != value) {
          self.$message({message: '活动ID不一致，跳过删除操作', type: 'warning'})
          return
        }

        deleteFlushedConfig(_actId).then(res => {
          if (res.result === 200) {
            self.$message({message: '删除成功', type: 'success'})
            self.onSearch()
            return
          }

          self.$message({message: res.reason, type: 'error'})
        }).catch((err) => {
          console.log(err)
        })
      }).catch(() => {
        self.$message({message: '删除已取消', type: 'info'})
      })
    },
    onDeleteTemplateActivity(_actId, _actName) {
      const self = this
      this.$confirm('删除快配活动【' + _actName + '（' + _actId + '）】？', '删除快配活动', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        deleteTemplateAct(_actId).then(res => {
          if (res.result === 200) {
            self.$message({message: '删除成功', type: 'success'})
            self.onSearch()
            return
          }

          self.$message({message: res.reason, type: 'error'})
        })
      }).catch(() => {
        self.$message({message: '删除已取消', type: 'info'})
      })
    }
  }
}
</script>

<style scoped lang="scss">

</style>
