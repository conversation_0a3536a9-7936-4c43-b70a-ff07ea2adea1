<template>
  <div class="app-container">
    <div>
      <el-container>
        <el-header>
          <el-row>
            <el-form :inline="true" :model="searchForm">
              <el-form-item label="活动id">
                <el-select v-model="searchForm.actId" placeholder="请选择活动"
                           filterable style="width:400px;">
                  <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                             :label='item.act_name+"("+item.act_id+")"'>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-button type="primary" :loading="loadingRankTaskConfig" @click="onSearch" icon="el-icon-search"
                         size="medium">查询
              </el-button>
              <el-button type="success" icon="el-icon-edit-outline" size="medium" @click="onEditRankTaskConfig(null)">
                新增榜单任务
              </el-button>
            </el-form>
          </el-row>
        </el-header>

        <el-main>
          <el-table :data="rankTaskConfigs" border stripe
                    :row-key="getRowKeys"
                    :expand-row-keys="expands"
                    @expand-change="expandChange">
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-row style="width: 1080px;">
                  <el-table :data="props.row.items" border stripe row-key="itemId" size="small"
                            :cell-style="rowStyle" :header-cell-style="rowStyle">
                    <el-table-column prop="itemId" label="奖项标识" width="80"></el-table-column>
                    <el-table-column prop="passValue" label="奖项标识" width="80"></el-table-column>
                    <el-table-column prop="remark" label="备注" width="80"></el-table-column>
                    <el-table-column prop="extjson" label="扩展(json格式)" width="300"></el-table-column>

                    <el-table-column label="操作" align="center">
                      <template slot-scope="scope">
                        <el-link @click="handleEditTaskItem(props.row.itemId,scope.row)" type="primary"
                                 size="mini">编辑
                        </el-link>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-row>
              </template>
            </el-table-column>


            <el-table-column prop="actId" label="活动id" width="110" align="center"></el-table-column>
            <el-table-column prop="taskName" label="任务名称" width="140" align="center"></el-table-column>
            <el-table-column prop="rankName" label="榜单名称" width="300" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.rankId + '-' + scope.row.rankName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="phaseName" label="榜单阶段" width="200" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.phaseId + '-' + scope.row.phaseName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="taskId" label="任务标识" width="80" align="center"></el-table-column>
            <el-table-column prop="taskLevel" label="任务级别" width="100" align="center"></el-table-column>
            <el-table-column prop="itemId" label="项目标识" width="110" align="center"></el-table-column>

            <el-table-column prop="passValue" label="过任务数值" width="100" align="center"></el-table-column>
            <el-table-column prop="poolName" label="奖池" width="100" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.poolId === 0 ? '' : scope.row.poolId + '-' + scope.row.poolName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="packageName" label="奖包" width="100" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.packageId === 0 ? '' : scope.row.packageId + '-' + scope.row.packageName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="packageNum" label="奖包数量" width="100" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.packageNum === 0 ? '' : scope.row.packageNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-link type="primary" @click="onAddItem(scope.row)">新增</el-link>
                <el-link type="primary" @click="onEditRankTaskConfig(scope.row)">编辑</el-link>
                <el-popconfirm title="确定删除该记录吗?" @confirm="onDeleteRankTaskConfig(scope.row)">
                  <template #reference>
                    <el-link type="danger"> 删除</el-link>
                  </template>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>

          <el-dialog title="添加任务item" :visible.sync="addTaskItemShow"
                     :close-on-click-modal="false" @closed="onRankTaskItemDialog">
            <el-form :model="rankTaskItem" label-width="80px">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="礼物Id(或别名)">
                    <el-input v-model="rankTaskItem.itemId"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="最低分值">
                    <el-input-number v-model="rankTaskItem.passValue"></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item label="备注">
                    <el-input v-model="rankTaskItem.remark"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="扩展(json)">
                    <el-input v-model="rankTaskItem.extJson"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="addTaskItemShow = false">取 消</el-button>
              <el-button type="primary" @click="onSaveRankTaskItem()">保 存</el-button>
            </div>

          </el-dialog>

          <el-dialog title="榜单任务" :visible.sync="showRankTaskConfigDialog"
                     :close-on-click-modal="false" @closed="onRankTaskConfigDialog">

            <el-form :model="rankTaskFormData" label-width="80px">
              <el-row>
                <el-col :span="8">
                  <el-form-item label="活动id">
                    <el-select v-model="rankTaskFormData.actId" placeholder="请选择活动"
                               @change="loadRankDropDownItems(rankTaskFormData.actId,true)"
                               filterable style="width:100%;" :disabled="isEditRankTask">
                      <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                                 :label='item.act_name+"("+item.act_id+")"'>
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="活动榜单">
                    <el-select v-model="rankTaskFormData.rankId" placeholder="请选择榜单" :disabled="isEditRankTask"
                               @change="loadRankPhaseDropDownItems(true)">
                      <el-option
                        v-for="item in rankDropDown"
                        :key="item.code"
                        :label='item.desc+"("+item.code+")"'
                        :value="item.code">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="榜单阶段">
                    <el-select v-model="rankTaskFormData.phaseId" placeholder="请选择榜单阶段" :disabled="isEditRankTask">
                      <el-option
                        v-for="item in rankPhaseDropDown"
                        :key="item.code"
                        :label='item.desc+"("+item.code+")"'
                        :value="item.code">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="任务id">
                    <el-input-number v-model="rankTaskFormData.taskId" :disabled="isEditRankTask"></el-input-number>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="任务名称">
                    <el-input v-model="rankTaskFormData.taskName"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="任务级别">
                    <el-input-number v-model="rankTaskFormData.taskLevel"></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="项目标识">
                    <el-input v-model="rankTaskFormData.itemId"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="任务数值">
                    <el-input-number v-model="rankTaskFormData.passValue"></el-input-number>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="奖池">
                    <el-select v-model="rankTaskFormData.poolId" placeholder="请选择奖池"
                               @change="loadPackageDropDown(true)">
                      <el-option
                        v-for="item in poolDropDown"
                        :key="item.code"
                        :label="item.desc"
                        :value="item.code">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="8">
                  <el-form-item label="奖包">
                    <el-select v-model="rankTaskFormData.packageId" placeholder="请选择奖包">
                      <el-option
                        v-for="item in packageDropDown"
                        :key="item.code"
                        :label="item.desc"
                        :value="item.code">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>

                <el-col :span="8">
                  <el-form-item label="奖包数量">
                    <el-input-number v-model="rankTaskFormData.packageNum"></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-form-item label="备注">
                  <el-input type="textarea" v-model="rankTaskFormData.remark"></el-input>
                </el-form-item>
              </el-row>

              <el-row>
                <el-form-item label="扩展属性">
                  <el-input type="textarea" v-model="rankTaskFormData.itemExtJson"></el-input>
                </el-form-item>
              </el-row>
            </el-form>
            <div slot="footer" class="dialog-footer">
              <el-button @click="showRankTaskConfigDialog = false">取 消</el-button>
              <el-button type="primary" @click="onSaveRankTaskConfig">保 存</el-button>
            </div>
          </el-dialog>
        </el-main>

        <el-footer>

        </el-footer>
      </el-container>
    </div>


  </div>
</template>

<script>
import Api from '@/api/activity/rankTaskConfig'
import ActConfigVersion from '@/api/activity/actConfigVersion'

export default {
  name: "RankTaskConfig",
  components: {},
  data: function () {
    return {
      searchForm: {
        actId: ''
      },
      loadingRankTaskConfig: false,
      rankTaskConfigs: [],
      showRankTaskConfigDialog: false,
      rankTaskFormData: {
        actId: '',
        rankId: '',
        phaseId: '',
        taskId: '',
        taskName: '',
        taskLevel: '',
        itemId: '',
        passValue: '',
        poolId: 0,
        packageId: 0,
        packageNum: 0,
        taskValueType: 2,
        remark: '',
        itemExtJson: ''
      },
      rankDropDown: [],
      rankPhaseDropDown: [],
      poolDropDown: [],
      packageDropDown: [],
      isEditRankTask: false,
        selectActList: [],
        addTaskItemShow: false,
        addTaskItem: false,

        rankTaskItem: {
          itemId: '',
          passValue: '',
          remark: '',
          extJson: ''
        },
        expands: [],
        getRowKeys(row) {
          console.log('rankId-taskId' + row.rankId + "-" + row.taskId)
          return row.rankId + "-" + row.taskId;
          //return row.itemId
        },
        rowStyle: {
          "background-color": "#f0f9eb"
    }
      }
  },
  created: function () {
    this.loadActInfos()
  },
  methods: {
    onSearch() {
      const self = this
      if (self.searchForm.actId === '' || isNaN(self.searchForm.actId)) {
        this.$Message.warning("查询的活动id为空或输入有误")
        return
      }
      self.loadingRankTaskConfig = true
      Api.listRankTaskConfig(self.searchForm.actId).then(
        res => {
          if (res.result === 200) {
            self.rankTaskConfigs = res.data

            self.loadRankDropDownItems(self.searchForm.actId)
          } else {
            this.$Message.warning(res.reason, 5)
          }
          self.loadingRankTaskConfig = false
        }
      ).catch((error) => {
        this.$Message.error(error.status + "," + error.statusText, 5)
        self.loadingRankTaskConfig = false
      })
    },
      onAddItem(item) {
        console.log(JSON.stringify(item))
        //this.addTaskItem = true;
        //this.rankTaskConfigs = item;
        this.rankTaskFormData = item
        //添加
        this.rankTaskItem = {
          actId: item.actId,
          rankId: item.rankId,
          phaseId: item.phaseId,
          taskId: item.taskId
        };
        this.addTaskItemShow = true;

      },
    onEditRankTaskConfig(item) {
      this.showRankTaskConfigDialog = true
      if (item) {
        this.isEditRankTask = true
        this.rankTaskFormData = JSON.parse(JSON.stringify(item))
        this.rankTaskFormData.rankId = this.rankTaskFormData.rankId + ""
        this.rankTaskFormData.phaseId = this.rankTaskFormData.phaseId + ""

        this.loadRankPhaseDropDownItems(false)
        if (this.rankTaskFormData.poolId === 0) {
          this.rankTaskFormData.poolId = ''
        } else {
          this.rankTaskFormData.poolId = this.rankTaskFormData.poolId + ""
        }
        if (this.rankTaskFormData.packageId === 0) {
          this.rankTaskFormData.packageId = ''
        } else {
          this.rankTaskFormData.packageId = this.rankTaskFormData.packageId + ""
        }
        this.loadPoolDropDown(this.rankTaskFormData.actId)
        this.loadPackageDropDown(false)
      } else {
        this.rankTaskFormData.actId = this.searchForm.actId
      }
      console.log(this.rankTaskFormData)
    },
    onRankTaskConfigDialog() {
      this.resetRankTaskFormData()
    },

      handleEditTaskItem(itemId, item) {
        console.log(JSON.stringify(item))
        this.rankTaskItem = JSON.parse(JSON.stringify(item))
        this.addTaskItemShow = true
      },


      onRankTaskItemDialog() {
        this.resetRankTaskItem()
      },

      resetRankTaskItem() {
        this.rankTaskItem.itemId = ''
        this.rankTaskItem.passValue = '';
        this.rankTaskItem.remark = '';
        this.rankTaskItem.extJson = '';
      },

    resetRankTaskFormData() {
      this.rankTaskFormData.actId = ''
      this.rankTaskFormData.rankId = ''
      this.rankTaskFormData.phaseId = ''
      this.rankTaskFormData.taskId = ''
      this.rankTaskFormData.taskName = ''
      this.rankTaskFormData.taskLevel = ''
      this.rankTaskFormData.itemId = ''
      this.rankTaskFormData.passValue = ''
      this.rankTaskFormData.poolId = ''
      this.rankTaskFormData.packageId = ''
      this.rankTaskFormData.packageNum = ''
      this.rankTaskFormData.taskValueType = 2
      this.rankTaskFormData.remark = ''
      this.isEditRankTask = false
      this.rankTaskFormData.itemExtJson = ''
    },

      onSaveRankTaskItem() {
        const self = this
        const item = self.rankTaskItem
        console.log('item : ' + JSON.stringify(item))
        Api.saveRankTaskItem(item)
          .then(res => {
            if (res.result === 200) {
              self.addTaskItemShow = false
              self.resetRankTaskItem()
              self.onSearch()
            } else {
              this.$Message.warning(res.reason, 5)
            }
          })
      },

    onSaveRankTaskConfig() {
      const self = this
      const formData = self.rankTaskFormData
      if (formData.actId === '' || isNaN(formData.actId)) {
        this.$Message.warning("活动id字段有误")
        return
      }
      if (formData.rankId === '') {
        this.$Message.warning("请选择榜单")
        return
      }
      if (formData.phaseId === '') {
        this.$Message.warning("请选择榜单阶段")
        return
      }
      if (formData.taskId === '' || formData.taskId <= 0) {
        this.$Message.warning("请填写任务id")
        return
      }
      if (formData.taskName === '') {
        this.$Message.warning("请填写任务名称")
        return
      }
      if (formData.taskLevel === '' || formData.taskLevel <= 0) {
        this.$Message.warning("请填写任务等级")
        return
      }
      if (formData.itemId === '') {
        this.$Message.warning("请填写任务标识")
        return
      }
      if (formData.passValue === '' || formData.passValue < 0) {
        this.$Message.warning("请填写正确的任务数值")
        return
      }

      if ((formData.poolId !== '' && formData.poolId !== 0 && formData.poolId !== '0') && formData.packageId === '') {
        this.$Message.warning("请选择奖包")
        return
      }

      if ((formData.packageId !== '' && formData.packageId !== 0 && formData.packageId !== '0') && formData.packageNum <= 0) {
        this.$Message.warning("请填写正确的奖包数量")
        return
      }

      Api.saveRankTaskConfig(formData)
        .then(res => {
          if (res.result === 200) {
            self.showRankTaskConfigDialog = false
            self.resetRankTaskFormData()
            self.onSearch()
          } else {
            this.$Message.warning(res.reason, 5)
          }
        })
    },
    loadRankDropDownItems(actId, empty) {
      const self = this
      if (empty) {
        self.rankTaskFormData.rankId = ''
        self.rankTaskFormData.itemId = ''
      }
      Api.listRankDropDown(actId).then(
        res => {
          if (res.result === 200) {
            self.rankDropDown = res.data
          } else {
            console.log(res)
          }
        }
      )
      self.loadPoolDropDown(actId)
    },
    loadRankPhaseDropDownItems(setEmpty) {
      const self = this
      if (setEmpty) {
        self.rankTaskFormData.phaseId = ''
      }
      Api.listRankPhaseDropDown(self.rankTaskFormData.actId, self.rankTaskFormData.rankId)
        .then(res => {
          if (res.result === 200) {
            self.rankPhaseDropDown = res.data
          } else {
            console.log(res)
          }
        })

      if (setEmpty) {
        Api.getRankItem(self.rankTaskFormData.actId, self.rankTaskFormData.rankId).then(res => {
          if (res.result === 200) {
            self.rankTaskFormData.itemId = res.data
          } else {
            console.log(res)
          }
        })
      }
    },
    loadPoolDropDown(actId) {
      const self = this
      Api.listAwardTaskDropDown(actId, "200,201,202,203").then(
        res => {
          if (res.result === 200) {
            self.poolDropDown = res.data
          } else {
            console.log(res)
          }
        }
      )
    },
    loadPackageDropDown(setEmpty) {
      const self = this
      if (setEmpty) {
        self.rankTaskFormData.packageId = ''
      }

      Api.listAwardPackageDropDown(this.rankTaskFormData.actId, this.rankTaskFormData.poolId)
        .then(
          res => {
            if (res.result === 200) {
              self.packageDropDown = res.data
            } else {
              console.log(res)
            }
          }
        )
    },
    //加载活动数据
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    onDeleteRankTaskConfig(item) {
      // actId,rankId,phaseId,taskId,itemId
      const param = {
        actId: item.actId,
        rankId: item.rankId,
        phaseId: item.phaseId,
        taskId: item.taskId,
        itemId: item.itemId
      }
      var self = this
      console.log(JSON.stringify(param))
      Api.removeRankTaskConfig(param).then(res => {
        if (res.result === 200) {
          self.onSearch()
        } else {
          this.$Message.warning(res.reason, 5)
        }
      })
      },

      expandChange(row, expandedRows) {
        debugger
        console.log('expand click')
        this.expands = [];
        if (expandedRows.length > 0) {
          this.expands.push(row.rankId + "-" + row.taskId);
    }
      },
  }
};
</script>
