<template>
  <div class="app-container">
    <div>
      <el-container>
        <el-header class="header">
          <el-row>
            <el-form :inline="true">
              <el-form-item label="组件id">
                <el-select v-model="componentId" placeholder="请选择组件" filterable style="width:300px">
                  <el-option v-for="item in selectComponents" :value="item.code" :key="item.code"
                             :label='item.desc+"("+item.code+")"'>
                  </el-option>
                </el-select>
              </el-form-item>
              <el-button type="primary" @click="loadAttr" icon="el-icon-search" size="medium">查询
              </el-button>
            </el-form>
          </el-row>
        </el-header>

        <el-main>
          <el-form label-width="150px">
            <el-form-item v-for="(item,index) in defineAttrs" :key="index" :label="item.labelText" :prop="item.propName">
              <!-- 输入框样式 -->
              <el-input v-model="item.data" v-if="item.propType === 'Text'"
                        :placeholder="item.placeholder" style="width: 50%;"></el-input>

              <!-- 数字样式 -->
              <el-input-number v-model="item.data" v-if="item.propType === 'Number'" :controls=false
                               :placeholder="item.placeholder" style="width: 50%;">
              </el-input-number>

              <!-- 下拉选项 -->
              <el-select v-model="item.data" v-if="item.propType === 'DropDown'" style="width: 50%;"
                         :placeholder="item.placeholder" filterable @change="$forceUpdate()">
                <el-option
                  v-for="option in item.options"
                  :key="option.code"
                  :label="option.desc"
                  :value="option.code">
                </el-option>
              </el-select>

              <!-- 表格 -->
              <el-table v-if="item.propType === 'Table'" size="mini" :data="item.data" border highlight-current-row
                        style="width: 80%;">
                <el-table-column
                  v-for="(prop,index) in item.props"
                  :label="prop.labelText"
                  :prop="prop.propName"
                  :key="index">
                  <!-- 表头 -->
                  <template slot="header" slot-scope="scope">
                    <span>{{prop.labelText}}</span>
                    <el-tooltip effect="light" placement="right-start"
                                style="margin-left: 10px;" v-if="prop.remark !== '' && prop.remark !== null">
                      <div slot="content">{{prop.remark}}</div>
                      <i class="el-icon-question"></i>
                    </el-tooltip>
                  </template>

                  <template slot-scope="scope">
                <span v-if="scope.row.isSet">
                  <!-- TODO 根据类型,构造不同的控件 -->
                  <el-input size="mini" placeholder="请输入内容" v-model="item.selectRow[prop.propName]"></el-input>
                </span>
                    <span v-else>{{scope.row[prop.propName]}}</span>
                  </template>
                </el-table-column>

                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button size="small" class="button" type="text" @click="saveRow(item, scope.row, scope.$index)"
                               style="color: #67c23a;">保存
                    </el-button>
                    <el-button size="small" class="button" type="text" @click="editRow(item, scope.row, scope.$index)">编辑
                    </el-button>
                    <el-button size="small" class="button" type="text" @click="deleteRow(item.data, scope.$index)"
                               style="color: #f56c6c;">删除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-col v-if="item.propType === 'Table'">
                <div style="margin-top: 5px;height: 30px;border: 1px dashed #c1c1cd;border-radius: 3px;cursor: pointer;justify-content: center;display: flex;
    line-height: 30px;width: 80%;" @click="onAddRow(item)"><span>+ 添加</span></div>
              </el-col>

              <!-- 开关 -->
              <el-switch v-model="item.data" v-if="item.propType === 'Switch'" active-value="true"/>

              <!-- 日期 -->
              <el-date-picker v-model="item.data" type="datetime" v-if="item.propType === 'DateTime'"
                              :placeholder="item.placeholder"
                              :format="item.format" :value-format="item.valueFormat">
              </el-date-picker>

              <el-time-picker v-model="item.data" v-if="item.propType === 'TimePicker'"
                              :placeholder="item.placeholder"
                              value-format="HH:mm:ss">

              </el-time-picker>

              <el-tooltip effect="light" :content="item.remark" placement="right-start"
                          style="margin-left: 10px;" v-if="item.remark !== '' && item.remark !== null">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-form-item>
          </el-form>
        </el-main>
      </el-container>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/pkgComponent'
  import CmptConfigApi from "@/api/activity/cmptConfig";
  import CmptAttrConfig from '@/api/activity/cmptAttrConfig'
  export default {
    name: "PkgComponent",
    components: {},
    data: function () {
      return {
        componentId: '',
        actId: '0',
        useIndex: '0',
        componentTitle: '',
        actName: '',
        defineAttrs: [],
        selectComponents: [],
      }
    },
    created: function () {
      this.loadComponentDefines()
    },
    methods: {
      loadComponentDefines() {
        CmptConfigApi.loadComponentDefines().then(
          res => {
            if (res.result === 200) {
              this.selectComponents = res.data
            } else {
              alert("拉取组件列表错误," + res.reason)
            }
          }
        )
      },
      loadAttr() {
        const self = this
        CmptAttrConfig.listComponentAttrDefine(self.actId, self.componentId, self.useIndex).then(
          res => {
            if (res.result === 200) {
              self.defineAttrs = res.data
            } else {
              alert("拉取属性列表错误," + res.reason)
            }
          }
        )
      },
      onSave() {
        const param = {
          actId: this.actId,
          cmptId: this.componentId,
          useIndex: this.useIndex
        }
        const values = []
        for (let attr of this.defineAttrs) {
          values.push({propName: attr.propName, data: attr.data})
        }
        param["values"] = values

        console.log(param)

        Api.saveComponentAttrValue(param).then(
          res => {
            if (res.result === 200) {
              this.$message.success(res.data);
            } else {
              this.$message.error(res.reason);
            }
          }
        )
      },
      onAddRow(item) {
        const props = item.props
        const data = item.data
        for (let i of data) {
          if (i.isSet) {
            return this.$message.error("请先保存当前编辑项");
          }
        }

        let newRow = {
          isSet: true
        };

        for (let prop of props) {
          newRow[prop.propName] = ""
        }
        data.push(newRow)
        item.selectRow = JSON.parse(JSON.stringify(newRow))
      },
      saveRow(item, row, index) {
        // 保存表格行
        let data = JSON.parse(JSON.stringify(item.selectRow));
        for (let k in data) {
          row[k] = data[k];
        }
        row.isSet = false;
      },
      editRow(item, row, index) {
        // 编辑表格行
        for (let i of item.data) {
          if (i.isSet) {
            return this.$message.error("请先保存当前编辑项");
          }
        }
        item.selectRow = row;
        row.isSet = true;
      },
      deleteRow(rows, index) {
        rows.splice(index, 1);
      }
    }
  };
</script>
