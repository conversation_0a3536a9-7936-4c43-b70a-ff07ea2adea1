<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="活动id">
            <el-select v-model="currentActId" placeholder="请选择活动"
                       filterable style="width:180px">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-button type="primary" :loading="loadingActTask" @click="onSearch()" icon="el-icon-search"
                     size="medium">
            查询
          </el-button>
          <el-button type="info" :loading="addingActTask" @click="onAddOfflineTask()" size="medium">
            添加
          </el-button>
          <el-tooltip class="item" effect="light" content="选择需要离线操作的活动进行操作" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>
      <el-row>
        <div v-loading="loadingActTask">
          <div v-if="currentActTaskState == -1">
            <p>请选择要离线化操作的活动，并点击“查询”按钮</p>
          </div>
          <div v-if="currentActTaskState == 1 || currentActTaskState == 2 || currentActTaskState == 3" style="height: 950px;">
            <el-steps v-if="stepDetail.steps" direction="vertical" :active="stepDetail.currentStep - 1" finish-status="success">
              <el-step v-for="step in stepDetail.steps" :key="step.step" :title="step.stepName" :description="step.stepDesc">
                <template v-if="step.step == stepDetail.currentStep && step.state != 2" slot="description">
                  <el-button v-if="step.state == 1" :loading="refreshingStep" size="small" type="success" plain icon="el-icon-refresh" @click="refreshExecuteResult(stepDetail.activity.actId, step.stepId, false)">刷新</el-button>
                  <el-tooltip v-if="step.state != 1 || step.idempotent" :content="step.stepDesc" placement="right" effect="light">
                    <el-button :loading="executingStep" size="small" type="success" plain icon="el-icon-caret-right" @click="onExecuteStep(step)">{{step | stepOpText}}</el-button>
                  </el-tooltip>
                </template>
              </el-step>
            </el-steps>
          </div>
          <div v-if="currentActTaskState == 0">
            <p>活动{{currentActId}}离线化操作尚未开始，您可以点击“添加”提前开始相关操作</p>
          </div>
          <div v-if="currentActTaskState == 4">
            <p>活动{{currentActId}}离线化操作已完成，您无需额外操作！</p>
          </div>
        </div>
      </el-row>
    </div>
  </div>
</template>

<script>
import API from '@/api/activity/actOfflineOperation'
import ActConfigVersion from '@/api/activity/actConfigVersion'

export default {
  name: "ActOfflineOperation",
  components: {},
  data: function () {
    return {
      selectActList: [],
      currentActId: '',
      loadingActTask: false,
      executingStep: false,
      refreshingStep: false,
      currentActTaskState: -1,
      stepDetail: {
        activity: {},
        state: -1,
        currentStep: -1,
        steps: []
      },
      loadingOfflineSteps: false,
      refreshTimer: null,
      addingActTask: false
    }
  },
  created: function () {
    this.loadActInfos()
    const _query = this.$route.query
    if (_query && _query.actId) {
      this.currentActId = _query.actId
      this.loadAndRenderActTask(this.currentActId)
    }
  },
  filters: {
    stepOpText: function (step) {
      if (step.state === 1) {
        return '再次执行'
      } else if (step.stepType === 1) {
        return '确认已操作'
      } else {
        return '执 行'
      }
    }
  },
  methods: {
    loadAndRenderActTask(actId) {
      const self = this
      this.executingStep = false
      this.refreshingStep = false
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      }

      this.loadingActTask = true
      API.queryActOfflineDetail(actId)
        .then(res => {
          if (res.result === 200) {
            self.currentActTaskState = res.data.state
            self.stepDetail = res.data
          } else {
            self.$message({message: '获取步骤失败:' + res.reason, type: 'warning'})
            self.currentActTaskState = -1
          }
          self.loadingActTask = false
        }).catch(error => {
        self.loadingActTask = false
        self.currentActTaskState = -1
        console.log(error)
      })
    },
    onSearch() {
      if (this.currentActId === '' || isNaN(this.currentActId)) {
        this.$Message.warning("请选择要查询的活动")
        return
      }
      this.loadAndRenderActTask(this.currentActId)
    },
    onAddOfflineTask() {
      if (this.currentActId === '' || isNaN(this.currentActId)) {
        this.$Message.warning("请选择要查询的活动")
        return
      }
      const self = this
      API.addActOfflineTask(this.currentActId)
        .then(res => {
          if (res.result === 200) {
            self.onSearch()
          } else {
            self.$message({message: '添加失败:' + res.reason, type: 'warning'})
          }
        }).catch(error => {
          console.log(error)
      })
    },
    onExecuteStep(step) {
      const _actId = this.stepDetail.activity.actId
      const self = this
      this.executingStep = true
      API.executeStep(_actId, step.stepId)
        .then(res => {
          if (res.result === 200) {
            if (res.data === 1) {
              step.state = 1
              self.refreshTimer = setInterval(() => {
                self.refreshExecuteResult(_actId, step.stepId, true)
              }, 3000)
            } else {
              self.$message({message: '执行成功', type: 'success'})
              self.loadAndRenderActTask(_actId)
            }
          } else {
            self.$message({message: '执行失败:' + res.reason, type: 'warning'})
          }
          self.executingStep = false
        }).catch(error => {
        console.log(error)
        self.executingStep = false
      })
    },
    //加载活动数据
    loadActInfos() {
      const self = this
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            self.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    refreshExecuteResult(actId, stepId, fromTimer) {
      const _actId = this.stepDetail.activity.actId
      const _stepId = this.stepDetail.currentStep
      if (_actId != actId || _stepId != stepId) {
        if (fromTimer && this.refreshTimer) {
          clearInterval(this.refreshTimer)
        }
        return
      }
      const self = this
      if (!fromTimer) {
        this.refreshingStep = true
      }
      API.refreshProcessingStep(actId, stepId)
        .then(res => {
          if (res.result === 200) {
            const stepState = res.data
            if (stepState != 1) {
              if (fromTimer && self.refreshTimer) {
                clearInterval(self.refreshTimer)
                self.refreshTimer = null
              }
              self.loadAndRenderActTask(actId)
            } else if (!fromTimer) {
              self.$message({message: '刷新成功', type: 'success'})
              self.refreshingStep = false
            }
          } else if (!fromTimer) {
            self.$message({message: '刷新失败' + res.reason, type: 'warning'})
            self.refreshingStep = false
          }
        }).catch(error => {
          console.log(error)
          if (!fromTimer) {
            self.refreshingStep = false
          }
      })
    }
  }
};
</script>
