<!-- 筹钱工具 -->
<template>
  <div>
    <Row style="padding: 20px;">
      <Button type="info" @click="handleAutoFill">自动填充</Button>
      <Button type="error" @click="handleReset">重置</Button>
      <Button type="info" @click="handleSave">临时保存</Button>
      <!-- readyDone -->
      <Button type="success" @click="handleReadyDone">发布到抽签工具</Button>
      <!-- redo -->
      <Button type="warning" @click="handleRedo">修改抽签界面数据</Button>
      <!-- submit -->
      <Button type="success" @click="handleSubmit">提交PK对阵关系</Button>

      <Select v-model="itemId" style="width:200px" placeholder="请选择抽签活动">
        <OptionGroup v-for="(groupList, actId) in actMap" :label="`[${actId}]${actMap[actId][0].actName}`" :key="actId">
          <Option v-for="item in getGroupList(actId)" :value="item.value" :key="item.value">{{ item.label }}</Option>
        </OptionGroup>
      </Select>
       <!-- <Tooltip max-width="400" content="抽签页面打开后可通过『Ctrl+Shift+L』切换环境" v-if="itemId" placement="bottom"> -->
      <Button v-if="itemId" :to="`https://hd-activity-test.yy.com/yo-toolbox/${isTest  ? 'test' : 'index'}.html#/draw?${itemId}`" target="_blank">跳转抽签页面</Button>
       <!-- </Tooltip> -->
    </Row>
    <!-- <Row  style="padding:0 20px;">


    </Row> -->
    <div style="position:relative;">
      <div class="content" style="position:relative;">
        <div class="content__bg" style="position:absolute;top:0;left:80px;">
          <Row :gutter="0" style="width:400px;padding:20px 0;">
            <Col v-for="(item, index) in indexList" :key="index" span="12" :offset="0" >
              <div style="height: 40px;line-height:40px;">
                <span style="margin-right: 40px" v-if="index % 2 === 1">{{index % 2 === 1 ? 'VS':''}}</span>
                {{item}}
              </div>
            </Col>
          </Row>
        </div>
        <!-- 输入 -->
        <Row :gutter="8" style="width:500px;padding:20px 0;">
          <Col v-for="(pos, index) in positions" :key="index" span="6" :offset="6" style="padding: 4px 0;">
            <Select v-model="positions[index]" filterable clearable>
              <Option v-for="item in members.map(item => ({label: `${item.shortId || item.id}`, value: item.id}))" :value="item.value" :key="item.value" :disabled="getUsed(item)">{{ item.label }}</Option>
            </Select>
          </Col>
        </Row>
      </div>


      <!-- 候选列表 -->
      <Row style="background:#eee;padding:20px" :gutter="4">
        <Col span="3" v-for="(item) in members" :key="item.id">
          <div :class="getUsed(item) && 'disabled'">
            <div style="border: 1px solid #000;margin-top:4px;padding:10px;">
              <p :title="item.id" slot="title">{{item.shortId || item.id}}</p>
              <!-- <p>{{item.name}}</p> -->
            </div>
          </div>
        </Col>
      </Row>
      <Spin size="large" fix v-if="loading"></Spin>

    </div>
  </div>
</template>

<script>
import pkGroupDrawApi from '@/api/activity/pkGroupDraw'
import {indexListConfig} from '@/api/activity/pkGroupDraw/const'
import qs from 'qs'
export default {
  props: {},
  components: {},
  data () {
    return {
      // configId: '',
      actMap: {},
      // configMap: new Map(),
      members: [],
      positions: [],
      loading: false,
      itemId: '',
      indexList: [],
      isTest: window.location.host.includes('test-') || window.location.host.includes('local')
    }
  },
  // computed: {
  //   defaultIndexList () {
  //     return indexListConfig[this.members.length]
  //   }
  // },

  created () {
    this.getList()
  },

  mounted () {},

  methods: {
    getReqParams (params) {
      // const item = this.configMap.get(this.configId)
      return {
        ...qs.parse(this.itemId),
        // actId: item.actId,
        // configId: item.configId,
        // rankingId: item.rankingId,
        // phaseId: item.phaseId,
        ...params
      }
    },

    getGroupList(actId) {
      return this.actMap[actId].map(item => ({
        label: `[${item.rankingId}]${item.rankingName}-[${item.phaseId}]${item.phaseName}-${item.configName}(${item.configId})`,
        value: `actId=${item.actId}&rankingId=${item.rankingId}&phaseId=${item.phaseId}&configId=${item.configId}`
      }))
    },
    getList() {
      pkGroupDrawApi.getList().then(res => {
        console.log('getList', res)
        // for (const key in res.data) {
        //   for (const item of res.data[key]) {
        //     this.configMap.set(item.configId, item)
        //   }
        // }
        this.actMap = res.data
      })
    },
    getMembers () {
      this.loading = true
      return pkGroupDrawApi.getMembers(this.getReqParams()).then(res => {
        this.loading = false
        console.log('getMembers', res)
        this.members = res.data.pkMembers
        if (res.data.positionNames && res.data.positionNames.length > 0) {
          this.indexList = res.data.positionNames
        } else {
          this.indexList = indexListConfig[this.members.length]
        }
        this.resetPositions()
      })
    },
    getUsed (item) {
      return this.positions.includes(item.id || item.value)
    },
    resetPositions() {
      this.positions = Array(this.members.length).fill("")
    },

    handleAutoFill () {
      const members = [...this.members]
      this.positions = members.sort(() => Math.random()-0.5).map(item => item.id)
    },
    handleReset () {
      this.resetPositions()
    },
    handleSave() {
      pkGroupDrawApi.savePositions(this.getReqParams({
        positions: this.positions
      }))
    },
    // 锁定顺序
    handleReadyDone () {
      if (this.positions.some(item => !item)) {
        this.$message.error('还有公会未分配位置')
        return
      }
      this.$modal.confirm('锁定抽签工具的对阵数据吗？').then(async () => {
       await pkGroupDrawApi.readyDone(this.getReqParams({
          positions: this.positions
        }))
      }) .catch(() => {});
    },
    // 获取暂存
    getPositions () {
      pkGroupDrawApi.getPositions(this.getReqParams()).then(res => {
        if (res.data.length > 0) {
          this.positions = res.data
        }
      })
    },
    handleRedo () {
      this.$modal.confirm('取消锁定抽签工具的对阵数据吗？').then(async () => {
        await pkGroupDrawApi.readyRedo(this.getReqParams())
      }) .catch(() => {});
    },
    handleSubmit () {
      this.$modal.confirm('确认提交最终的 PK 对阵数据吗？\n提交后活动页面将生效').then(async () => {
        await pkGroupDrawApi.drawSubmit(this.getReqParams())
      }) .catch(() => {});
    }
  },
  watch: {
    async itemId (newVal, oldVal) {
      if (newVal) {
        // 初始化
        await this.getMembers(this.getReqParams())
        this.getPositions(this.getReqParams())
      }
      console.log(oldVal, newVal)
    },
  }
}

</script>
<style lang='scss' scoped>
.disabled {
  opacity: 0.5;
}
</style>
