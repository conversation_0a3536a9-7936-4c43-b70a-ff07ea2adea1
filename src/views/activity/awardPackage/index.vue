<template>
  <div class="app-container">
    <div>
      <el-container>
        <el-header class="header">
          <el-button @click="close()">返回</el-button>

          <el-button type="success" icon="el-icon-circle-plus-outline"
                     @click="onAddPackage">新增奖包
          </el-button>
          <el-tooltip class="item" effect="light" content="操作表:award_model和award_package" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-header>

<!--        奖包列表-->
        <el-main>
          <el-table :data="packages" border stripe
                    :row-key="getRowKeys"
                    :expand-row-keys="expands"
                    @expand-change="expandChange">
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-row style="width: 80%">
                  <el-table :data="props.row.items" border stripe row-key="itemId" size="small"
                            :cell-style="rowStyle" :header-cell-style="rowStyle">
                    <el-table-column prop="itemId" label="奖项标识" width="80"></el-table-column>
                    <el-table-column prop="businessId" label="业务标识" width="80"
                                     :formatter="businessFormatter"></el-table-column>
                    <el-table-column prop="issueType" label="发放方式" width="80">
                      <template slot-scope="scope">
                        <span>{{ scope.row.issueType === '1' ? '自动发放' : '人工发放' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="status" label="奖项状态" width="80">
                      <template slot-scope="scope">
                        <span>{{ scope.row.status === '1' ? '正常' : '禁用' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="giftType" label="礼物类型" width="120"
                                     :formatter="giftTypeFormatter"></el-table-column>
                    <el-table-column prop="giftCode" label="礼物编码" width="200"></el-table-column>
                    <el-table-column prop="giftName" label="礼物名称" width="300"></el-table-column>
                    <el-table-column prop="giftNum" label="礼物数量" width="80"></el-table-column>
                    <el-table-column prop="position" label="显示位置" width="80"></el-table-column>
                    <el-table-column label="操作" align="center">
                      <template slot-scope="scope">
                        <el-link @click="handleEditPackageItem(props.row.packageId,scope.row)" type="primary"
                                 size="mini">编辑
                        </el-link>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-row>
              </template>
            </el-table-column>

            <el-table-column prop="packageId" label="奖包id" width="100"></el-table-column>
            <el-table-column prop="packageName" label="奖包名称" width="200"></el-table-column>
            <el-table-column prop="unit" label="单位" width="100"></el-table-column>
            <el-table-column prop="packageStatus" label="奖包状态" width="100"
                             :formatter="packageStatusFormatter"></el-table-column>
            <!--
            <el-table-column prop="position" label="显示位置" width="100"></el-table-column>
            -->
            <el-table-column prop="probability" label="中奖概率" width="100"></el-table-column>
            <el-table-column prop="packageTotal" label="可抽总上限" width="150"
                             :formatter="limitFormatter"></el-table-column>
            <el-table-column prop="userHitLimit" label="用户抽中上限" width="150"
                             :formatter="limitFormatter"></el-table-column>
            <el-table-column prop="dailyHitLimit" label="每日抽中上限" width="150"
                             :formatter="limitFormatter"></el-table-column>
            <!--
            <el-table-column prop="dailyLimitGroup" label="日限额统计分组" width="150"></el-table-column>
            -->
            <el-table-column label="操作" align="center">
              <template slot-scope="scope">
                <el-link @click="handleEditClick(scope.row)" type="primary" size="mini">编辑</el-link>
                <!--
                <el-link @click="handleAddItemClick(scope.row)" type="primary" size="mini" style="margin-left: 10px;">配置奖项
                </el-link>
                -->
                <el-link @click="onAddPackageItem(scope.row.packageId)" type="primary" size="mini"
                         style="margin-left: 10px;">新增奖项
                </el-link>
                <el-link @click="handleDeletePackageItem(scope.row)" type="danger"
                         size="mini">删除
                </el-link>
              </template>
            </el-table-column>
          </el-table>

          <!-- 新增奖包弹窗 -->
          <el-dialog
            title="新增奖包" :close-on-click-modal="false"
            :visible.sync="addPackage">
            <el-form ref="packageData" :model="packageData" label-width="100px" size="medium ">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="奖包名称">
                    <el-input v-model="packageData.packageName" placeholder="请填写奖包名称"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="奖包类型">
                    <el-select v-model="packageData.packageType" placeholder="请选择奖包类型">
                      <el-option label="有实际内容需要发放(1)" value="1"></el-option>
                      <el-option label="谢谢参与(0)" value="0"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="奖包单位">
                    <el-input v-model="packageData.unit" placeholder="请填写单位名称"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="奖包图片地址">
                    <image-upload v-model="packageData.packageImage"  />
                  </el-form-item>
                </el-col>

              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="奖包状态">
                    <el-switch v-model="packageData.packageStatus" active-value="1"
                               inactive-value="0"></el-switch>
                  </el-form-item>
                </el-col>
              </el-row>

              <!--
              <el-row>
                <el-col :span="12">
                  <el-form-item label="tips提示">
                    <el-input v-model="packageData.tips" placeholder="鼠标移上去时候的tips"></el-input>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="跳转地址">
                    <el-input v-model="packageData.skipUrl" placeholder="点击跳转地址"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              -->


              <el-row>
                <el-col :span="12">
                  <el-form-item label="每日上限">
                    <el-input-number v-model="packageData.dailyHitLimit"
                                     label="每日抽中上限，小于0无限制"></el-input-number>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="中奖概率">
                    <el-input-number v-model="packageData.probability" label="中奖概率"></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row>
                <el-col :span="12">
                  <el-form-item label="可抽上限">
                    <el-input-number v-model="packageData.packageTotal"
                                     label="可抽总上限, 小于 0 则不限制"></el-input-number>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="用户上限">
                    <el-input-number v-model="packageData.userHitLimit"
                                     label="用户抽中上限, 小于 0 则不限制"></el-input-number>
                  </el-form-item>
                </el-col>
              </el-row>

              <!--
              <el-row>
                <el-col :span="12">
                  <el-form-item label="显示位置">
                    <el-input-number v-model="packageData.position" label="显示位置，值小排前"></el-input-number>
                  </el-form-item>
                </el-col>

                <el-col :span="12">
                  <el-form-item label="统计分组">
                    <el-input v-model="packageData.dailyLimitGroup"
                              placeholder="日限额统计分组，若空则在 package_id 上统计"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              -->
              <el-row>
                <el-form-item label="奖包备注">
                  <el-input type="textarea" v-model="packageData.remark" placeholder="说明信息"></el-input>
                </el-form-item>
              </el-row>

              <el-row>
                <el-form-item label="奖包展示扩展">
                  <el-input type="textarea" v-model="packageData.viewExtjson" placeholder="全部透传给前端，不要放私密信息"></el-input>
                </el-form-item>
              </el-row>

            </el-form>
            <span slot="footer" class="dialog-footer">
                    <el-button @click="onCancelPackage">取 消</el-button>
                    <el-button type="primary" @click="handleAddPackage">确 定</el-button>
                </span>
          </el-dialog>

          <!-- 配置奖项 -->
          <el-dialog title="配置奖项" :close-on-click-modal="false"
                     :visible.sync="addPackageItem" width="60%">

            <!--
             <el-row>         </el-row>
            <el-tooltip class="item" effect="light" content="操作表:award_package_item" placement="right-start"
                        style="margin-right: 5px;">
              <i class="el-icon-question"></i>
            </el-tooltip>
            -->
            <!--
            <el-button type="primary" icon="el-icon-circle-plus-outline" style="margin-bottom: 10px;"
                       @click="onAddPackageItem">新增奖项
            </el-button>
            -->

            <!--
            <el-button style="margin-left: 10px;"
                       @click="cancelAddPackageItem">取消
            </el-button>
            -->


            <!--
            <el-row>
              <el-table :data="packageData.items" border row-key="itemId">
                <el-table-column prop="itemId" label="奖项标识" width="80"></el-table-column>
                <el-table-column prop="businessId" label="业务标识" width="80" :formatter="businessFormatter">

                </el-table-column>
                <el-table-column prop="issueType" label="发放方式" width="80">
                  <template slot-scope="scope">
                    <span>{{scope.row.issueType === '1' ? '自动发放' : '人工发放'}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="奖项状态" width="80">
                  <template slot-scope="scope">
                    <span>{{scope.row.status === '1' ? '正常' : '禁用'}}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="giftType" label="礼物类型" width="120" :formatter="giftTypeFormatter">

                </el-table-column>
                <el-table-column prop="giftCode" label="礼物编码" width="80"></el-table-column>
                <el-table-column prop="giftName" label="礼物名称" width="300"></el-table-column>
                <el-table-column prop="giftNum" label="礼物数量" width="80"></el-table-column>
                <el-table-column prop="position" label="显示位置" width="80"></el-table-column>
                <el-table-column label="操作" align="center">
                  <template slot-scope="scope">
                    <el-link @click="handleEditPackageItem(scope.row)" type="primary" size="mini">编辑</el-link>
                  </template>
                </el-table-column>
              </el-table>
            </el-row>
            -->
            <el-row style="margin-top: 10px;" v-if="addNewPackageItem">
              <!--
              <el-divider></el-divider>
              -->
              <el-form ref="addItemsData" :model="addItemsData" label-width="100px" size="medium ">
                <el-row>
                  <el-col :span="8">
                    <el-form-item label="奖项id">
                      <el-input-number :disabled="true" v-model="addItemsData.itemId"
                                       placeholder="请填写奖项id"></el-input-number>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="业务">
                      <el-select v-model="addItemsData.businessId" placeholder="请选择业务">
                        <el-option
                          v-for="item in businessDropDown"
                          :key="item.code"
                          :label='item.desc+"("+item.code+")"'
                          :value="item.code">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="发放方式">
                      <el-select v-model="addItemsData.issueType" placeholder="请选择发放方式">
                        <el-option label="自动发放(1)" value="1"></el-option>
                        <el-option label="人工发放(2)" value="2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="8">
                    <el-form-item label="礼物编码">
                      <el-select v-if="addItemsData.issueType === '1' &&	addItemsData.giftType==='132'" v-model="addItemsData.giftCode" placeholder="请选择发奖代理">
                        <el-option value="0" :label="'新建发奖道具'" title="点击新建一个发奖道具" @click.native="clickRewardAgent()">
                          <i style="color: #67C23A" class="el-icon-plus" />
                          <span style="color: #67C23A">新建发奖道具</span>
                        </el-option>
                        <el-option v-for="rewardConfig in rewardConfigs" :value="rewardConfig.rewardID.toString()" :key="rewardConfig.rewardID.toString()" :label="rewardConfig.rewardDesc" title="点击编辑道具" @click.native="clickRewardAgent(rewardConfig)" />
                      </el-select>
                      <el-input v-else v-model="addItemsData.giftCode" placeholder="请填写礼物编码"></el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="奖项状态">
                      <el-select v-model="addItemsData.status" placeholder="请选择奖项状态">
                        <el-option label="正常(1)" value="1"></el-option>
                        <el-option label="禁用(0)" value="0"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="礼物类型">
                      <el-select v-model="addItemsData.giftType" placeholder="请选择礼物类型" @change="giftTypeSelectChange">
                        <el-option
                          v-for="item in giftTypeDropDown"
                          :key="item.code"
                          :label='item.desc+"("+item.code+")"'
                          :value="item.code">
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row>
                  <el-col :span="8">
                    <el-form-item label="礼物名称">
                      <el-input v-model="addItemsData.giftName" placeholder="请填写礼物名称"></el-input>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="礼物数量">
                      <el-input-number v-model="addItemsData.giftNum" placeholder="请填写礼物数量"></el-input-number>
                    </el-form-item>
                  </el-col>

                  <el-col :span="8">
                    <el-form-item label="展示位置">
                      <el-input-number v-model="addItemsData.position" placeholder="请填写展示位置"></el-input-number>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row v-show="addItemsData.issueType === '1' &&	addItemsData.giftType==='132'">
                  <el-divider>交友统一发奖配置</el-divider>
                  <el-col :span="8">
                    <el-form-item label="礼物id">
                      <el-input v-model="addItemsData.issueConfig.propsId" placeholder="请填写礼物id"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="交友密码">
                      <el-input v-model="addItemsData.issueConfig.password" placeholder="请填写交友密码"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="宝贝礼物id">
                      <el-input v-model="addItemsData.issueConfig.bBPropsId" placeholder="请填写宝贝礼物id"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row v-show="addItemsData.issueType === '1' &&	addItemsData.giftType!=='132' && addItemsData.giftType!==''">
                  <el-divider>其他发奖配置</el-divider>
                  <el-form-item label="发奖配置">
                    <el-input type="textarea" v-model="addItemsData.issueConfigJson"
                              placeholder="请填写发奖配置，json格式"></el-input>
                  </el-form-item>
                </el-row>

                <el-row v-show="addItemsData.issueType === '1'">
                  <el-col :span="8">
                    <el-form-item label="有效天数">
                      <el-input-number v-model="addItemsData.issueConfig.days"
                                       placeholder="请填写有效天数"></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="sid">
                      <el-input v-model="addItemsData.issueConfig.sid" placeholder="请填写sid"></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="ssid">
                      <el-input v-model="addItemsData.issueConfig.ssid" placeholder="请填写ssid"></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row v-show="addItemsData.issueType === '1'">
                    <el-col :span="12">
                      <el-form-item label="有效期">
                        <el-date-picker
                          range-separator="至"
                          v-model="addItemsData.issueConfig.validStartTime"
                          type="datetime" value-format="timestamp"
                          placeholder="开始时间">
                        </el-date-picker>

                        <el-date-picker
                          range-separator="至"
                          v-model="addItemsData.issueConfig.validEndTime"
                          type="datetime" value-format="timestamp"
                          placeholder="结束时间">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="扩展字段">
                        <el-input type="textarea" v-model="addItemsData.issueConfig.expand"
                                  placeholder="请填写扩展字段，json格式"></el-input>

                        <el-tooltip effect="light" :content="info" placement="right-start"
                                    style="margin-left: 10px;">
                          <i class="el-icon-question"></i>
                        </el-tooltip>
                      </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                    <el-col :span="8">
                      <el-form-item label="备注">
                        <el-input v-model="addItemsData.remark" placeholder="备注(remark)"></el-input>
                      </el-form-item>
                    </el-col>
                </el-row>

                <el-row>
                  <el-button type="primary" style="margin-bottom: 10px;"
                             @click="savePackageItem">保存奖项
                  </el-button>
                  <el-button style="margin-left: 10px;"
                             @click="cancelAddPackageItem">取消
                  </el-button>
                </el-row>
              </el-form>
            </el-row>
            <el-dialog width="35%" title="发奖代理道具" :visible.sync="rewardAgentVisible" append-to-body :before-close="discardAgentChange">
              <el-form :model="editRewardConfig" label-width="150px">
                <el-form-item label="活动福利道具ID：">
                  <el-input v-model="editRewardConfig.rewardID" :disabled="true" />
                </el-form-item>
                <el-form-item label="道具类型：">
                  <dict-selector v-model="editRewardConfig.rewardType" dictType="agent_reward_type" :filterable="true" />
                </el-form-item>
                <el-form-item label="营收AppID：">
                  <dict-selector v-model="editRewardConfig.revenueAppID" dictType="agent_revenue_appid" :filterable="true" />
                </el-form-item>
                <el-form-item label="营收活动ID：">
                  <el-input v-model="editRewardConfig.revenueActID" />
                  <el-tooltip effect="light" placement="right-start" style="margin-left: 5px;" content="营收中台的发放ID,依赖营收中台发放的奖励类型需填写。否则填1。">
                    <i class="el-icon-question" />
                  </el-tooltip>
                </el-form-item>
                <el-form-item label="道具描述：">
                  <el-input v-model="editRewardConfig.rewardDesc" />
                </el-form-item>
                <el-form-item label="密钥：">
                  <el-input v-model="editRewardConfig.password" />
                  <el-tooltip effect="light" placement="right-start" style="margin-left: 5px;" content="对应奖项配置中的交友密码">
                    <i class="el-icon-question" />
                  </el-tooltip>
                </el-form-item>
              </el-form>
              <div slot="footer" class="dialog-footer">
                <el-button @click="discardAgentChange">不做更改</el-button>
                <el-button type="primary" @click="saveRewardAgent">保 存</el-button>
              </div>
            </el-dialog>
          </el-dialog>
        </el-main>

        <el-footer>

        </el-footer>
      </el-container>
    </div>
  </div>
</template>

<script>
import Api from '@/api/activity/awardPackage'
import ImageUpload from '@/views/activity/cmptAttrConfig/cmpt/ImageUpload'
import { queryRewardAgentConfigs, addRewardAgentConfig, updateRewardAgentConfig } from '@/api/activity/awardAgent'
import DictSelector from "@/components/DictSelector"

export default {
  name: "AwardPackage",
  components: { ImageUpload, DictSelector },
  data: function () {
    return {
      taskId: '',
      actId: '',
      paramId: -1,
      addPackage: false,
      addPackageItem: false,
      addNewPackageItem: false,
      packages: [],
      info: '宝贝业务,坐骑 {"AwardType":1001} 勋章 {"AwardType":2002};模板道具 {"AwardType":1000};进场秀 {"AwardType":2003};其他活动道具 {"AwardType":2005}',
      packageData: {
        packageName: '',
        packageType: '',
        packageStatus: '1',
        packageImage: '',
        tips: '',
        skipUrl: '',
        remark: '',
        position: 0,
        probability: 0,
        packageTotal: -1,
        userHitLimit: -1,
        dailyHitLimit: -1,
        dailyLimitGroup: '',
        packageId: 0,
        add: true,
        items: [{}]
      },
      addItemsData: {
        itemId: '',
        businessId: '',
        issueType: '1',
        status: '1',
        giftType: '',
        giftCode: '',
        giftName: '',
        giftNum: 1,
        position: 0,
        issueConfig: {},
        issueConfigJson: '',
        remark: ''
      },
      businessDropDown: [],
      giftTypeDropDown: [],
      rewardTypeDropDown: [],
      rewardConfigs: [],
      editRewardConfig: {
        rewardID: 0,
        password: 'hMJaNrKZEM',
        revenueActID: 1,
        revenueAppID: 0,
        rewardType: 0,
        rewardDesc: ''
        // startTime: '',
        // endTime: '',
        // budget: -1,
        // dailyLimit: -1,
        // dailyLimitPerUid: -1
      },
      agentLabelWidth: 40,
      rewardAgentVisible: false,
      expands: [],
      getRowKeys(row) {
        return row.packageId;
      },
      rowStyle: {
        "background-color": "#f0f9eb"
      }
    }
  },
  mounted: function () {
    console.log('activated')
    const query = this.$route.query
    this.taskId = query.taskId
    this.actId = query.actId
    this.loadPackageList()
    this.loadDropDownItems()
    this.loadAllRewardConfigs()
  },
  methods: {
    loadPackageList() {
      if (this.taskId === '') {
        return
      }
      const self = this
      Api.listAwardPackage(self.actId, self.taskId).then(
        res => {
          if (res.result === 200) {
            self.packages = res.data
            self.updatePackageData()
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      )/*.catch((error) => {
          console.log(error)
          this.$Message.error(error.status + "," + error.statusText, 5)
        })*/
    },
    loadAllRewardConfigs() {
      const self = this
      queryRewardAgentConfigs(this.actId).then(res => {
        if (res && res.result === 200) {
          self.rewardConfigs = res.data
        }
      })
    },
    handleEditClick(row) {
      this.packageData = JSON.parse(JSON.stringify(row));
      this.packageData.add = false
      this.addPackage = true
    },
    handleAddItemClick(row) {
      this.addPackageItem = true
      this.addNewPackageItem = false
      this.packageData = JSON.parse(JSON.stringify(row));
    },
    handleAddPackage() {
      const newPackage = JSON.parse(JSON.stringify(this.packageData));
      newPackage.actId = this.actId
      newPackage.taskId = this.taskId
      if (newPackage.add) {
        newPackage.packageId = 0
      }

      if (newPackage.packageName === '') {
        this.$Message.warning("请填写奖包名称")
        return
      }
      newPackage.items = []
      const self = this
      Api.saveAwardPackage(newPackage).then(res => {
        if (res.result === 200) {
          self.addPackage = false
          self.resetPackageData()
          self.loadPackageList()
          this.$Message.success("保存成功")
        } else {
          this.$Message.warning(res.reason, 5)
        }
      })/*.catch((error) => {
          console.log(error)
          this.$Message.error(error.status + "," + error.statusText, 5)
        })*/
    },
    resetPackageData() {
      this.packageData.packageName = ''
      this.packageData.packageType = ''
      this.packageData.packageStatus = '1'
      this.packageData.packageImage = ''
      this.packageData.tips = ''
      this.packageData.skipUrl = ''
      this.packageData.remark = ''
      this.packageData.position = 0
      this.packageData.probability = 0
      this.packageData.packageTotal = -1
      this.packageData.userHitLimit = -1
      this.packageData.dailyHitLimit = -1
      this.packageData.dailyLimitGroup = ''
      this.packageData.packageId = 0
      this.packageData.add = true
    },
    onAddPackage() {
      this.addPackage = true
      this.resetPackageData()
    },
    onCancelPackage() {
      this.addPackage = false
      this.resetPackageData()
    },
    packageStatusFormatter(row, column, cellValue, index) {
      if (cellValue === "1") {
        return "正常"
      }

      return "禁用"
    },
    limitFormatter(row, column, cellValue, index) {
      if (cellValue < 0) {
        return "不限额"
      }

      return cellValue
    },
    onAddPackageItem(packageId) {
      // 显示编辑界面
      this.addPackageItem = true
      this.addNewPackageItem = true

      let packageData;
      for (const element of this.packages) {
        if (element.packageId === packageId) {
          packageData = element
          break
        }
      }

      let lastItemId = 0;
      if (packageData !== undefined && packageData !== null) {
        for (const element of packageData.items) {
          if (element.itemId > lastItemId) {
            lastItemId = element.itemId
          }
        }
      }

      this.addItemsData.itemId = lastItemId + 1
      this.addItemsData.packageId = packageId
      this.resetAddItemsData()
    },


    resetAddItemsData() {
      this.addItemsData.businessId = '200'
      this.addItemsData.issueType = '1'
      this.addItemsData.status = '1'
      this.addItemsData.giftType = '132'
      this.addItemsData.giftCode = ''
      this.addItemsData.giftName = ''
      this.addItemsData.giftNum = 1
      this.addItemsData.position = 0
      this.addItemsData.issueConfig = {}
      this.addItemsData.issueConfigJson = ''
    },
    handleEditPackageItem(packageId, item) {
      this.addPackageItem = true
      console.log(item)
      if (item.issueConfig == null) {
        item.issueConfig = {}
      }
      this.addItemsData = JSON.parse(JSON.stringify(item))
      this.addItemsData.businessId = this.addItemsData.businessId + ''
      this.addItemsData.giftType = this.addItemsData.giftType + ''
      this.addItemsData.packageId = packageId
      this.addNewPackageItem = true
    },
    loadDropDownItems() {
      const self = this
      Api.listDropDown(0).then(
        res => {
          if (res.result === 200) {
            self.businessDropDown = res.data
          } else {
            console.log(res)
          }
        }
      )

      Api.listDropDown(1).then(
        res => {
          if (res.result === 200) {
            self.giftTypeDropDown = res.data
          } else {
            console.log(res)
          }
        }
      )

      Api.listDropDown(2).then(
        res => {
          if (res.result === 200) {
            self.rewardTypeDropDown = res.data
          } else {
            console.log(res)
          }
        }
      )
    },
    businessFormatter(row, column, cellValue, index) {
      for (let index = 0; index < this.businessDropDown.length; index++) {
        if (this.businessDropDown[index].code === cellValue + "") {
          return this.businessDropDown[index].desc
        }
      }

      return cellValue
    },
    giftTypeFormatter(row, column, cellValue, index) {
      for (let index = 0; index < this.giftTypeDropDown.length; index++) {
        if (this.giftTypeDropDown[index].code === cellValue + "") {
          return this.giftTypeDropDown[index].desc
        }
      }

      return cellValue
    },
    giftTypeSelectChange() {
      this.addItemsData.issueConfigJson = ''
      this.addItemsData.issueConfig = {}
    },
    savePackageItem() {
      // 校验参数
      const self = this
      const formData = self.addItemsData
      console.log(formData)
      if (formData.itemId <= 0) {
        this.$Message.warning("填写的奖项id有误")
        return
      }
      if (formData.businessId === '') {
        this.$Message.warning("请选择业务")
        return
      }
      if (formData.giftCode === '') {
        this.$Message.warning("请填写礼物编码")
        return
      }
      if (formData.giftType === '') {
        this.$Message.warning("请选择礼物类型")
        return;
      }
      if (formData.giftName === '') {
        this.$Message.warning("请填写礼物名称")
        return;
      }
      if (formData.issueConfig.expand !== undefined
        && formData.issueConfig.expand !== ''
        && !this.isJson(formData.issueConfig.expand)) {
        this.$Message.warning("扩展填写有误")
        return;
      }
      console.log(formData.issueConfigJson)
      if (formData.issueConfigJson !== '' && !this.isJson(formData.issueConfigJson)) {
        this.$Message.warning("发奖配置填写有误")
        return;
      }
      formData.actId = self.actId
      formData.taskId = self.taskId

      Api.saveAwardPackageItem(formData).then(
        res => {
          if (res.result === 200) {
            self.resetAddItemsData()
            self.addNewPackageItem = false
            self.addPackageItem = false
            self.loadPackageList()
          } else {
            this.$Message.warning(res.reason, 5)
          }
        })/*.catch((error) => {
          console.log(error)
          this.$Message.error(error.status + "," + error.statusText, 5)
        })*/
    },
    cancelAddPackageItem() {
      this.resetAddItemsData()
      this.addPackageItem = false
    },
    isJson(input) {
      if (typeof input == 'string') {
        try {
          const obj = JSON.parse(input);
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
            return false;
          }
        } catch (e) {
          return false;
        }
      }
    },
    updatePackageData() {
      const packageId = this.packageData.packageId
      if (packageId === 0) {
        return
      }
      for (let index = 0; index < this.packages.length; index++) {
        if (packageId === this.packages[index].packageId) {
          this.packageData = this.packages[index]
          break
        }
      }
    },
    expandChange(row, expandedRows) {
      this.expands = [];
      if (expandedRows.length > 0) {
        this.expands.push(row.packageId);
      }
    },
    clickRewardAgent(rewardConfig) {
      if (!rewardConfig) {
        const maxRewardID = this.rewardConfigs.map(config => parseInt(config.rewardID)).reduce((acc, current) => Math.max(acc, current), 0);
        let newRewardID = maxRewardID + 1
        if (maxRewardID === 0 || maxRewardID < parseInt(this.actId)) {
          newRewardID = parseInt(this.actId) * 1000 + 1
        }

        this.editRewardConfig.rewardID = newRewardID
        this.editRewardConfig.revenueActID = 1
        this.editRewardConfig.revenueAppID = '0'
        this.editRewardConfig.rewardType = '0'
        this.editRewardConfig.rewardDesc = ''
      } else {
        this.editRewardConfig.rewardID = rewardConfig.rewardID
        this.editRewardConfig.password = rewardConfig.password
        this.editRewardConfig.revenueActID = rewardConfig.revenueActID
        this.editRewardConfig.revenueAppID = String(rewardConfig.revenueAppID)
        this.editRewardConfig.rewardType = String(rewardConfig.rewardType)
        this.editRewardConfig.rewardDesc = rewardConfig.rewardDesc
      }

      this.rewardAgentVisible = true
    },
    saveRewardAgent() {
      const password = this.editRewardConfig.password
      if (!password) {
        this.$message({message: '秘钥不能为空', type: 'warning'})
        return
      }

      if (!this.editRewardConfig.rewardDesc) {
        this.$message({message: '道具描述不能为空', type: 'warning'})
        return
      }

      const self = this
      const rewardId = this.editRewardConfig.rewardID
      const editConfig = this.rewardConfigs.find(config => config.rewardID === rewardId)
      const deferred = editConfig ? updateRewardAgentConfig(this.actId, this.editRewardConfig) : addRewardAgentConfig(this.actId, this.taskId, this.editRewardConfig)
      deferred.then(res => {
          if (res && res.result === 200) {
            self.$message({message: '道具配置保存成功', type: 'success'})
            self.addItemsData.giftCode = rewardId.toString()
            self.addItemsData.issueConfig.password = password
            self.loadAllRewardConfigs()
            self.rewardAgentVisible = false
          } else {
            self.$message({message: res?.reason, type: 'error'})
          }
      })
    },
    discardAgentChange() {
      const password = this.editRewardConfig?.password
      if (password) {
        this.addItemsData.issueConfig.password = password
      }
      this.rewardAgentVisible = false
    },
    bBPropsIdChange() {
      const formData = this.addItemsData
      if (formData.giftName !== '') {
        if (formData.giftName.indexOf('勋章') > -1) {
          formData.issueConfig.expand = '{"AwardType":2002}'
        } else if (formData.giftName.indexOf('进场秀') > -1) {
          formData.issueConfig.expand = '{"AwardType":2003}'
        } else if (formData.giftName.indexOf('礼物') > -1) {
          formData.issueConfig.expand = '{"AwardType":1000}'
        }
      }
    },
    /** 关闭按钮 */
    close() {
      const obj = {path: "awardConfig", query: {'actId': this.actId}};
      this.$tab.closeOpenPage(obj);
    },
    handleDeletePackageItem(item) {
      console.log(item.packageId)
      console.log(this.taskId)
      console.log(this.actId)
      const self = this
      this.$confirm('确定删除该礼包？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        Api.removePackage(this.actId, this.taskId, item.packageId).then(
          res => {
            if (res.result === 200) {
              self.loadPackageList()
            } else {
              this.$Message.warning(res.reason, 5)
            }
          }
        )
      }).catch(() => {
      });
    }
  }
};
</script>
