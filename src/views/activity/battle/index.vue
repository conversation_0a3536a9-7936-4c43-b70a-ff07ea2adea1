<template>
  <div class="app-container">
    <div>
      <h3>战报配置</h3>
      <p><strong>注意:</strong></p>
      <br><br>
      <Modal v-model="modal">
        <p slot="header" style="text-align:center">
          <span>输入活动标识ActID</span>
        </p>
        <div style="text-align:center">
          <Input v-model="actIdInput" placeholder="请输入act_id"/>
        </div>
        <div slot="footer">
          <Button type="primary" long @click="init">获取标题数据</Button>
        </div>
      </Modal>
      <Collapse v-model="value" accordion>
        <Panel name="1">
          交友战报
          <div slot="content">
            <Select  style="width: 800px" >
              <Option v-for="item in params" :key="item" v-if="item.type===1" @click.native="handleSelect(item)" >{{ item.actId }}-{{item.groupName}}-{{item.title}}-{{item.remark}}</Option>
            </Select>
            <div>
              <br>
              <Form inline>
                <FormItem>
                  <Input v-model="JY.hdztRankId" style="width: auto">
                    <span slot="prepend">RankId</span>
                  </Input>
                </FormItem>
                <FormItem>
                  <Input v-model="JY.hdztPhaseId" style="width: auto">
                    <span slot="prepend">PhaseId</span>
                  </Input>
                </FormItem>
                <FormItem><Button type="primary" @click="initTable()">获取列表</Button></FormItem>
              </Form>
            </div>
          </div>
        </Panel>
        <Panel name="2">
          约战配置
          <div slot="content">
            <Select style="width: 800px" >
              <Option v-for="item in params" :key="item" v-if="item.type===2" @click.native="handleSelect(item)" >{{ item.actId }}-{{item.groupName}}-{{item.title}}-{{item.remark}}</Option>
            </Select>
            <div>
              <br>
              <Form inline>
                <FormItem>
                  <Input v-model="YZ.hdztRankId" style="width: auto">
                    <span slot="prepend">RankId</span>
                  </Input>
                </FormItem>
                <FormItem>
                  <Input v-model="YZ.hdztPhaseId" style="width: auto">
                    <span slot="prepend">PhaseId</span>
                  </Input>
                </FormItem>
                <FormItem><Button type="primary" @click="initTable()">获取列表</Button></FormItem>
              </Form>
            </div>
          </div>
        </Panel>
        <Panel name="3">
          宝贝配置
          <div slot="content">
            <Select  style="width: 800px" >
              <Option v-for="item in params" :key="item" v-if="item.type===3" @click.native="handleSelect(item)" >{{ item.actId }}-{{item.groupName}}-{{item.title}}-{{item.remark}}</Option>
            </Select>
            <div>
              <br>
              <Form inline>
                <FormItem>
                  <Input v-model="BB.hdztRankId" style="width: auto">
                    <span slot="prepend">RankId</span>
                  </Input>
                </FormItem>
                <FormItem>
                  <Input v-model="BB.hdztPhaseId" style="width: auto">
                    <span slot="prepend">PhaseId</span>
                  </Input>
                </FormItem>
                <FormItem><Button type="primary" @click="initTable()">获取列表</Button></FormItem>
              </Form>
            </div>
          </div>

        </Panel>
        <Panel name="4">
          全品类配置
          <div slot="content">
            <Select  style="width: 800px" >
              <Option v-for="item in params" :key="item" v-if="item.type===4" @click.native="handleSelect(item)" >{{ item.actId }}-{{item.groupName}}-{{item.title}}-{{item.remark}}</Option>
            </Select>
            <div>
              <br>
              <Form inline>
                <FormItem>
                  <Input v-model="QPL.hdztRankId" style="width: auto">
                    <span slot="prepend">RankId</span>
                  </Input>
                </FormItem>
                <FormItem>
                  <Input v-model="QPL.hdztPhaseId" style="width: auto">
                    <span slot="prepend">PhaseId</span>
                  </Input>
                </FormItem>
                <FormItem><Button type="primary" @click="initTable()">获取列表</Button></FormItem>
              </Form>
            </div>
          </div>

        </Panel>
        <Panel name="5">
          陪玩配置
          <div slot="content">
            <Select  style="width: 800px" >
              <Option v-for="item in params" :key="item" v-if="item.type===5" @click.native="handleSelect(item)" >{{ item.actId }}-{{item.groupName}}-{{item.title}}-{{item.remark}}</Option>
            </Select>
            <div>
              <br>
              <Form inline>
                <FormItem>
                  <Input v-model="PW.hdztRankId" style="width: auto">
                    <span slot="prepend">RankId</span>
                  </Input>
                </FormItem>
                <FormItem>
                  <Input v-model="PW.hdztPhaseId" style="width: auto">
                    <span slot="prepend">PhaseId</span>
                  </Input>
                </FormItem>
                <FormItem><Button type="primary" @click="initTable()">获取列表</Button></FormItem>
              </Form>
            </div>
          </div>
        </Panel>
      </Collapse>

    </div>




  </div>
</template>

<script>
  import Api from '@/api/activity/battle'

  export default {
    name: "Battle",
    components: {},
    data: function () {
      return {
        //默认弹出对话框
        modal: true,
        actIdInput:'202012001',
        //默认不打开折叠板
        value: '',
        resultList:{},
        params:{},
        flag:false,
        JY:{},
        YZ:{},
        BB:{},
        QPL:{},
        PW:{}
      }
    },

    methods:{
      init() {

        this.select(this.actIdInput);
        if (!this.flag) return;
        let i=0;
        for (const resultPair of this.resultList) {
          const result = resultPair[0];
          const resultGroup = resultPair[1];
          let param =  JSON.parse(JSON.stringify(result));
          param.groupName = param.groupId+"-"+(resultGroup==null?"UnknownGroup":resultGroup.groupName);
          this.params[i++]=param;
        }
        //关闭对话框
        this.modal = false;
        this.$Message.info('Clicked ok');
      },
      select(actId) {

        let reqData = {"actId": this.actIdInput, "extra": null};
        Api.select(reqData).then(
          res => {
            if (res.length==0){
              alert("无标题列表数据");
              return;
            }
            this.resultList=res;
            this.flag = true;
          }
        )
      },
      handleSelect(item){
        if(item.type ===1){
          this.JY = item;
        }
        if(item.type === 2){
          this.YZ = item;
        }
        if(item.type === 3){
          this.BB = item;
        }
        if(item.type === 4){
          this.QPL = item;
        }
        if(item.type === 5){
          this.PW = item;
        }

      },
    }
  };
</script>
