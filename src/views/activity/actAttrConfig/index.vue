<template>
  <div class="app-container">
    <el-form :inline="true" :model="fromProps" label-width="90" class="demo-form-inline">
      <el-form-item label="活动ID">
        <el-select v-model="fromProps.actId" filterable placeholder="请选择活动"
                   allow-create default-first-option
                   style="width:300px">
          <el-option
            v-for="item in selectActList"
            :key="item.act_id"
            :label="item.act_name+'('+item.act_id+')'"
            :value="item.act_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="数据库">
        <el-select v-model="fromProps.dataBase" style="width:200px"
                   allow-create default-first-option>
          <el-option
            v-for="item in selectDataBaseList"
            :key="item.code"
            :label="item.desc"
            :value="item.code">
          </el-option>
        </el-select>
      </el-form-item>
      <el-button type="primary" :loading="loadingTableData" @click="onSearch" icon="el-icon-search"
                 size="medium">
        查询
      </el-button>
      <el-button type="primary" icon="el-icon-edit-outline" size="medium" @click="onEditAward(null)">添加属性
      </el-button>
    </el-form>

    <el-main>
      <el-table :data="tableData" border>
        <el-table-column label="属性名称" prop="attrname" align="center" />
        <el-table-column label="属性值" prop="attrvalue" align="center" />
        <el-table-column label="说明" prop="remark" align="center" />
        <el-table-column label="操作" align="center">
          <template v-slot="scope">
            <el-link type="success" v-if="scope.row.supportEdit" @click="onEditAward(scope.row)" style="margin-left: 10px;">编辑</el-link>
            <el-link type="warning" v-if="scope.row.supportEdit" @click="onDeleteActAttr(scope.row.actId,scope.row.attrname)">删除</el-link>
          </template>
        </el-table-column>
      </el-table>

      <el-dialog title="活动属性" :visible.sync="awardFormVisible"
                 :close-on-click-modal="false" @closed="onCloseActAttrForm">
        <el-form :model="attrFormData" label-width="150px">

          <el-form-item label="活动id">
            <el-select v-model="attrFormData.actId" filterable placeholder="请选择活动"
                        style="width:100%;" :disabled="!attrFormData.add">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="数据库">
            <el-select v-model="attrFormData.dataBase" placeholder="请选择数据库"
                       filterable style="width:100%;" :disabled="!attrFormData.add">
              <el-option v-for="item in selectDataBaseList" :value="item.code" :key="item.code"
                         :label='item.desc'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="属性名称">
            <el-input v-model="attrFormData.attrname" placeholder="请填写属性名称"
                      filterable style="width:100%;" :disabled="!attrFormData.add"></el-input>
          </el-form-item>
          <el-form-item label="属性值">
            <el-input v-model="attrFormData.attrvalue" placeholder="请填写属性值"></el-input>
          </el-form-item>

          <el-form-item label="备注">
            <el-input type="textarea" v-model="attrFormData.remark"></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button @click="awardFormVisible = false">取 消</el-button>
          <el-button type="primary" @click="onSaveAward">保 存</el-button>
        </div>
      </el-dialog>
    </el-main>

  </div>


</template>

<script>
 import Api from '@/api/activity/actAttrConfig'
 import ActConfigVersion from '@/api/activity/actConfigVersion'
export default {
  name: 'ActAttrConfig',
  components: {},
  data: function () {
    return {
      fromProps: {
        actId: '',
        dataBase: "",
      },
      selectActList: [],
      selectDataBaseList: [
        {
          code: 'hdzt',
          desc: '中台'
        },
        {
          code: 'hdzk',
          desc: '中控'
        }
      ],
      tableData: [],
      loadingTableData: false,
      awardFormVisible: false,
      attrFormData: {
        actId: '',
        dataBase:'',
        attrname: '',
        attrvalue: '',
        remark: '',
        add: true
      },

    }

  },
  created: function () {
    this.loadActInfos()
  },
  methods:{
    // 查询活动属性
    onSearch: function () {
      const self = this
      if (self.fromProps.actId === '') {
        self.tableData = []
        return;
      }
      if (!this.checkActId(self.fromProps.actId)) {
        return
      }
      self.loadingTableData = true
      Api.listActAttr(self.fromProps.actId,self.fromProps.dataBase).then(
        res => {
          if (res.result === 200) {
            self.tableData = res.data
          } else {
            this.$Message.warning(res.reason, 5)
          }
          self.loadingTableData = false
        }
      ).catch((error) => {
        this.$Message.error(error.status + "," + error.statusText, 5)
        self.loadingTableData = false
      })
    },
    // 新增or编辑奖池
    onEditAward: function (item) {
      this.awardFormVisible = true
      if (item) {
        this.attrFormData.actId = item.actId
        this.attrFormData.attrname = item.attrname
        this.attrFormData.attrvalue = item.attrvalue
        this.attrFormData.remark = item.remark
        this.attrFormData.dataBase = this.fromProps.dataBase
        this.attrFormData.add = false
      } else {
        this.resetAttrForm()
        this.attrFormData.actId = this.fromProps.actId
        this.attrFormData.dataBase = this.fromProps.dataBase
      }
    },
    // 关闭弹窗
    onCloseActAttrForm: function () {
      this.resetAttrForm()
    },
    // 保存信息
    onSaveAward: function () {
      // 参数校验
      const self = this
      const attrFormData = self.attrFormData
      if (!this.checkActId(attrFormData.actId)) {
        return
      }

      if (attrFormData.dataBase === '') {
        this.$Message.warning("数据库不能为空")
        return
      }

      if (attrFormData.attrname === '') {
        this.$Message.warning("请输入属性名称")
        return
      }
      if (attrFormData.attrvalue === '') {
        this.$Message.warning("请输入属性值")
        return
      }

      Api.saveActAttr(attrFormData).then(
        res => {
          if (res.result === 200) {
            self.resetAttrForm()
            self.awardFormVisible = false
            this.$Message.success("保存成功")
            if (self.fromProps.actId === '') {
              self.fromProps.actId = attrFormData.actId
            }
            self.onSearch()
          } else {
            this.$Message.warning(res.reason, 5)
          }
        }
      )/*.catch((error) => {
          console.log(error)
          this.$Message.error(error.status + "," + error.statusText, 5)
        })*/
    },
    checkActId(actId) {
      if (actId === '' || actId === null) {
        this.$Message.warning("请输入活动ID")
        return false
      }
      if (actId === '' || actId <= 0 || isNaN(actId)) {
        this.$Message.warning("输入的活动ID有误,请检查")
        return false
      }

      return true
    },
    // 重置数据
    resetAttrForm: function () {
      this.attrFormData = {
        actId: '',
        dataBase:'',
        attrname: '',
        attrvalue: '',
        remark: '',
        add: true
      }
    },
    //加载活动数据
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    onDeleteActAttr: function(actId, attrname) {
      const self = this
      const dataBase = self.fromProps.dataBase
        this.$confirm('删除活动属性【' + attrname + '（' + actId+':' +dataBase+ '）】？', '删除活动属性', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
          Api.deleteActAttr(actId,dataBase, attrname).then(res => {
          if (res.result === 200) {
            self.$message({message: '删除成功', type: 'success'})
            self.onSearch()
            return
          }

          self.$message({message: res.reason, type: 'error'})
        })
      }).catch(() => {
        self.$message({message: '删除已取消', type: 'info'})
      })
    }
  }

  }

</script>
