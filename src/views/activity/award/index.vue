<template>
  <div class="app-container">
    <div>
      <h3>奖包发放</h3>
      <br><br>
      <el-form :label-width="20" :model="formItem" ref="formInline"  inline>
        <el-form-item  label="活动ID">
          <el-select v-model="formItem.actId" placeholder="请选择活动" filterable style="width:100%;" @change="actIdChange">
            <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id" :label='item.act_name+"("+item.act_id+")"' />
          </el-select>
        </el-form-item>
        <el-form-item label="奖池ID">
          <el-select v-model="formItem.taskId" placeholder="请选择奖池" style="width:100%;" @change="awardPoolChange">
            <el-option v-for="item in awardPool" :value="item.code" :key="item.code" :label='item.desc+"("+item.code+")"' />
          </el-select>
        </el-form-item>
        <el-form-item  label="奖包ID">
          <el-select v-model="formItem.packageId" placeholder="请选择奖包" style="width: 100%">
            <el-option v-for="item in awardPackage" :value="item.code" :key="item.code" :label='item.desc+"("+item.code+")"' />
          </el-select>
        </el-form-item>
        <el-form-item  label="奖项ID">
          <el-input-number v-model="formItem.itemId" placeholder="itemId" style="width: 100px" :controls="false" />
        </el-form-item>
        <el-form-item label="UID">
          <el-input-number v-model="formItem.uid" placeholder="uid" style="width: 120px" :controls="false"/>
        </el-form-item>
        <el-form-item>
          <el-button :loading="loadingState" type="primary" @click="awardVerify()">发放</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div style="margin-top: 30px">
      <h3>整个奖包一次性发放</h3>
      <br><br>
      <el-form :label-width="20" :model="formItem" ref="formInline"  inline>
        <el-form-item  label="活动ID">
          <el-input v-model="formItem.actId" placeholder="actId" style="width: 150px" />
        </el-form-item>
        <el-form-item  label="奖包ID">
          <el-input v-model="formItem.taskId" placeholder="taskId" style="width: 100px"/>
        </el-form-item>

        <el-form-item label="UID">
          <el-input-number v-model="formItem.uid" placeholder="uid" style="width: 120px" :controls="false"/>
        </el-form-item>
        <el-form-item>
          <el-button :loading="loadingState" type="primary" @click="taskVerify()">发放</el-button>
        </el-form-item>
      </el-form>
    </div>

  </div>
</template>

<script>
  import Api from '@/api/activity/award'
  import RankTaskConfigApi from '@/api/activity/rankTaskConfig'
  import ActConfigVersion from '@/api/activity/actConfigVersion'

  export default {
    name: "Award",
    components: {},
    data: function () {
      return {
        selectActList: [],
        awardPool: [],
        awardPackage: [],
        formItem: {
          actId: '',
          taskId: '',
          packageId: '',
          itemId: 1,
          uid: ''
        },
        loadingState: false
      }
    },
    beforeMount() {
      this.loadActInfos()
    },
    methods: {
      //加载活动数据
      loadActInfos() {
        ActConfigVersion.loadActInfos().then(
          res => {
            if (res.result === 200) {
              this.selectActList = res.data
            } else {
              alert("拉取活动列表错误," + res.reason)
            }
          }
        )
      },
      actIdChange(_actId) {
        const self = this
        this.formItem.taskId = ''
        this.formItem.packageId = ''
        RankTaskConfigApi.listAwardTaskDropDown(_actId, "100,101,102,103,200,201,202,203")
          .then(res => {
            if (res.result === 200) {
              self.awardPool = res.data
            } else {
              console.log(res)
            }
          }).catch(error => {
          console.log(error)
        })
      },
      awardPoolChange(_taskId) {
        const self = this
        this.formItem.packageId = ''
        RankTaskConfigApi.listAwardPackageDropDown(self.formItem.actId, _taskId)
          .then(res => {
            if (res.result === 200) {
              self.awardPackage = res.data
            } else {
              console.log(res)
            }
          }).catch(error => {
          console.log(error)
        });
      },
      awardVerify() {
        const self = this;
        const formItem = self.formItem;

        let reqData = {
          "actId": formItem.actId,
          "packageId": formItem.packageId,
          "itemId": formItem.itemId,
          "uid": formItem.uid
        }
        self.loadingState = true;
        Api.awardVerify(reqData).then(
          res => {
            if (res.result == 200) {
              self.$Message.success('操作成功');
            } else {
              self.$Message.warning('操作失败');
            }
            self.loadingState = false;
          }
        ).catch(() => {
          self.loadingState = false;
        });
      },
      taskVerify() {
        const self = this;
        const formItem = self.formItem;
        let reqData = {
          "actId": formItem.actId,
          "taskId": formItem.taskId,
          "uid": formItem.uid
        }
        self.loadingState = true;
        Api.taskVerify(reqData).then(
          res => {
            if (res.result === 0) {
              self.$Message.success('异步发放成功,请稍等片刻再查询');
            } else {
              self.$Message.warning('操作失败');
            }
            self.loadingState = false;
          }
        ).catch(() => {
          self.loadingState = false;
        });
      }
    }
  };
</script>
