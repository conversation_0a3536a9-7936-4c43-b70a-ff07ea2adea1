<template>
  <div class="app-container">
    <h3>说明</h3>
    <p>线上验证时需要插入白名单才能正常累榜</p>
    <p>多个id用英文逗号或者换行分隔，会自动去除首尾空格</p>
    <el-divider></el-divider>
    <el-form :model="form" inline>
      <el-form-item label="活动">
        <activity-selector v-model="form.actId" style="width: 350px;" />
      </el-form-item>

      <el-form-item label="角色">
        <el-select v-model="form.roleType" placeholder="请选择角色" filterable style="width: 200px;">
          <el-option v-for="item in selectRoleType" :value="item.roleType" :key="item.roleType"
                     :label='item.roleName+"("+item.roleType+")"'>
          </el-option>
        </el-select>
      </el-form-item>
      <el-button type="success" @click="onLoadWhiteList">查询</el-button>
    </el-form>
    <el-input v-model="form.whiteList" type="textarea" rows="10"></el-input>
    <el-button type="success" style="margin-top: 10px;" @click="onSaveWhiteList">覆盖</el-button>

  </div>
</template>

<script>
  import Api from '@/api/activity/actGreyWhiteList'
  import ActConfigVersion from '@/api/activity/actConfigVersion'
  import ActivitySelector from "@/components/ActivitySelector"

  export default {
    name: "WhiteList",
    components: { ActivitySelector },
    data: function () {
      return {
        form: {
          actId: '',
          whiteList: '',
          roleType: ''
        },
        selectActList: [],
        selectRoleType: []
      }
    },
    created: function () {
      this.loadActInfos()
      const _query = this.$route.query
      if (_query && _query.actId) {
        this.form.actId = Number(_query.actId)
      }
      this.buildRoleType()
    },
    methods: {
      //加载活动数据
      loadActInfos() {
        ActConfigVersion.loadActInfos()
          .then(res => this.selectActList = res.data)
          .catch(() => alert("拉取活动列表错误," + res.reason));
      },
      buildRoleType() {
        this.selectRoleType = [
		{
          roleType: 0,
          roleName: '未定义'
        },
		{
          roleType: 100,
          roleName: '用户'
        }, {
          roleType: 200,
          roleName: '主播/主持/陪陪'
        }, {
          roleType: 201,
          roleName: '陪玩客服接待'
        }, {
          roleType: 400,
          roleName: '公会'
        }, {
          roleType: 401,
          roleName: '厅'
        }, {
          roleType: 402,
          roleName: '陪玩团'
        }, {
          roleType: 403,
          roleName: '新厅/交友贡献厅'
        },
          {roleType: 700, roleName: '家族'}
          ,
          {roleType: 404, roleName: '语音房房间'}
        ]
      },
      onLoadWhiteList() {
        if (!this.checkParam()) {
          return
        }
        const self = this
        self.form.whiteList = ''
        Api.loadWhiteList(self.form)
          .then(res => self.form.whiteList = res.data.whiteList)
        /*.catch((error) => {
          console.log(error)
          this.$message.error(error.status + "," + error.statusText, 5)
        })*/
      },
      onSaveWhiteList() {
        if (!this.checkParam()) {
          return
        }
        const self = this
        Api.saveWhiteList(self.form)
          .then(res => this.$message.success("覆盖数量:" + res.data.saveCount))
      },
      checkParam() {
        const form = this.form
        if (form.actId === '') {
          this.$message.warning("请选择活动")
          return false
        }
        if (form.roleType === '') {
          this.$message.warning("请选择角色")
          return false
        }

        return true
      }
    }
  };
</script>
