<template>
  <div class="app-container">
    <div>
      <h3>PK配置</h3>
      <p><strong>注意:</strong>1.下拉框选择后需要点击更新列表 2.在系统待机过久会导致请求数据出错</p>
      <br><br>

      <Select v-model="model" style="width: 400px" @on-change="changeParamToggle()">
        <Option v-for="(item,index) in ChosenParams"  :value="index" :key="index"  >[{{index}}]{{ item.name }}</Option>
      </Select>
      <br><br>
      <Collapse v-model="value" accordion>
        <Panel name="1">
          pk分组配置
          <div slot="content">
            <div v-if="isShow1">
              <div>
                <Input v-model="actIdInput">
                  <span slot="prepend">ActId</span>
                </Input>
              </div>
              <div>
                <Input v-model="rankIdInput">
                  <span slot="prepend">RankId</span>
                </Input>
              </div>
              <div>
                <Input v-model="phaseIdInput">
                  <span slot="prepend">PhaseId</span>
                </Input>
              </div>
              <div>
                <Input v-model="dayInput">
                  <span slot="prepend">Day</span>
                </Input>
              </div>
              <br>
              <Button type="primary" @click="unmarkAccomplished()">退出已结算</Button>
              <Button type="primary" @click="markAccomplished()">标记已结算</Button>
            </div>
            <br>
            <Button type="primary" @click="membersTableCreat()" style="width: 100%">刷新列表</Button>

            <br><br>
          </div>
        </Panel>
        <Panel name="2">
          阶段分组配置
          <div slot="content">
            <Button type="primary" @click="fetchPkgroup()" style="width: 100%">获取阶段分组</Button>
            <div v-if="isShow2">
              <div v-for="(item,index) in pkGroup">
                <p>
                  {{item.name}}<br>members:
                </p>
                <span v-for="(member,index) in item.ranks" >
                  <InputNumber :max="pkGroup.length*item.ranks.length" :min="1" v-model="item.ranks[index]" style="width: auto" ></InputNumber>
              </span>
                <div>
                  <p>count:</p>
                  <InputNumber :max="item.ranks.length" :min="0" v-model="pkGroup[index].count" style="width: auto"></InputNumber>
                </div>
              </div>
              <button type="primary" @click="updatePkgroup()"style="width: 100%">修改阶段分组</button>
            </div>
          </div>
        </Panel>
        <Panel name="3">
          选项三
          <p slot="content">
            #3 内容
          </p>
        </Panel>
      </Collapse>

    </div>




  </div>
</template>

<script>
  import Api from '@/api/activity/pkConfig'
  export default {
    name: "PkConfig",
    components: {},
    data: function () {
      return {
        ChosenParams:[
          {
            name:"交友天团PK赛12进6",
            groupCode:"JYTTS",
            actId:202012001,
            rankId:1,
            phaseId:3,
            day:"00000000",
            treeSubmit:false,
          },{
            name:"公会分区PK赛-交友16进8",
            groupCode:"GHFQS",
            actId:202012001,
            rankId:31,
            phaseId:34,
            day:"00000000",
            treeSubmit:true,
          },{
            name:"公会分区PK赛-约战8进4",
            groupCode:"GHFQS",
            actId:202012001,
            rankId:32,
            phaseId:34,
            day:"00000000",
            treeSubmit:false,
          },{
            name:"公会分区PK赛-宝贝16进8",
            groupCode:"GHFQS",
            actId:202012001,
            rankId:33,
            phaseId:34,
            day:"00000000",
            treeSubmit:true,
          },{
            name:"综合厅PK赛一轮",
            groupCode:"TINGS",
            actId:202012001,
            rankId:41,
            phaseId:43,
            day:"00000000",
            treeSubmit:true,
          },{
            name:"多人厅PK赛一轮",
            groupCode:"TINGS",
            actId:202012001,
            rankId:42,
            phaseId:43,
            day:"00000000",
            treeSubmit:true,
          },{
            name:"主播大比拼海选-交友天团-主播PK赛第一轮32进16",
            groupCode:"ZBFQBP",
            actId:202012001,
            rankId:57,
            phaseId:52,
            day:"00000000",
            treeSubmit:false,
          },{
            name:"主播大比拼海选-交友天团-主播PK赛第二轮16进8",
            groupCode:"ZBFQBP",
            actId:202012001,
            rankId:57,
            phaseId:53,
            day:"00000000",
            treeSubmit:false,
          },{
            name:"主播大比拼海选-交友天团-主播PK赛第三轮8进4",
            groupCode:"ZBFQBP",
            actId:202012001,
            rankId:57,
            phaseId:54,
            day:"00000000",
            treeSubmit:false,
          },{
            name:"主播大比拼海选-交友天团-主播大比拼第四轮4进2",
            groupCode:"ZBFQBP",
            actId:202012001,
            rankId:57,
            phaseId:55,
            day:"00000000",
            treeSubmit:false,
          },
        ],
        //默认选择ChosenParams[0]
        model:0,
        //默认打开第一个折叠板
        value: '1',
        //全局变量
        actId:'',
        rankId:'',
        phaseId:'',
        day:'',
        isShow1:false,
        isShow2:false,
        pkGroup:{},
        pkgroupArray_update:{},
      }
    },

    methods:{
      changeParamToggle(){
        this.actId = this.ChosenParams[this.model].actId;
        this.rankId = this.ChosenParams[this.model].rankId;
        this.phaseId = this.ChosenParams[this.model].phaseId;
        this.day = this.ChosenParams[this.model].day;

      },
      unmarkAccomplished(){
        let reqData = {"actId": this.actId,"rankId": this.rankId,"phaseId": this.phaseId}
        Api.unmarkAccomplished(reqData).then(
          res => {
            alert(res.result);
          }
        )
      },
      markAccomplished(){
        let reqData = {"actId": this.actId,"rankId": this.rankId,"phaseId": this.phaseId}
        Api.markAccomplished(reqData).then(
          res => {
            alert(res.result);
          }
        )
      },
      fetchPkgroup(){

        let reqData = {"srcActId": this.actId,"srcRankId": this.rankId,"srcPhaseId": this.phaseId}
        Api.fetchPkgroup(reqData).then(
          res => {
            this.isShow2 = true;
            this.pkGroup =res;
          }
        )
      },
      //获取前台修改的值
      updatePkgroup(){
        if(!confirm("是否提交阶段分组？")){
          return;
        }
        for (let i = 0; i < this.pkGroup.length; i++) {
          let pkgroup_update = {
            ranks:[],
            name:this.pkGroup[i].name,
            count:this.pkGroup[i].count,
          }
          let ranks = [];
          for (let j = 0; j < this.pkGroup[i].ranks.length; j++) {
            ranks[j]=this.pkGroup[i].ranks[j];
          }
          pkgroup_update.ranks=ranks;
          this.pkgroupArray_update[i]=pkgroup_update;
        }
        let reqData = {
          "srcActId": this.actId,
          "srcRankId": this.rankId,
          "srcPhaseId": this.phaseId,
          "pkgroup_json": this.pkgroupArray_update,
        };
        //reqData = JSON.stringify(reqData);
        Api.updatePkgroup(reqData).then(
          res => {
            alert(res.result);
          }
        )
      },
    }

  };
</script>
