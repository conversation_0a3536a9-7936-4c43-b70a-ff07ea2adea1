<template>
  <div class="app-container">
    <Form :label-width="90" :model="formItem">
      <FormItem label="活动ID" prop="actId" >
        <el-select v-model="formItem.actId" placeholder="请选择新活动" filterable required="true" style="width: 184px" >
          <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                     :label='item.act_name+"("+item.act_id+")"'>
          </el-option>
        </el-select>
      </FormItem>
      <FormItem label="活动时间" prop="actTime" >
        <DatePicker type="datetime" format="yyyy-MM-dd HH:mm:ss"  placeholder="Select actTime" v-model="formItem.actTime"  @on-change="formItem.actTime=$event" ></DatePicker>
      </FormItem>
      <FormItem >
        <Button :loading="loadingState0" type="primary" @click="getVirtTime()">刷新(查询)</Button>
        <Button :loading="loadingState1" type="primary" @click="setVirtTime()" style="margin-left: 8px">修改</Button>
      </FormItem>
    </Form>

  </div>
</template>

<script>
  import Api from '@/api/activity/actTime'
  import ActConfigVersion from '@/api/activity/actConfigVersion'
  import {parseTime} from "@/utils/systemUtils";

  export default {
    name: "ActTime",
    components: {},
    data: function () {
      return {
        formItem: {
          actId:'',
          actTime:new Date(),
        },
        selectActList: [],
        loadingState0: false,
        loadingState1: false
      }
    },
    created: function () {
      this.loadActInfos()
    },
    methods:{
      loadActInfos() {
        ActConfigVersion.loadActInfos().then(
          res => {
            this.selectActList = res.data
          }
        ).catch(() => {
          this.selectActList = []
        });
      },
      //刷新
      getVirtTime(){
        const self = this;
        const formItem = self.formItem;
        if (formItem.actId === "" || formItem.actId == null || isNaN(formItem.actId) || formItem.actId <= 0) {
          alert("请输入活动ID");
          return 0;
        }
        if (formItem.actId === 0) {
          return;
        }
        self.loadingState0 = true;
        Api.getVirtTime(formItem.actId).then(
          res => {
            formItem.actTime = res.data.time;
            this.$message({
              message: res.reason,
              type: 'success'
            });
            self.loadingState0 = false;
          }
        ).catch(() => {
          self.loadingState0 = false;
        });
      },
      //修改
      setVirtTime(){
        const self = this;
        const formItem = self.formItem;
        if (formItem.actTime === "" || formItem.actTime == null) {
          alert("请输入时间");
          return;
        }
        const updateTime = parseTime(formItem.actTime, '{y}-{m}-{d} {h}:{i}:{s}');
        self.loadingState1 = true;
        Api.setVirtTime(formItem.actId, updateTime).then(
          res => {
            this.$message({
              message: res.reason,
              type: 'success'
            });
            self.loadingState1 = false;a
          }
        ).catch(() => {
          self.loadingState1 = false;
        });
      }

    }
  };
</script>
