<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="活动id">
            <el-select v-model="searchForm.actId" placeholder="请选择活动"
                       filterable style="width:180px">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="类型">
            <el-select v-model="searchForm.backupType" placeholder="备份类型" style="width:150px" clearable>
              <el-option v-for="item in backupTypeList" :value="item.code" :key="item.code"
                         :label='item.desc+"("+item.code+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="数据源">
            <el-select v-model="searchForm.backupSource" placeholder="备份数据源"
                       filterable style="width:150px" clearable>
              <el-option v-for="item in backupSourceList" :value="item.code" :key="item.code"
                         :label='item.desc+"("+item.code+")"'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="分组">
            <el-input v-model="searchForm.groupCode" placeholder="分组" style="width: 150px" />
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="备份状态"
                       filterable style="width:150px" clearable>
              <el-option v-for="item in statusList" :value="item.code" :key="item.code"
                         :label='item.desc+"("+item.code+")"'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="备份配置ID">
            <el-input v-model="searchForm.configId" placeholder="备份配置ID" style="width: 150px" />
          </el-form-item>
          <el-button type="primary" :loading="loadingActBackupRecord" @click="onSearch(true)" icon="el-icon-search"
                     size="medium">
            查询
          </el-button>
          <el-button type="danger" @click="onBatchDel" icon="el-icon-delete" size="medium">批量删除</el-button>
          <el-tooltip class="item" effect="light" content="操作表:backup_activity_record" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>
      <el-row>
        <el-table :data="tableData" border v-loading="loadingActBackupRecord" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="50" align="center" />
          <el-table-column prop="id" label="ID" align="center" width="75"></el-table-column>
          <el-table-column prop="actId" label="活动ID" align="center" width="130"></el-table-column>
          <el-table-column label="数据源" align="center" width="110">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="top">
                <p>{{scope.row.remark}}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag size="medium">{{scope.row.backupSource}}</el-tag>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="groupCode" label="分组" align="center" width="50"></el-table-column>
          <el-table-column prop="configId" label="配置ID" align="center" width="75"></el-table-column>
          <el-table-column label="目标节点" align="center" width="270">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="top">
                <p>{{scope.row.sourceUri}}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag size="medium">{{scope.row.sourceNode}}</el-tag>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column prop="fenceTime" label="备份时间" align="center" width="170"></el-table-column>
          <el-table-column prop="backupPath" label="保存路径" align="center" width="160"></el-table-column>
          <el-table-column label="保存文件" align="center" width="270">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="top">
                <p>{{scope.row.fileSize | fileSizeFormat}}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag size="medium">{{scope.row.fileKey}}</el-tag>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="状态" align="center" width="100">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="top" v-if="scope.row.backupState === 'failed' || scope.row.backupState === 'error'">
                <p>{{scope.row.failMsg}}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag type="danger" size="medium">{{scope.row.backupState}}</el-tag>
                </div>
              </el-popover>
              <el-tag v-else :type="scope.row.backupState === 'completed' ? 'success' : 'warning'" size="medium">{{scope.row.backupState}}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" align="center">
            <template slot-scope="scope">
              <el-link type="success" @click="onClickSink(scope.row)" v-if="scope.row.backupState == 'completed'">恢复</el-link>
              <el-link type="primary" @click="onClickDelRecord(scope.row)" style="margin-left: 10px;" v-if="scope.row.backupState == 'completed'">删除</el-link>
              <el-link type="info" @click="showSinkRecord(scope.row)" style="margin-left: 10px;" v-if="scope.row.backupState == 'completed'">记录</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="[15,20,50,100]"
          @size-change="onSearch(false)"
          @current-change="onSearch(false)"
          layout="->, total, prev, pager, next, sizes"
          :current-page.sync="pageData.page"
          :page-size.sync="pageData.size"
          :total="pageData.total">
        </el-pagination>
      </el-row>
    </div>

    <el-dialog title="备份恢复" :visible.sync="sinkDialogVisible"
               :close-on-click-modal="false">
      <template slot="title">
        <h2 style="display: inline-block; margin-right: 10px;">备份恢复</h2>
        <i class="el-icon-warning" />
        <el-link type="danger" :underline="false" href="https://doc.yy.com/pages/viewpage.action?pageId=223681221">
          操作指引
        </el-link>
      </template>
      <el-form :model="sinkSourceProp" label-width="150px">
        <el-input v-model="sinkSourceProp.recordId" readonly type="hidden"></el-input>
        <el-row>
          <el-col :span="12">
            <el-form-item label="备份记录">
              <el-input v-model="sinkSourceProp.recordName" readonly></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="数据源类型">
              <el-select v-model="sinkSourceProp.scheme" placeholder="数据源类型">
<!--                <el-option value="default" label="默认">默认</el-option>-->
                <el-option value="redis" label="直连">直连</el-option>
                <el-option value="redis-sentinel" label="哨兵">哨兵</el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="sinkSourceProp.scheme == 'redis'">
          <el-col :span="12">
            <el-form-item label="IP">
              <el-input v-model="sinkSourceProp.host" placeholder="IP地址"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口">
              <el-input-number v-model="sinkSourceProp.port" placeholder="端口"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="sinkSourceProp.scheme == 'redis-sentinel'">
          <el-col :span="24">
            <el-form-item label="哨兵地址">
              <el-input v-model="sinkSourceProp.sentinel" placeholder="哨兵地址 eg. demo-wx-sentinel.yy.com:20112,demo-bj-sentinel.yy.com:20112"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="清理">
              <el-switch v-model="sinkSourceProp.clear"></el-switch>
              <el-tooltip class="item" effect="light" content="写入数据前，对相应前缀的key做删除" placement="right">
                <i class="el-icon-question"></i>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="sinkSourceProp.clear">
            <el-form-item label="Pattern">
              <el-input v-model="sinkSourceProp.clearPatterns" placeholder="清理Key Pattern，多个使用英文逗号分隔开"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="sinkSourceProp.scheme == 'redis-sentinel' || sinkSourceProp.scheme == 'redis'">
          <el-col :span="12" v-if="sinkSourceProp.scheme == 'redis-sentinel'">
            <el-form-item label="Master">
              <el-input v-model="sinkSourceProp.master" placeholder="Master"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Password">
              <el-input v-model="sinkSourceProp.password" placeholder="Password"></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="sinkSourceProp.scheme == 'default'">
          <el-col :span="24">
            <el-form-item label="RedisUri">
              <el-input v-model="sinkSourceProp.redisUri" readonly placeholder="地址URL"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <template v-if="sinking || sinked">
          <i v-if="sinking" class="el-icon-loading"></i>
          <el-tag type="danger">{{sinkResult}}</el-tag>
        </template>
        <el-button @click="sinkDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSink()" :disabled="sinking">恢 复</el-button>
      </div>
    </el-dialog>

    <el-dialog title="恢复记录" :visible.sync="sinkRecordVisible">
      <el-table :data="sinkRecordList" border v-loading="loadingActSinkRecord">
        <el-table-column property="id" label="ID" align="center" width="65"></el-table-column>
        <el-table-column property="sinkSourceUri" align="center" label="恢复数据源"></el-table-column>
        <el-table-column property="sinkState" label="状态" align="center" width="100">
          <template slot-scope="scope">
            <el-popover trigger="hover" placement="top" v-if="scope.row.sinkState === 'failed' || scope.row.sinkState === 'error'">
              <p>{{scope.row.failMsg}}</p>
              <div slot="reference" class="name-wrapper">
                <el-tag type="danger" size="medium">{{scope.row.sinkState}}</el-tag>
              </div>
            </el-popover>
            <el-tag v-else :type="scope.row.sinkState === 'completed' ? 'success' : 'warning'" size="medium">{{scope.row.sinkState}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column property="createTime" label="恢复时间" align="center" width="170"></el-table-column>
      </el-table>
      <el-pagination
        :page-sizes="[8]"
        @current-change="loadSinkRecords()"
        layout="->, total, prev, pager, next, sizes"
        :current-page.sync="sinkRecordData.page"
        :page-size.sync="sinkRecordData.size"
        :total="sinkRecordData.total">
      </el-pagination>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/activity/actRedisBackupRecord'
import ActConfigVersion from '@/api/activity/actConfigVersion'

export default {
  name: "ActRedisBackup",
  components: {},
  data: function () {
    return {
      loadingActBackupRecord: false,
      loadingActSinkRecord: false,
      sinking: false,
      sinked: false,
      sinkResult: '',
      sinkDialogVisible: false,
      sinkRecordVisible: false,
      selectActList: [],
      backupTypeList: [
        {code: 'event', desc: '活动事件'},
        {code: 'point', desc: '时间点'},
        {code: 'timer', desc: '定时备份'},
        {code: 'manual', desc: '手动备份'},
        {code: 'extra', desc: 'ranking备份'}
      ],
      backupSourceList: [
        {code: 'ZTRanking', desc: '中台榜单'},
        {code: 'ZTAward', desc: '中台抽发奖'},
        {code: 'Ecology', desc: '中控Ecology'},
        {code: 'Stream', desc: '中控Stream'}
      ],
      statusList: [
        {code: 'trying', desc: '备份中'},
        {code: 'completed', desc: '备份成功'},
        {code: 'failed', desc: '备份失败'},
        {code: 'deleted', desc: '已删除'}
      ],
      searchForm: {
        actId: '',
        backupType: '',
        backupSource: '',
        groupCode: '',
        configId: '',
        status: 'completed'
      },
      multipleSelection: [],
      sinkSourceProp: {
        recordId: '',
        recordName: '',
        scheme: 'redis-sentinel',
        redisUri: '',
        host: '',
        port: '',
        sentinel: '',
        master: '',
        password: '',
        clear: false,
        clearPatterns: ''
      },
      sinkResultTimer: null,
      pageData: {
        page: 1,
        size: 15,
        total: 0,
      },
      tableData: [],
      sinkRecordData: {
        recordId: 0,
        remark: '',
        page: 1,
        size: 8,
        total: 0
      },
      sinkRecordList: []
    }
  },
  created: function () {
    const _query = this.$route.query
    if (_query && _query.configId) {
      this.searchForm.configId = _query.configId
    }
    this.loadActInfos()
    // this.buildStatusList()
  },
  mounted() {
    const _query = this.$route.query
    if (_query && _query.configId) {
      this.searchForm.configId = _query.configId
      this.searchForm.actId = _query.actId
      this.onSearch(false)
    }
  },
  filters: {
    fileSizeFormat: function (fileSize) {
      const kb = 1024;
      const mb = kb * 1024;
      const gb = mb * 1024;
      if (fileSize < kb) {
        return fileSize + 'B'
      } else if (fileSize < mb) {
        let temp = fileSize / kb;
        temp = temp.toFixed(2);
        return temp + 'K'
      } else if (fileSize < gb) {
        let temp = fileSize / mb;
        temp = temp.toFixed(2);
        return temp + 'M'
      } else {
        let temp = fileSize / gb;
        temp = temp.toFixed(2);
        return temp + 'G'
      }
    },
  },
  watch: {
    'sinkSourceProp.clear': {
      handler(newVal, oldVal) {
        console.log(newVal)
      }
    }
  },
  methods: {
    onSearch(fromBtn) {
      const req = this.searchForm
      if (req.actId === '' || isNaN(req.actId)) {
        this.$Message.warning("请选择要查询的活动")
        return
      }
      const self = this
      if (fromBtn) {
        self.pageData.page = 1
      }
      req.pageNo = self.pageData.page
      req.pageSize = self.pageData.size
      self.loadingActBackupRecord = true
      API.queryActBackupRecords(req)
        .then(res => {
          if (res.code == 200) {
            self.pageData.total = res.total
            self.tableData = res.data
          } else {
            self.$message({message: '获取列表数据失败:' + res.reason, type: 'warning'})
          }
          self.loadingActBackupRecord = false
        })
        .catch(error => {
          console.log(error)
          self.loadingActBackupRecord = false
        })
    },
    onBatchDel() {
      const selectedIds = this.multipleSelection.filter(row => row.backupState == 'completed').map(row => row.id)
      if (!selectedIds || selectedIds.length <= 0) {
        this.$Message.warning('请选择需要删除且备份成功的内容')
        return
      }

      const self = this
      this.$modal.confirm('将删除' + selectedIds.join(',') +' ，请确认').then(() =>{
        API.batchDelBackupRecord(selectedIds)
          .then(res => {
            if (res.result == 200) {
              self.$message({message: '删除成功', type: 'success'})
              self.onSearch(false)
            } else {
              self.$message({message: '删除失败了:' + res.reason, type: 'warning'})
            }
          }).catch(error => {
          console.log(error)
        })
      }).catch(() => {
        self.$message({message: '删除已取消', type: 'warning'})
      })
    },
    onClickSink(record) {
      this.sinkSourceProp.recordId = record.id
      this.sinkSourceProp.recordName = record.id + '【' + record.remark + '】'
      this.sinkSourceProp.scheme = 'redis-sentinel'
      const self = this
      this.getDefaultSinkSource().then(defaultUri => self.sinkSourceProp.redisUri = defaultUri)
      this.clear = false
      this.sinkSourceProp.clearPatterns = this.getClearPattern(record)
      this.sinking = false
      this.sinked = false
      this.sinkResult = ''
      this.sinkDialogVisible = true
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    getClearPattern(record) {
      if (record.backupSource === 'ZTRanking' || record.backupSource === 'ZTAward') {
        return `act:${record.actId}*,ranking:${record.actId}*`
      }

      return `act:${record.actId}*`
    },
    onClickDelRecord(config) {
      const self = this
      this.$modal.confirm('将删除' + config.id + '【' + config.remark + '】' +'，请确认').then(() => {
        API.batchDelBackupRecord([config.id])
          .then(res => {
            if (res.result == 200) {
              self.$message({message: '删除成功', type: 'success'})
              self.onSearch(false)
            } else {
              self.$message({message: '删除失败了:' + res.reason, type: 'warning'})
            }
          }).catch(error => {
          console.log(error)
        })
      }).catch(() => {
        self.$message({message: '删除已取消', type: 'warning'})
      })
    },
    //加载活动数据
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    onSink() {
      let sourceUri = ''
      if (this.sinkSourceProp.scheme == 'redis') {
        if (!this.sinkSourceProp.host.trim()) {
          this.$message({message: 'Redis IP不能为空', type: 'warning'})
          return
        }

        sourceUri = this.sinkSourceProp.scheme + '://'
        if (this.sinkSourceProp.password.trim()) {
          sourceUri += (':' + this.sinkSourceProp.password.trim() + '@')
        }
        sourceUri += (this.sinkSourceProp.host.trim() + ':' + this.sinkSourceProp.port.trim());
      } else if (this.sinkSourceProp.scheme == 'redis-sentinel') {
        if (!this.sinkSourceProp.sentinel.trim() || !this.sinkSourceProp.master.trim()) {
          this.$message({message: 'Redis sentinel、master不能为空不能为空', type: 'warning'})
          return
        }

        sourceUri = this.sinkSourceProp.scheme + '://'
        if (this.sinkSourceProp.password.trim()) {
          sourceUri += (':' + this.sinkSourceProp.password.trim() + '@')
        }
        sourceUri += (this.sinkSourceProp.sentinel.trim() + '#' + this.sinkSourceProp.master.trim())
      } else {
        sourceUri = this.sinkSourceProp.redisUri.trim()
      }

      let clearPatterns = []
      if (this.sinkSourceProp.clear && this.sinkSourceProp.clearPatterns.trim()) {
        clearPatterns = this.sinkSourceProp.clearPatterns.trim().split(',')
      }
      const self = this
      this.$confirm('将备份恢复到【' + sourceUri + '】，<b style="color: red">目标数据源数据将被覆盖</b>，请确认？',
        '重要提示', {dangerouslyUseHTMLString: true, type: 'warning', center: true})
        .then(() => {
        API.sinkBackupRecord(this.sinkSourceProp.recordId, sourceUri, clearPatterns)
          .then(res => {
            if (res.result == 200) {
              self.sinking = true
              self.sinkResult = '恢复执行中...'
              self.sinkResultTimer = setInterval(() => {
                self.refreshSinkResult(res.data)
              }, 3000)
            } else {
              self.$message({message: '恢复请求失败:' + res.reason, type: 'warning'})
            }
          }).catch(error => {
          console.log(error)
        })
      }).catch(() => {
        self.$message({message: '备份已取消:', type: 'warning'})
      })
    },
    refreshSinkResult(sinkRecordId) {
      const self = this
      API.querySinkResult(sinkRecordId)
        .then(res => {
          if (res.result == 200) {
            const sinkState = res.data
            if (sinkState == 'trying') {
              self.sinkResult = '恢复执行中...'
            } else {
              if (sinkState == 'completed') {
                self.sinkResult = '恢复成功'
              } else {
                self.sinkResult = '恢复失败'
              }
              self.sinking = false
              self.sinked = true
              clearInterval(self.sinkResultTimer)
            }
          } else {
            console.log(res)
          }
        }).catch(error => {
          console.log(error)
      })
    },
    loadSinkRecords() {
      const self = this
      this.loadingActSinkRecord = true
      this.sinkRecordVisible = true
      API.queryActSinkRecords(this.sinkRecordData.recordId, this.sinkRecordData.page, this.sinkRecordData.size)
        .then(res => {
          if (res.code == 200) {
            self.sinkRecordData.total = res.total
            self.sinkRecordList = res.data
          } else {
            self.$message({message: '获取列表数据失败:' + res.msg, type: 'warning'})
          }

          self.loadingActSinkRecord = false
        }).catch(error => {
          console.log(error)
          self.loadingActSinkRecord = false
      })
    },
    showSinkRecord(record) {
      this.sinkRecordData.recordId = record.id
      this.sinkRecordData.remark = record.remark
      this.sinkRecordData.page = 1
      this.sinkRecordData.size = 8
      this.sinkRecordData.total = 0
      this.sinkRecordList = []

      this.loadSinkRecords()
    },
    async getDefaultSinkSource() {
      return await API.queryDefaultSinkSource()
    }
  }
};
</script>
