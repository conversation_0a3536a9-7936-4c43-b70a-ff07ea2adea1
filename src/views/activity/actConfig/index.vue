<template>
  <div class="app-container">
    <div style="margin: 60px">
      <h4>活动配置指南</h4> <br> <span style="clior: red"> 仅适用于宝贝月度活动</span>
      详细的配置文档: <a href="https://kdocs.cn/l/ceoRpdBxML8q" target="_blank"> 活动配置指导手册</a>

      <ul>
        <ol>
          <li>创建活动, 后台地址: <a target="_blank" href="https://webtest.yy.com/ge_brid/#/config">点击</a></li>
          <li>奖池配置, 后台地址: <a target="_blank" href="https://test-manager-hdzt.yy.com/admin-static/#/awardConfig">点击</a>
          </li>
          <li>任务配置, 后台地址: <a target="_blank"
                             href="https://test-manager-hdzt.yy.com/admin-static/#/rankTaskConfig">点击</a></li>
          <li>挂件配置, 后台地址: <a target="_blank" href="https://test-manager-hdzt.yy.com/admin-static/#/viewDefine">点击</a>
          </li>
          <li>数据上报配置, 后台地址: <a target="_blank" href="https://test-manager-hdzt.yy.com/admin-static/#/reRule">点击</a></li>
          <li>宝贝红包雨配置, 后台地址: <a target="_blank" href="https://test-manager-g.yy.com/yygg/redPackConfig.do">点击</a>
            <span style="clior: red">若提示未登陆,请点击</span> <a target="_blank" href="https://test-manager-g.yy.com/"> 此处</a>登陆后再点击
          </li>
        </ol>
      </ul>
    </div>

  </div>
</template>

<script>
  import Api from '@/api/activity/reRule'

  export default {
    name: "ActConfig",
    components: {},
    data: function () {
      return {
        reRuleList: [],
        reRuleParamList: [],
        reRuleScriptList: [],
        reRuleVersionList: [],
        reRuleColumns: [{
          title: '调用方id',
          width: 180,
          key: 'appId'
        }, {
          title: '规则名称',
          width: 180,
          key: 'ruleName'
        }, {
          title: '规则开关',
          width: 180,
          key: 'ruleStatus'
        }, {
          title: '创建时间',
          width: 180,
          key: 'createTime'
        }, {
          title: '更新时间',
          width: 180,
          key: 'updateTime'
        }, {
          title: '创建人',
          width: 180,
          key: 'createPassport'
        }, {
          title: '修改人',
          width: 180,
          key: 'updatePassport'
        }, {
          title: '配置类型 0-脚本 1-流程图',
          width: 180,
          key: 'configType'
        }, {
          title: '限流(秒)',
          width: 180,
          key: 'limitSecond'
        }],
        reRuleParamColumns: [{
          title: '规则id',
          width: 180,
          key: 'ruleId'
        }, {
          title: '参数名',
          width: 180,
          key: 'paramKey'
        }, {
          title: '参数类型',
          width: 180,
          key: 'paramType'
        }, {
          title: '参数描述',
          width: 180,
          key: 'paramDesc'
        }, {
          title: '参数来源 0-调用方传入 1-表达式 2-函数 3-事件',
          width: 180,
          key: 'paramSource'
        }, {
          title: '表达式',
          width: 180,
          key: 'expression'
        }, {
          title: '函数',
          width: 180,
          key: 'functionKey'
        }, {
          title: '函数参数',
          width: 180,
          key: 'functionParam'
        }, {
          title: '是否可视化参数',
          width: 180,
          key: 'visualStatus'
        }, {
          title: '事件id',
          width: 180,
          key: 'eventId'
        }, {
          title: '事件活动id',
          width: 180,
          key: 'eventActId'
        }],
        reRuleScriptColumns: [{
          title: '规则id',
          width: 180,
          key: 'ruleId'
        }, {
          title: '脚本类型',
          width: 180,
          key: 'ruleType'
        }, {
          title: '规则脚本',
          width: 180,
          key: 'ruleScript',
          render: (h, params) => {
            return h('span', {
              style: {
                display: 'inline-block',
                width: params.column._width * 0.9 + 'px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }
            }, params.row.ruleScript);
          }

        }, {
          title: '返回类型',
          width: 180,
          key: 'returnType'
        }, {
          title: '规则可视化配置',
          width: 180,
          key: 'visualConfig'
        }, {
          title: '视图配置',
          width: 180,
          key: 'viewConfig'
        }],
        reRuleVersionColumns: [{
          title: '规则id',
          width: 180,
          key: 'ruleId'
        }, {
          title: '生效状态',
          width: 180,
          key: 'status'
        }, {
          title: '规则配置',
          width: 180,
          key: 'ruleConfig',
          render: (h, params) => {
            return h('span', {
              style: {
                display: 'inline-block',
                width: params.column._width * 0.9 + 'px',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }
            }, params.row.ruleConfig);
          }
        }, {
          title: '创建时间',
          width: 180,
          key: 'createTime'
        }, {
          title: '创建人',
          width: 180,
          key: 'createPassport'
        }, {
          title: '启用时间',
          width: 180,
          key: 'enableTime'
        }, {
          title: '启用人',
          width: 180,
          key: 'enablePassport'
        }, {
          title: '版本说明',
          width: 180,
          key: 'versionDesc'
        }],

        formItem: {},
        editFormItem: {},

        loadingState: false,
        loadingStatus: false,
        opt_type: 1,
        addLayerState: false,
        ruleValidate: {
          actId: [{required: true, message: '不可为空', trigger: 'blur'}],
          attrName: [{required: true, message: '不可为空', trigger: 'blur'}],
          attrValue: [{required: true, message: '不可为空', trigger: 'blur'}],
          remark: [{required: true, message: '不可为空', trigger: 'blur'}],
        },
        copyFormItem: {},
        copyLayerState: false,
        loadingCopyState: false,
      }

    },

    methods: {
      queryReRule() {
        Api.queryReRule(this.formItem.ruleId).then(res => {

          if (res.result === '0') {

            this.reRuleList = res.data.reRuleList
            this.reRuleParamList = res.data.reRuleParamList
            this.reRuleScriptList = res.data.reRuleScriptList
            this.reRuleVersionList = res.data.reRuleVersionList
          } else {
            this.$Message.warning("查询失败:" + res.data)
          }
        })
      },

      editItem: function (index) {
        debugger
        const self = this
        let data = self.reRuleList[index]
        console.log(JSON.stringify(data))
        self.setEditData(data)
      },
      // 父组件触发编辑操作，设置表单数据
      setEditData(data) {
        const self = this
        self.editFormItem = data
        self.opt_type = 2
        self.addLayerState = true
      },
      closeModal() {
        this.addLayerState = false
        this.loadingState = false
      },

      closeCopyModal() {
        this.copyLayerState = false
        this.loadingCopyState = false
      },

      addTask() {
        this.editFormItem = {};
        this.opt_type = 1;
        this.addLayerState = true;
      },

      copyConfig() {
        this.copyFormItem = {
          srcActId: this.formItem.actId,
          targetActId: ''
        };
        this.copyLayerState = true;
      },

      addRule(name) {
        const self = this
        let reqData = self.editFormItem
        let flag = false;
        this.$refs[name].validate((valid) => {
          if (valid) {
            flag = true;
          }
        });
        if (!flag) {
          this.loadingState = false
          return;
        }

        //reqData = JSON.stringify(reqData);
        console.log(reqData)
        Api.addReRule(reqData).then(res => {
          if (res.result === '0') {
            self.$Message.success("添加成功:" + res.data);
            this.formItem.ruleId = res.data
            this.queryReRule()
          } else {
            alert(("添加失败:" + res.data));
          }
        })
        this.addLayerState = false
        this.loadingState = false
      },

      updateRuleWithValid(name) {
        this.$refs[name].validate().then(() => {
          this.updateRule();
          this.editFormItem = {};
          this.queryRule()
        }).catch(() => {
          this.loadingState = false
          this.editFormItem = {};
          this.queryRule()
        });

      },

      updateRule() {
        debugger
        const self = this
        let reqData = self.editFormItem;

        reqData = JSON.stringify(reqData);
        console.log('reqdata->' + reqData)
        Api.updateRule(reqData).then(res => {
          if (res.result === '0') {
            self.$Message.success("修改成功:" + res.data);
          } else {
            alert(("修改失败:" + res.data));
          }
        })
        this.addLayerState = false
        this.loadingState = false
      },

      copyRule() {
        const self = this
        let reqData = self.copyFormItem;

        reqData = JSON.stringify(reqData);
        console.log('reqdata->' + reqData)
        Api.copyRule(reqData).then(res => {
          if (res.result === '0') {
            self.$Message.success("修改成功:" + res.data);
            this.formItem.actId = self.copyFormItem.targetActId;
            this.queryRule();
          } else {
            alert(("修改失败:" + res.data));
          }
        })
        this.copyLayerState = false
        this.loadingCopyState = false
      }
    }
  };
</script>
