<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="活动id">
<!--            <el-select v-model="currentActId" placeholder="请选择活动"
                       filterable style="width:180px">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>-->
            <activity-selector v-model="currentActId" style="width:180px" />
          </el-form-item>

          <el-button type="primary" :loading="checkingActivity" @click="loadConfigData" size="medium">
            加载
          </el-button>
          <el-tooltip class="item" effect="light" content="选择需要校验的活动查询" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>
      <el-row>
        <activity-info-desc ref="actInfo" :act-id="currentActId" />
      </el-row>
      <config-category ref="configCategory" v-for="category in configCategories" :key="category.id" :category-name="category.categoryName" :category-id="category.id" />
    </div>
    <json-modal ref="jsonModal" />
    <award-detail-modal ref="awardDetailModal" />
  </div>
</template>
<script>
import { queryAllConfigCategories } from "@/api/activity/configCenter"
import ActivitySelector from "@/components/ActivitySelector"
import ConfigCategory from "./cmpt/ConfigCategory.vue"
import ActivityInfoDesc from "./cmpt/ActivityInfoDesc.vue"
import JsonModal from "@/components/Json/JsonModal.vue"
import AwardDetailModal from "./cmpt/AwardDetailModal.vue"

export default {
  name: "configCenter",
  components: { ActivitySelector, AwardDetailModal, JsonModal, ActivityInfoDesc, ConfigCategory },
  data: () => ({
    currentActId: '',
    configCategories: [],
    checkingActivity: false
  }),
  created() {
    const _query = this.$route.query
    if (_query && _query.actId) {
      this.currentActId = _query.actId
    }
    this.loadAllConfigCategories()
  },
  beforeMount() {
    this.$bus.$on('showSnippetValue', this.showSnippetValue)
    this.$bus.$on('showAwardDetail', this.showAwardDetail)
  },
  methods: {
    loadAllConfigCategories() {
      const self = this
      queryAllConfigCategories().then(res => {
        res.result === 200 && (self.configCategories = res.data)
        res.result === 200 || self.$message({'message': res.reason, 'type': 'warning'})
        if (self.currentActId) {
          self.$nextTick(() => {
            self.loadConfigData()
          })
        }
      })
    },
    loadConfigData() {
      if (!this.currentActId) {
        this.$message({'message': '请先选择一个活动', type: 'error'})
        return
      }

      const actId = this.currentActId;
      this.$refs.actInfo.queryActInfo(actId)
      this.$refs.configCategory.forEach(ref => ref.loadConfigItems((actId)))
    },
    showSnippetValue({ title, content }) {
      this.$refs.jsonModal.showModal(content, title)
    },
    showAwardDetail({ actId, taskId, taskName }) {
      this.$refs.awardDetailModal.showAwardDetail(actId, taskId, taskName)
    }
  }
}
</script>

<style scoped>

</style>
