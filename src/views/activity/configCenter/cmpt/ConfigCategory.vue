<template>
  <div v-show="configItems.length > 0">
    <el-row>
      <el-divider content-position="center">{{ categoryName }}</el-divider>
    </el-row>
    <el-row :gutter="20" v-for="items in groupedItems" :key="items[0].itemId">
      <el-col :span="24 / rows" v-for="item in items" :key="item.itemId">
        <config-item :item="item" :category-id="categoryId" />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import ConfigItem from "./ConfigItem.vue"
import { queryConfigItems } from "@/api/activity/configCenter"
export default {
  name: 'layerViewDefineCard',
  components: { ConfigItem },
  data() {
    return {
      configItems: []
    }
  },
  props: {
    categoryId: {
      type: Number,
      required: true
    },
    categoryName: {
      type: String,
      required: true
    },
    rows: {
      type: Number,
      required: false,
      default: 3
    }
  },
  computed: {
    groupedItems() {
      const total = this.configItems.length
      const result = []
      for (let i = 0; i < total; i++) {
        if (i % this.rows === 0) {
          console.log('add subList', i, this.rows)
          const subList = []
          result.push(subList)
        }

        console.log(i, this.rows, result)
        result[Math.floor(i / this.rows)].push(this.configItems[i])
      }

      return result
    }
  },
  methods: {
    loadConfigItems(actId) {
      const self = this
      queryConfigItems(actId, this.categoryId).then((res) => {
        if (res.result === 200) {
          self.configItems = res.data
        } else {
          self.$message({message: '获取配置项失败：' + res.reason, type: 'error'})
        }
      }).catch((err) => {
        self.$message({message: '获取配置项失败', type: 'error'})
      }).finally(() => {

      })
    }
  }
}
</script>

<style>

</style>
