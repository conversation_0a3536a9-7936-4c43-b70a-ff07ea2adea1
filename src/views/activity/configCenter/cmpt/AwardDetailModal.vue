<template>
  <el-dialog :visible.sync="dialogVisible" :title="title" width="942px">
    <el-table :data="tableData" border>
      <el-table-column align="center" prop="packageId" label="奖包ID" width="100" />
      <el-table-column align="center" prop="packageName" label="奖包名称" width="200" />
      <el-table-column align="center" prop="probability" label="中奖概率" width="150" />
      <el-table-column align="center" prop="packageTotal" :formatter="limitFormatter" label="总上限" width="150" />
      <el-table-column align="center" prop="userHitLimit" :formatter="limitFormatter" label="用户上限" width="150" />
      <el-table-column align="center" prop="dailyHitLimit" :formatter="limitFormatter" label="每日上限" width="150" />
    </el-table>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import Api from '@/api/activity/awardPackage'
export default {
  name: "AwardDetailModal",
  data() {
    return {
      dialogVisible: false,
      title: '奖池概率&上限',
      tableData: []
    }
  },
  methods: {
    showAwardDetail(actId, taskId, taskName) {
      this.title = '【' + taskName + '】概率&上限'
      this.tableData = []
      const self = this
      Api.listAwardPackage(actId, taskId).then(
        res => {
          if (res.result === 200) {
            self.tableData = res.data
            self.dialogVisible = true
          } else {
            self.$message({message: res.reason, type: 'error'})
          }
        }
      )
    },
    limitFormatter(row, column, cellValue) {
      if (cellValue < 0) {
        return '不限制'
      }

      return cellValue
    }
  }
}
</script>

<style scoped lang="scss">

</style>
