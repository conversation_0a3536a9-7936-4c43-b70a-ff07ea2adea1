<template>
  <div class="snippet-item">
    <span class="snippet-item-label" :title="snippet.snippetDesc">{{ snippet.snippetName | formatLabel }}：</span>
    <span class="snippet-item-value">
      <el-button type="text" @click="showSnippetValue">点击查看</el-button>
    </span>
    <snippet-icon :state="snippet.snippetState" @saveState="saveSnippetState" />
  </div>
</template>
<script>
import SnippetIcon from "./SnippetIcon.vue";

export default {
  name: "ObjectSnippet",
  components: { SnippetIcon },
  props: {
    snippet: {
      type: Object,
      required: true
    }
  },
  filters: {
    formatLabel(name) {
      if (name.length < 8) {
        return name
      }

      return name.substring(0, 6) + '...'
    }
  },
  methods: {
    showSnippetValue() {
      this.$bus.$emit('showSnippetValue', { title: this.snippet.snippetName, content: this.snippet.snippetValue })
    },
    saveSnippetState(newState) {
      this.$emit('saveSnippetState', { snippetId: this.snippet.snippetId, newState })
    }
  }
}
</script>

<style scoped>
.snippet-item {
  padding: 13px 3px;
  display: grid;
  grid-template-columns: 100px auto 22px;
  place-items: center start;
}

.snippet-item > *:first-child {
  justify-self: end;
}

.snippet-item > *:last-child {
  justify-self: center;
  align-self: center;
}

.snippet-item .snippet-item-label {
  font-weight: bold;
  float: left;
}

.snippet-item .snippet-item-value {
  white-space: nowrap;
}
</style>
