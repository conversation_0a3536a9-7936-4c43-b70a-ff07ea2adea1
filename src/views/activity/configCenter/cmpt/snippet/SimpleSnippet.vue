<template>
  <div class="snippet-item">
    <span class="snippet-item-label" :title="snippet.snippetDesc">
<!--      <el-tooltip effect="light" placement="top" v-if="snippet.snippetDesc" :content="snippet.snippetDesc">
        <i class="el-icon-question"></i>
      </el-tooltip>-->
      {{ snippet.snippetName | formatLabel }}：
    </span>
    <span class="snippet-item-value">
      <span :title="showTitle">{{ showValue }}</span>
      <el-popover v-if="snippet.snippetValue.length > showSize" placement="top" :content="snippet.snippetValue" trigger="click">
        <i slot="reference" title="点击查看完整数据" class="el-icon-question" />
      </el-popover>
    </span>
    <snippet-icon :state="snippet.snippetState" @saveState="saveSnippetState" />
  </div>
</template>
<script>
import SnippetIcon from "./SnippetIcon.vue";
export default {
  name: 'simpleSnippet',
  components: { SnippetIcon },
  props: {
    snippet: {
      type: Object,
      required: true
    },
    showSize: {
      type: Number,
      required: false,
      default: 40
    }
  },
  computed: {
    showValue() {
      if (this.snippet.snippetValue && this.snippet.snippetValue.length > this.showSize) {
        return this.snippet.snippetValue.substring(0, this.showSize) + '...'
      }

      return this.snippet.snippetValue
    },
    showTitle() {
      if (this.snippet.snippetValue && this.snippet.snippetValue.length > this.showSize) {
        return this.snippet.snippetValue
      }

      return ""
    }
  },
  filters: {
    formatLabel(name) {
      if (name.length < 8) {
        return name
      }

      return name.substring(0, 6) + '...'
    }
  },
  methods: {
    saveSnippetState(newState) {
      this.$emit('saveSnippetState', {snippetId: this.snippet.snippetId, newState})
    }
  }
}
</script>

<style scoped>
.snippet-item {
  padding: 13px 3px;
  display: grid;
  grid-template-columns: 100px auto 22px;
  grid-template-rows: 25px;
  place-items: center start;
}

.snippet-item > *:first-child {
  justify-self: end;
}

.snippet-item > *:last-child {
  justify-self: center;
  align-self: center;
}

.snippet-item .snippet-item-label {
  font-weight: bold;
  float: left;
  white-space: nowrap;
  overflow: hidden;
}

.snippet-item .snippet-item-value {
  white-space: nowrap;
}

.snippet-item .snippet-item-value i {
  cursor: pointer;
}
</style>
