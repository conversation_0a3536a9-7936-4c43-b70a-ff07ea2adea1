<template>
  <div class="snippet-item">
    <span class="snippet-item-label" :title="snippet.snippetDesc">{{ snippet.snippetName }}：</span>
    <span class="snippet-item-size">{{ snippet.snippetValue }} 个</span>
    <el-button type="text" @click="jumpToWhitelist">跳转查看</el-button>
    <snippet-icon :state="snippet.snippetState" @saveState="saveSnippetState" />
  </div>
</template>

<script>

import SnippetIcon from "./SnippetIcon.vue"

export default {
  name: 'hugeWhitelistSnippet',
  components: { SnippetIcon },
  props: {
    snippet: {
      type: Object,
      required: true
    }
  },
  methods: {
    saveSnippetState(newState) {
      this.$emit('saveSnippetState', {snippetId: this.snippet.snippetId, newState})
    },
    jumpToWhitelist() {
      this.$emit('jumpWhitelist', this.snippet.snippetId)
    }
  }
}
</script>

<style scoped>
.snippet-item {
  padding: 6px 3px;
  display: grid;
  grid-template-columns: 200px 80px auto 22px;
  place-items: center start;
}

.snippet-item > *:first-child {
  justify-self: end;
}

.snippet-item > *:last-child {
  justify-self: center;
  align-self: center;
}

.snippet-item .snippet-item-label {
  font-weight: bold;
}
</style>
