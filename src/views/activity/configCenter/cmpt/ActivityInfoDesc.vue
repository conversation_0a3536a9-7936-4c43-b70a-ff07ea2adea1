<template>
  <div class="act-info-wrapper" v-show="actId !== ''">
    <el-descriptions title="活动信息" :column="5" :label-style="{'font-weight': 'bold'}" size="medium" v-loading="loadingActInfo">
      <el-descriptions-item label="活动标识">{{ actId }}</el-descriptions-item>
      <el-descriptions-item label="活动名称">{{ actName }}</el-descriptions-item>
      <el-descriptions-item label="中台灰度">
        <el-tag :type="getGreyType(ztGreyStatus)" size="small">{{ ztGreyStatus | greyTxt }}</el-tag>
      </el-descriptions-item>
<!--      <el-descriptions-item label="发奖灰度">-->
<!--        <el-tag type="info" size="small">{{ awardGreyStatus | greyTxt }}</el-tag>-->
<!--      </el-descriptions-item>-->
      <el-descriptions-item label="中控灰度">
        <el-tag :type="getGreyType(zkGreyStatus)" size="small">{{ zkGreyStatus | greyTxt }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="活动状态">
        <el-tag :type="actStatusType" size="small">{{ actStatusTxt }}</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="开始时间">{{ beginTime }}</el-descriptions-item>
      <el-descriptions-item label="结束时间">{{ endTime }}</el-descriptions-item>
      <el-descriptions-item label="开始展示时间">{{ beginShowTime }}</el-descriptions-item>
      <el-descriptions-item label="结束展示时间">{{ endShowTime }}</el-descriptions-item>
    </el-descriptions>
  </div>
</template>
<script>

import { queryActivityInfo } from "@/api/activity/configCenter"

export default {
  name: "ActivityInfoDesc",
  data() {
    return {
      loadingActInfo: false,
      actId: '',
      actName: '',
      actStatus: 0,
      ztGreyStatus: 0,
      awardGreyStatus: 0,
      zkGreyStatus: 0,
      beginTime: '',
      endTime: '',
      beginShowTime: '',
      endShowTime: ''
    }
  },
  methods: {
    queryActInfo(_actId) {
      const self = this
      this.loadingActInfo = true
      queryActivityInfo(_actId).then(res => {
        if (res.result !== 200) {
          self.$message({'message': res.reason, 'type': 'warning'})
          return
        }

        self.actId = res.data.actId
        self.actName = res.data.actName
        self.actStatus = res.data.actStatus
        self.ztGreyStatus = res.data.ztGreyStatus
        self.awardGreyStatus = res.data.awardGreyStatus
        self.zkGreyStatus = res.data.zkGreyStatus
        self.beginTime = res.data.beginTime
        self.endTime = res.data.endTime
        self.beginShowTime = res.data.beginShowTime
        self.endShowTime = res.data.endShowTime
      }).catch(err => {
        self.$message({message: '数据加载失败', type: 'error'})
        console.log(err)
      }).finally(() => {
        self.loadingActInfo = false
      })
    },
    getGreyType(greyStatus) {
      if (greyStatus === 1) {
        return 'info'
      }

      return 'success'
    }
  },
  computed: {
    actStatusTxt() {
      if (this.actStatus === 1) {
        return '运行中'
      } else if (this.actStatus === 77) {
        return '离线回收'
      } else {
        return '未生效'
      }
    },
    actStatusType() {
      if (this.actStatus === 1) {
        return 'success'
      } else if (this.actStatus === 77) {
        return 'info'
      } else {
        return 'error'
      }
    }
  },
  filters: {
    greyTxt(greyStatus) {
      if (greyStatus === 1) {
        return '灰度中'
      }

      return '非灰度'
    }
  }
}
</script>

<style scoped lang="scss">

</style>
