<template>
  <el-card class="box-card" v-loading="loadingSnippets">
    <div slot="header" class="clearfix">
      <span class="item-name">{{ item.itemName }}</span>
      <span class="item-desc" v-if="item.itemDesc">[{{ item.itemDesc }}]</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="loadConfigSnippets">触发校验</el-button>
    </div>
    <div v-for="snippet in item.snippets" :key="snippet.snippetId">
      <simple-snippet v-if="snippet.snippetType === 'simple'" :snippet="snippet" @saveSnippetState="saveSnippetState" />
      <whitelist-snippet v-else-if="snippet.snippetType === 'whitelist'" :snippet="snippet" @showSnippet="showSnippetValue" @saveSnippetState="saveSnippetState" />
      <link-snippet v-else-if="snippet.snippetType === 'link'" :snippet="snippet" @saveSnippetState="saveSnippetState" />
      <award-snippet v-else-if="snippet.snippetType === 'award'" :snippet="snippet" @showAward="showAwardDetail" @saveSnippetState="saveSnippetState" />
      <object-snippet v-else-if="snippet.snippetType === 'object'" :snippet="snippet" @saveSnippetState="saveSnippetState" />
      <huge-whitelist-snippet v-else-if="snippet.snippetType === 'hugeWhitelist'" :snippet="snippet" @jumpWhitelist="openComponentWhitelist" @saveSnippetState="saveSnippetState" />
    </div>
  </el-card>
</template>
<script>
import WhitelistSnippet from "./snippet/WhitelistSnippet.vue"
import SimpleSnippet from "./snippet/SimpleSnippet.vue"
import LinkSnippet from "./snippet/LinkSnippet.vue"
import AwardSnippet from "./snippet/AwardSnippet.vue"
import { queryConfigSnippets, saveConfigSnippetState, queryConfigSnippetValue } from '@/api/activity/configCenter'
import ObjectSnippet from "./snippet/ObjectSnippet.vue";
import HugeWhitelistSnippet from "./snippet/HugeWhitelistSnippet.vue";

export default {
  name: "ConfigItem",
  components: {HugeWhitelistSnippet, ObjectSnippet, AwardSnippet, LinkSnippet, SimpleSnippet, WhitelistSnippet },
  data() {
    return {
      loadingSnippets: false
    }
  },
  props: {
    item: {
      type: Object,
      required: true
    },
    categoryId: {
      type: Number,
      required: true
    }
  },
  methods: {
    loadConfigSnippets() {
      const self = this
      this.loadingSnippets = true
      queryConfigSnippets(this.item.actId, this.categoryId, this.item.itemId).then(res => {
        res.result === 200 && (self.item.snippets = res.data)
        res.result === 200 || self.$message({message: '获取配置项失败：' + res.reason, type: 'error'})
      }).catch(err => {
        self.$message({message: '获取配置项失败' + err, type: 'error'})
      }).finally(() => {
        self.loadingSnippets = false
      })
    },
    showSnippetValue({snippetId, snippetName}) {
      const self = this
      queryConfigSnippetValue(this.item.actId, this.categoryId, this.item.itemId, snippetId).then(res => {
        if (res.result !== 200) {
          self.$message({message: '获取配置值失败，请稍后再试' + res.reason, type: 'error'})
          return
        }

        self.$bus.$emit('showSnippetValue', { title: snippetName, content: res.data })
      })
    },
    showAwardDetail() {
      this.$bus.$emit('showAwardDetail', { actId: this.item.actId, taskId: this.item.itemId, taskName: this.item.itemName })
    },
    saveSnippetState({ snippetId, newState }) {
      const self = this
      saveConfigSnippetState(this.item.actId, this.categoryId, this.item.itemId, snippetId, newState).then(res => {
        if (res.result !== 200) {
          self.$message({message: '保存校验状态失败，' + res.reason, type: 'error'})
          return
        }

        self.$message({message: '状态保存成功', type: 'success'})
        self.loadConfigSnippets()
      }).catch(err => {
        self.$message({message: '保存校验状态失败，' + err, type: 'error'})
      })
    },
    openComponentWhitelist(componentIndex) {
      let url = `/actComponentModul/cmptAttrConfig?actId=${this.item.actId}&componentId=2055&useIndex=${componentIndex}`
      let contextPath = process.env.VUE_APP_CONTEXT_PATH
      if (contextPath && contextPath.endsWith('/')) {
        contextPath = contextPath.substring(0, contextPath.length - 1)
      }

      url = contextPath + url
      window.open(url)
    }
  }
}
</script>

<style scoped lang="css">
.box-card {
  margin-top: 10px;
}
.item-name {
  font-weight: 600;
  font-size: 14px;
}

.item-desc {
  margin-left: 3px;
  color: #626b7d;
  font-size: 13px;
}
</style>
