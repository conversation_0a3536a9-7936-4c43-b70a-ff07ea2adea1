<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="活动分组">
            <el-select v-model="searchForm.group" placeholder="活动分组" style="width:150px" clearable>
              <el-option v-for="item in actGroup" :value="item.code" :key="item.code"
                         :label='item.desc+"("+item.code+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="活动id">
            <el-select v-model="searchForm.actId" placeholder="请选择活动"
                       filterable clearable style="width:300px">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="活动状态">
            <el-select v-model="searchForm.status" placeholder="活动状态"
                       filterable style="width:150px" clearable>
              <el-option v-for="item in actStatus" :value="item.code" :key="item.code"
                         :label='item.desc+"("+item.code+")"'>
              </el-option>
            </el-select>
          </el-form-item>


          <el-button type="primary" :loading="loadingActBackupConfig" @click="onSearch(true)" icon="el-icon-search"
                     size="medium">
            查询
          </el-button>

          <el-tooltip class="item" effect="light" content="活动结束->复制zt\zk redis数据-->pika->设置归档(读取pika)"
                      placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>
      <el-row>
        <el-table :data="tableData" border v-loading="loadingActBackupConfig">
          <el-table-column prop="actId" label="活动ID" align="center" width="110"></el-table-column>
          <el-table-column prop="actName" label="活动名称" align="center" width="200"></el-table-column>
          <el-table-column prop="beginTime" label="活动开始时间" align="center" width="180"></el-table-column>
          <el-table-column prop="endTime" label="活动结束时间" align="center" width="180"></el-table-column>
          <el-table-column label="活动状态" align="center" width="150">
            <template slot-scope="scope">
              <el-tag :type="getStatusStyle(scope.row)" size="medium">{{scope.row | getStatusText}}（{{scope.row.status}}）</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="当前步骤" align="center" width="250">
            <template slot-scope="scope">
              <el-popover trigger="hover" placement="top" v-if="scope.row.currentStep">
                <p>{{scope.row.currentStep.stepDesc}}</p>
                <div slot="reference" class="name-wrapper">
                  <el-tag type="success" size="medium">{{scope.row.currentStep.step}}、{{scope.row.currentStep.stepName}}</el-tag>
                </div>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column label="操作" fixed="right" align="center">
            <template slot-scope="scope">
              <el-link type="success" @click="goOfflineOperation(scope.row)">操作详情</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="[10,15,20,50,100]"
          @size-change="onSearch(false)"
          @current-change="onSearch(false)"
          layout="->, total, prev, pager, next, sizes"
          :current-page.sync="pageData.page"
          :page-size.sync="pageData.size"
          :total="pageData.total">
        </el-pagination>
      </el-row>
    </div>
  </div>
</template>

<script>
  import API from '@/api/activity/actOffline'
  import ActConfigVersion from '@/api/activity/actConfigVersion'

  export default {
    name: "ActOffline",
    components: {},
    data: function () {
      return {
        loadingActBackupConfig: false,
        selectActList: [],
        actGroup: [
          {code: '1', desc: '分组1'},
          {code: '2', desc: '分组2'},
          {code: '3', desc: '分组3'},
          {code: '4', desc: '分组4'},
          {code: '5', desc: '分组5'},
          {code: '6', desc: '分组6'},
          {code: '7', desc: '分组7'},
          {code: '8', desc: '分组8'},
          {code: '9', desc: '分组9'}
        ],
        actStatus: [
          {code: '1', desc: '有效'},
          {code: '101', desc: '未生效'},
          {code: '77', desc: '已完成'}
        ],
        statusList: [],
        searchForm: {
          actId: '',
          backupType: '',
          backupSource: '',
          status: ''
        },
        pageData: {
          page: 1,
          size: 15,
          total: 0,
        },
        tableData: []
      }
    },
    created: function () {
      this.loadActInfos()
    },
    filters: {
      getStatusText(row) {
        if (row.currentStep) {
          return '进行中'
        } else if (row.status === 1) {
          return '未开始'
        } else if (row.status === 77) {
          return '已完成'
        }

        return '无效'
      }
    },
    methods: {
      onSearch(fromBtn) {
        const req = this.searchForm
        if (req.group === '' || isNaN(req.group)) {
          this.$Message.warning("请选择要查询的分组")
          return
        }
        const self = this
        if (fromBtn) {
          self.pageData.page = 1
        }
        req.pageNo = self.pageData.page
        req.pageSize = self.pageData.size
        self.loadingActBackupConfig = true
        API.queryActList(req)
          .then(res => {
            if (res.code === 200) {
              self.pageData.total = res.total
              self.tableData = res.data
            } else {
              self.$message({message: '获取列表数据失败:' + res.reason, type: 'warning'})
            }
            self.loadingActBackupConfig = false
          })
          .catch(error => {
            self.loadingActBackupConfig = false
            console.log(error)
          })
      },
      loadActInfos() {
        ActConfigVersion.loadActInfos().then(
          res => {
            if (res.result === 200) {
              this.selectActList = res.data
            } else {
              alert("拉取活动列表错误," + res.reason)
            }
          }
        )
      },
      getStatusStyle(row) {
        if (row.currentStep) {
          return 'success'
        } else if (row.status === 1) {
          return ''
        } else if (row.status === 77) {
          return 'info'
        }

        return 'danger'
      },
      goOfflineOperation(config) {
        this.$router.push({path: 'actOfflineOperation', query: {actId: config.actId}});
      }
    }
  };
</script>
