<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="活动id">
            <el-select v-model="actId" placeholder="请选择活动" filterable style="width:180px" @change="loadAllPhases">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                         :label='item.act_name+"("+item.act_id+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="所属赛程">
            <el-select v-model="phaseId" placeholder="根据时间请选择赛程" style="width: 340px" @change="loadPhaseInfo">
              <el-option v-for="item in selectPhaseList" :value="item.phaseId" :key="item.phaseId" :label="item.startTime + ' ~ ' + item.endTime" />
            </el-select>

            <el-button type="primary" size="medium" @click="loadPhaseInfo">
              加载
            </el-button>
          </el-form-item>
        </el-form>
      </el-row>
      <el-row>
        <div v-show="phaseId !== ''">
          <el-descriptions title="基本信息" :column="5" :label-style="{'font-weight': 'bold'}" size="medium">
            <el-descriptions-item label="阶段">{{ phaseId }}</el-descriptions-item>
            <el-descriptions-item label="报名开始">{{ phaseInfo.signupStartTime }}</el-descriptions-item>
            <el-descriptions-item label="报名截止">{{ phaseInfo.signupEndTime }}</el-descriptions-item>
            <el-descriptions-item label="调整截止">{{ phaseInfo.adjustingEndTime }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-row>
      <el-row>
        <el-card v-show="phaseId !== ''" class="box-card">
          <div slot="header" class="clearfix">
            <span>轮次信息</span>
          </div>
          <el-table>
            <el-table-column label="轮次" />
            <el-table-column label="轮次名称" />
            <el-table-column label="BO" />
            <el-table-column label="比赛模式" />
            <el-table-column label="开始时间" />
            <el-table-column label="状态" />
          </el-table>
        </el-card>
      </el-row>

      <el-row>
        <el-card v-show="phaseId !== ''" class="box-card">
          <div slot="header" class="clearfix">
            <span>奖励信息</span>
          </div>
          <el-table>
            <el-table-column label="轮次" />
            <el-table-column label="轮次名称" />
            <el-table-column label="BO" />
            <el-table-column label="比赛模式" />
            <el-table-column label="开始时间" />
            <el-table-column label="状态" />
          </el-table>
        </el-card>
      </el-row>
    </div>
  </div>
</template>

<script>
import ActConfigVersion from '@/api/activity/actConfigVersion'
import { queryAllPhases, queryPhaseDetail, queryPhaseRounds, queryPhaseAwards } from '@/api/activity/aov'
export default {
  name: "aovPhase",
  data: () => ({
    actId: '',
    selectActList: [],
    phaseId: '',
    selectPhaseList: [],
    phaseInfo: {

    }
  }),
  created() {
    this.loadActInfos()
  },
  methods: {
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    loadAllPhases() {
      this.phaseId = ''
      const self = this
      queryAllPhases(this.actId).then(res => {
        if (res.result === 200) {
          self.selectPhaseList = res.data
          return
        }

        self.$message({message: '获取所有赛程失败', type: 'error'})
      })
    },
    loadPhaseInfo() {
      this.phaseInfo = {}
      const self = this
      queryPhaseDetail(this.actId, this.phaseId).then(res => {
        if (res.result === 200) {
          self.phaseInfo = res.data
          return
        }

        self.$message({message: '获取所有赛程失败', type: 'error'})
      })
    }
  }
}
</script>


<style scoped lang="scss">
.box-card {
  margin-top: 15px;
  width: 95%;
}
</style>
