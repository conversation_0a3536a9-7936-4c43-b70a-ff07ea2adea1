<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true">
          <el-form-item label="活动id">
            <el-select v-model="actId" placeholder="请选择活动" filterable style="width:180px" @change="loadAllPhases">
              <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id" :label='item.act_name+"("+item.act_id+")"' />
            </el-select>
          </el-form-item>

          <el-form-item label="所属赛程">
            <el-select v-model="phaseId" placeholder="根据时间请选择赛程" style="width: 340px" @change="loadAllRounds">
              <el-option v-for="item in selectPhaseList" :value="item.phaseId" :key="item.phaseId" :label="item.startTime + ' ~ ' + item.endTime" />
            </el-select>
          </el-form-item>

          <el-form-item label="轮次">
            <el-select v-model="roundNum" placeholder="选择轮次" style="width:180px">
              <el-option v-for="item in selectRoundList" :value="item.roundNum" :key="item.id" :label='item.roundName' />
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="medium" @click="loadRoundGames">
              加载
            </el-button>
          </el-form-item>
        </el-form>
      </el-row>

      <el-row>
        <el-table :data="tableData" border>
          <el-table-column type="expand">
            <template v-slot="props">
              <div class="inner-table">
                <el-table :data="props.row.games" border stripe>
                  <el-table-column type="expand">
                    <template v-slot="scope">
                      <div class="team-container">
                        <div class="team">
                          <el-descriptions title="蓝方战队" :column="6">
                            <el-descriptions-item label="队伍ID">{{ scope.row.team1.teamId }}</el-descriptions-item>
                            <el-descriptions-item label="队伍名称" :span="3">
                              <el-button type="primary" round plain size="mini">{{scope.row.team1.teamName}}</el-button>
                            </el-descriptions-item>
                            <el-descriptions-item label="比赛结果" :span="2">
                              <el-tag v-if="scope.row.team1.state === 0" type="info">初始化</el-tag>
                              <el-tag v-else-if="scope.row.team1.state === 10" type="success"  effect="dark">赛宝满人</el-tag>
                              <el-tag v-else-if="scope.row.team1.state === 20" type="danger"  effect="dark">赛宝不满人</el-tag>
                              <el-tag v-else-if="scope.row.team1.state === 30" type="success"  effect="dark">胜利</el-tag>
                              <el-tag v-else-if="scope.row.team1.state === 40" type="danger"  effect="dark">失败</el-tag>
                              <el-tag v-else-if="scope.row.team1.state === 50" type="warning"  effect="dark">无比赛结果</el-tag>
                              <el-tag v-else>未定义</el-tag>
                            </el-descriptions-item>
                          </el-descriptions>
                          <el-table :data="scope.row.team1.members" style="width: 100%" border stripe>
                            <el-table-column align="center" width="50" label="座号" prop="seatId" />
                            <el-table-column align="center" width="180" label="UID">
                              <template v-slot="item">
                                <el-popover placement="right" width="300" trigger="click">
                                  <el-descriptions title="详细信息" size="small" :column="1">
                                    <el-descriptions-item label="YY号">{{item.row.yy}}</el-descriptions-item>
                                    <el-descriptions-item label="OpenID">{{item.row.openId}}</el-descriptions-item>
                                  </el-descriptions>
                                  <el-tag slot="reference" type="info" effect="plain">{{ item.row.uid }}</el-tag>
                                </el-popover>
                              </template>
                            </el-table-column>
                            <el-table-column align="center" label="昵称" prop="nick" />
                            <el-table-column align="center" width="100" label="状态">
                              <template v-slot="item">
                                <el-tag v-if="item.row.state === 10">进游戏</el-tag>
                                <el-tag v-else-if="item.row.state === 20" type="success">已准备</el-tag>
                                <el-tag v-else-if="item.row.state === 30" type="danger" effect="dark">未准备</el-tag>
                                <el-tag v-else-if="item.row.state === 40" type="success" effect="dark">完成比赛</el-tag>
                                <el-tag v-else-if="item.row.state === 50" type="success" effect="dark">完成比赛</el-tag>
                                <el-tag v-else type="info" effect="dark">未定义</el-tag>
                              </template>
                            </el-table-column>
                            <el-table-column align="center" width="60" label="击杀数" prop="killCnt" />
                          </el-table>
                        </div>
                        <div class="vs">VS</div>
                        <div class="team">
                          <el-descriptions title="红方战队" :column="6">
                            <el-descriptions-item label="队伍ID">{{ scope.row.team2.teamId }}</el-descriptions-item>
                            <el-descriptions-item label="队伍名称" :span="3">
                              <el-button type="danger" round plain size="mini">{{scope.row.team2.teamName}}</el-button>
                            </el-descriptions-item>
                            <el-descriptions-item label="比赛结果" :span="2">
                              <el-tag v-if="scope.row.team2.state === 0" type="info">初始化</el-tag>
                              <el-tag v-else-if="scope.row.team2.state === 10" type="success"  effect="dark">赛宝满人</el-tag>
                              <el-tag v-else-if="scope.row.team2.state === 20" type="danger"  effect="dark">赛宝不满人</el-tag>
                              <el-tag v-else-if="scope.row.team2.state === 30" type="success"  effect="dark">胜利</el-tag>
                              <el-tag v-else-if="scope.row.team2.state === 40" type="danger"  effect="dark">失败</el-tag>
                              <el-tag v-else-if="scope.row.team2.state === 50" type="warning"  effect="dark">无比赛结果</el-tag>
                              <el-tag v-else>未定义</el-tag>
                            </el-descriptions-item>
                          </el-descriptions>
                          <el-table :data="scope.row.team2.members" style="width: 100%" border stripe>
                            <el-table-column align="center" width="50" label="座号" prop="seatId" />
                            <el-table-column align="center" width="180" label="UID">
                              <template v-slot="item">
                                <el-popover placement="right" width="300" trigger="click">
                                  <el-descriptions title="详细信息" size="small" :column="1">
                                    <el-descriptions-item label="YY号">{{item.row.yy}}</el-descriptions-item>
                                    <el-descriptions-item label="OpenID">{{item.row.openId}}</el-descriptions-item>
                                  </el-descriptions>
                                  <el-tag slot="reference" type="info" effect="plain">{{ item.row.uid }}</el-tag>
                                </el-popover>
                              </template>
                            </el-table-column>
                            <el-table-column align="center" label="昵称" prop="nick" />
                            <el-table-column align="center" width="100" label="状态">
                              <template v-slot="item">
                                <el-tag v-if="item.row.state === 10">进游戏</el-tag>
                                <el-tag v-else-if="item.row.state === 20" type="success">已准备</el-tag>
                                <el-tag v-else-if="item.row.state === 30" type="danger" effect="dark">未准备</el-tag>
                                <el-tag v-else-if="item.row.state === 40" type="success" effect="dark">完成比赛</el-tag>
                                <el-tag v-else-if="item.row.state === 50" type="success" effect="dark">完成比赛</el-tag>
                                <el-tag v-else type="info" effect="dark">未定义</el-tag>
                              </template>
                            </el-table-column>
                            <el-table-column align="center" width="60" label="击杀数" prop="killCnt" />
                          </el-table>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" width="80" label="比赛场次" prop="curBo" />
                  <el-table-column align="center" width="120" label="比赛ID" prop="gameId" />
                  <el-table-column align="center" label="腾讯比赛ID" prop="childId" />
                  <el-table-column align="center" label="开始时间" prop="startTime" />
                  <el-table-column align="center" width="120" label="比赛状态">
                    <template v-slot="scope">
                      <el-tag v-if="scope.row.state === 0" size="medium" type="info" effect="plain">初始化</el-tag>
                      <el-tag v-else-if="scope.row.state === 10" size="medium" type="info">创建中</el-tag>
                      <el-tag v-else-if="scope.row.state === 20" size="medium">创建完成</el-tag>
                      <el-tag v-else-if="scope.row.state === 30" size="medium" type="success">赛宝完备</el-tag>
                      <el-tag v-else-if="scope.row.state === 40" size="medium" type="danger">取消中</el-tag>
                      <el-tag v-else-if="scope.row.state === 50" size="medium" type="danger" effect="dark">已取消</el-tag>
                      <el-tag v-else-if="scope.row.state === 60" size="medium" type="success" effect="dark">完成</el-tag>
                      <el-tag v-else-if="scope.row.state === 70" size="medium" type="warning" effect="dark">超时无结果</el-tag>
                      <el-tag v-else size="medium">已结算</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="赛宝完备时间" prop="readyTime" />
                  <el-table-column align="center" label="结束时间" prop="endTime" />
                  <el-table-column align="center" label="结算状态">
                    <template v-slot="scope">
                      <el-tag v-if="scope.row.settleState === 0" size="medium" type="info">未结算</el-tag>
                      <el-tag v-else size="medium" type="success">已结算</el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" width="60" label="轮次" prop="roundNum" />
          <el-table-column align="center" width="120" label="名称" prop="roundName" />
          <el-table-column align="center" width="60" label="BO" prop="bo" />
          <el-table-column align="center" width="120" label="模式">
            <template v-slot="scope">
              <el-tag v-if="scope.row.battleMode === 0" size="medium"type="info">5V5</el-tag>
              <el-tag v-else-if="scope.row.battleMode === 1" size="medium" type="info">1V1</el-tag>
              <el-tag v-else-if="scope.row.battleMode === 3" size="medium" type="info">3V3</el-tag>
              <el-tag v-else size="medium" type="danger">未知</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" width="350" label="蓝队">
            <template v-slot="scope">
              <el-popover placement="right" width="300" trigger="click">
                <el-descriptions title="队伍信息" size="small" :column="1">
                  <el-descriptions-item label="节点索引">{{scope.row.team1.nodeIndex}}</el-descriptions-item>
                  <el-descriptions-item label="队伍ID">{{scope.row.team1.teamId}}</el-descriptions-item>
                  <el-descriptions-item label="队伍名称">{{scope.row.team1.teamName}}</el-descriptions-item>
                  <el-descriptions-item label="队长UID">{{scope.row.team1.uid}}</el-descriptions-item>
                  <el-descriptions-item label="补位分">{{scope.row.team1.aliveScore}}</el-descriptions-item>
                </el-descriptions>
                <el-button slot="reference" type="primary" round plain size="mini">{{scope.row.team1.teamName}}（{{scope.row.team1.score}}）</el-button>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column align="center" width="350" label="红队">
            <template v-slot="scope">
              <el-popover v-if="scope.row.team2.teamId >= 0" placement="right" width="300" trigger="click">
                <el-descriptions title="队伍信息" size="small" :column="1">
                  <el-descriptions-item label="节点索引">{{scope.row.team2.nodeIndex}}</el-descriptions-item>
                  <el-descriptions-item label="队伍ID">{{scope.row.team2.teamId}}</el-descriptions-item>
                  <el-descriptions-item label="队伍名称">{{scope.row.team2.teamName}}</el-descriptions-item>
                  <el-descriptions-item label="队长UID">{{scope.row.team2.uid}}</el-descriptions-item>
                  <el-descriptions-item label="补位分">{{scope.row.team2.aliveScore}}</el-descriptions-item>
                </el-descriptions>
                <el-button slot="reference" type="danger" round plain size="mini">{{scope.row.team2.teamName}}（{{scope.row.team2.score}}）</el-button>
              </el-popover>
              <el-button v-else type="info" round plain disabled size="mini">轮空对手（0）</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" width="150" label="比赛创建状态">
            <template v-slot="scope">
              <el-tag v-if="scope.row.gameState === 0" type="info" effect="dark">未晋级</el-tag>
              <el-tag v-else-if="scope.row.gameState === 1" type="warning" effect="dark">可创建</el-tag>
              <el-tag v-if="scope.row.gameState === 2" type="success" effect="dark">已创建</el-tag>
            </template>
          </el-table-column>
          <el-table-column align="center" width="200" label="开始时间" prop="startTime" />
          <el-table-column align="center" label="操作">
            <template v-slot="scope">
              <el-link v-if="scope.row.gameState === 1" type="primary" :underline="false" @click="createRoundGame(scope.row)">创建对局</el-link>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <el-dialog title="创建对局" :visible.sync="showCreateGameDialog" width="60%">
        <el-form :model="createGameReq" label-width="150px">
<!--          <el-input v-model="createGameReq.actId" readonly type="hidden" />-->
<!--          <el-input v-model="createGameReq.phaseId" readonly type="hidden" />-->
<!--          <el-input v-model="createGameReq.advanceNodeIndex" readonly type="hidden" />-->
          <el-row>
            <el-col :span="12">
              <el-form-item label="蓝队：">
                <span>{{ createTeam1.teamName }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="红队：">
                <span>{{ createTeam2.teamName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item label="轮次：">
                <span>{{ createGameReq.roundNum }}</span>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="名称：">
                <el-input v-model="createGameReq.roundName" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="12">
              <el-form-item label="BO：">
                <el-input-number v-model="createGameReq.bo" readonly />
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="模式：">
                <el-select v-model="createGameReq.battleMode">
                  <el-option :value="0" label="5V5">5V5</el-option>
                  <el-option :value="1" label="1V1">1V1</el-option>
                  <el-option :value="3" label="3V3">3V3</el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <el-form-item label="开始时间：">
                <el-date-picker v-model="createGameReq.startTime" type="datetime" placeholder="选择比赛开始时间" value-format="yyyy-MM-dd HH:mm:ss" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="showCreateGameDialog = false">取 消</el-button>
          <el-button type="primary" @click="doCreateRoundGame">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import ActConfigVersion from '@/api/activity/actConfigVersion'
import { queryAllPhases, queryPhaseRounds, queryRoundGames, createRoundGame } from '@/api/activity/aov'
export default {
  name: "aovMatch",
  data: () => ({
    actId: '',
    selectActList: [],
    phaseId: '',
    selectPhaseList: [],
    roundNum: '',
    selectRoundList: [],
    tableData: [],
    showCreateGameDialog: false,
    createTeam1: {},
    createTeam2: {},
    createGameReq: {
      actId: '',
      phaseId: '',
      roundNum: '',
      roundName: '',
      advanceNodeIndex: '',
      bo: '',
      battleMode: 0,
      startTime: ''
    }
  }),
  created() {
    this.loadActInfos()
  },
  methods: {
    loadActInfos() {
      const self = this
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            self.selectActList = res.data
            self.selectPhaseList = []
            self.selectRoundList = []
            const _query = self.$route.query
            if (_query && _query.actId) {
              self.actId = parseInt(_query.actId)
              self.loadAllPhases()
            }
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    loadAllPhases() {
      this.phaseId = ''
      this.roundNum = ''
      const self = this
      queryAllPhases(this.actId).then(res => {
        if (res.result === 200) {
          self.selectPhaseList = res.data
          self.selectRoundList = []
          const _query = self.$route.query
          if (_query && _query.phaseId) {
            self.phaseId = parseInt(_query.phaseId)
            self.loadAllRounds()
          }
          return
        }

        self.$message({message: '获取所有阶段失败', type: 'error'})
      })
    },
    loadAllRounds() {
      this.roundNum = ''
      const self = this
      queryPhaseRounds(this.actId, this.phaseId).then(res => {
        if (res.result === 200) {
          self.selectRoundList = res.data
          const _query = self.$route.query
          if (_query && _query.roundNum) {
            self.roundNum = parseInt(_query.roundNum)
            self.autoLoadRoundGames()
          }
          return
        }

        self.$message({message: '获取阶段轮次失败', type: 'error'})
      })
    },
    loadRoundGames() {
      if (!this.actId || !this.phaseId || !this.roundNum) {
        this.$message({message: '请先选择一个轮次', type: 'warning'})
        return
      }

      this.doLoadRoundGames(this.actId, this.phaseId, this.roundNum)
    },
    autoLoadRoundGames() {
      if (this.actId && this.phaseId && this.roundNum) {
        this.doLoadRoundGames(this.actId, this.phaseId, this.roundNum)
      }
    },
    doLoadRoundGames(actId, phaseId, roundNum) {
      const self = this
      queryRoundGames(actId, phaseId, roundNum).then(res => {
        if (res.result === 200) {
          self.tableData = res.data
          return
        }

        self.$message({message: '获取轮次比赛失败', type: 'error'})
      })
    },
    createRoundGame(row) {
      if (!this.actId || !this.phaseId || !this.roundNum) {
        this.$message({message: '请先选择一个轮次', type: 'warning'})
        return
      }

      if (row.gameState !== 1) {
        this.$message({message: '当前对局不可创建', type: 'warning'})
        return
      }

      this.createTeam1 = row.team1
      this.createTeam2 = row.team2

      this.createGameReq.actId = this.actId
      this.createGameReq.phaseId = this.phaseId
      this.createGameReq.roundNum = row.roundNum
      this.createGameReq.roundName = row.roundName
      this.createGameReq.advanceNodeIndex = row.advanceNodeIndex
      this.createGameReq.bo = row.bo
      this.createGameReq.battleMode = row.battleMode
      this.createGameReq.startTime = row.startTime

      this.showCreateGameDialog = true
    },
    doCreateRoundGame() {
      const self = this
      createRoundGame(this.createGameReq).then(res => {
        if (res.result === 200) {
          self.$message({message: '创建成功', type: 'success'})
          self.showCreateGameDialog = false
          self.loadRoundGames()
          return
        }

        self.$message({message: res.reason, type: 'error'})
      }).catch(err => {
        self.$message({message: '创建失败了', type: 'error'})
      })
    }
  }
}
</script>
<style scoped lang="scss">
.inner-table {
  padding-left: 5px;
  padding-right: 5px;
}
.team-container {
  display: flex;
  justify-content: space-around;
  align-items: flex-start;
  width: 100%;
}

.team {
  width: 45%;
  text-align: center;
}

.vs {
  font-size: 50px;
  font-weight: bold;
  align-self: center;
}
</style>
