<template>
  <div class="app-container">
    <div style="height: 800px;">

      <el-form :inline="true" :model="fromProps" label-width="90" class="demo-form-inline">
        <el-form-item label="分组">
          <el-select v-model="fromProps.groupCode" @change="queryMethodIds" style="width:100px">
            <el-option
              v-for="item in selectDataGroup"
              :key="item.code"
              :label="item.label"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="方法">
          <el-select v-model="fromProps.methodId" filterable placeholder="请选择方法" default-first-option
                     style="width:200px">
            <el-option
              v-for="item in selectMethod"
              :key="item.methodId"
              :label="item.methodShow"
              :value="item.methodId">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="fromProps.status" style="width:100px">
            <el-option
              v-for="item in selectDataStatus"
              :key="item.code"
              :label="item.label"
              :value="item.code">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="seq">
          <el-input
            v-model="fromProps.seq"
            placeholder="请输入seq"
            clearable
            size="small"
          />
        </el-form-item>

        <el-form-item label="参数">
          <el-input
            v-model="fromProps.params"
            placeholder="请输入参数"
            clearable
            size="small"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="queryMethodLog"  style="margin-left: 8px">查询</el-button>
        </el-form-item>
      </el-form>

      <div>
        <el-table
          ref="multipleTable"
          :data="page.tableData"
          border
          style="width: 100%"
          @selection-change="handleSelect">

          <el-table-column prop="groupCode" label="分组"></el-table-column>
          <el-table-column prop="methodShow" label="方法名称"></el-table-column>
          <el-table-column prop="beanName" label="实例名称"></el-table-column>
          <el-table-column prop="method" label="方法"></el-table-column>
          <el-table-column prop="seq" label="seq"></el-table-column>
          <el-table-column prop="params" label="参数"></el-table-column>
          <el-table-column prop="time" label="执行时间"></el-table-column>
          <el-table-column prop="status" :formatter="statusFormat" label="状态"></el-table-column>
          <el-table-column prop="exceptionDetail" label="异常信息">
            <template slot-scope="scope">
              <el-tooltip v-if="scope.row.exceptionDetail!==null && scope.row.exceptionDetail!==''" placement="top">
                <div slot="content">{{ scope.row.exceptionDetail }}</div>
                <el-button type="text">{{ parseExceptionDetail(scope.row.exceptionDetail) }}</el-button>
              </el-tooltip>
            </template>
          </el-table-column>

          <el-table-column  prop="retryIndex"   label="重试次数">  </el-table-column>
          <el-table-column  prop="createTime"   label="时间">  </el-table-column>
          <el-table-column   prop="operator"  label="操作人" >   </el-table-column>

          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button size="mini" type="danger" @click="retry(scope.row)">重试 </el-button>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          background
          @size-change="changePage"
          @current-change="changePage"
          :current-page.sync="page.current"
          :page-size.sync="page.pageSize"
          :page-sizes="page.pageSizeOpts"
          layout="total, prev, pager, next,sizes"
          :total="page.total"
          style="float:right;">
        </el-pagination>

      </div>

    </div>



  </div>
</template>

<script>
  import Api from '@/api/activity/methodLog'
  export default {
    name: "MethodLog",
    components: {},
    data: function () {
      return {
        fromProps: {
          groupCode: '',
          methodId: "",
          status: null,
          seq:'',
          params:'',

        },
        selectMethod: [],
        selectDataGroup: [{
          code: '1',
          label: '1分组'
        }, {
          code: '2',
          label: '2分组'
        }, {
          code: '3',
          label: '3分组'
        }, {
          code: '4',
          label: '4分组'
        }, {
          code: '5',
          label: '5分组'
        }],
        selectDataStatus: [{
          code: '0',
          label: '失败'
        }, {
          code: '1',
          label: '成功'
        }],
        tableData: [],
        page: {
          total: 0,
          current: 1,
          pageSize: 5,
          pageSizeOpts: [1, 5, 10, 15, 20, 25],
          tableData: [],
          multipleSelection: []
        }
      }
    },

    watch: {
      tableData: function (val) {
        this.page.total = this.tableData.length;
        this.page.current = 1;
        this.changePage();
      }
    },
    methods: {
      //
      queryMethodIds() {
        const self = this;
        const fromProps = this.fromProps;
        if (fromProps.groupCode === "" || fromProps.groupCode == null) {
          self.selectMethod = [];
          return;
        }
        Api.queryMethodLabel(fromProps.groupCode).then(
          res => {
            if (res.result === 200) {
              self.selectMethod = res.data;
            } else {
              self.selectMethod = [];
              alert("请求方法下拉框错误," + res.reason);
            }
          }
        ).catch((error) => {
          this.tableData = [];
          this.$Message.error(error.status + "," + error.statusText, 5);
        })
      },
      queryMethodLog() {
        const self = this;
        const fromProps = this.fromProps;
        if(!self.checkParam()){
          return
        }
        Api.queryGeMethodLogs(self.toParam(fromProps)).then(
          res => {
            if (res.result === 200) {
              self.tableData = res.data;
            } else {
              self.tableData = [];
              alert("请求方法下拉框错误," + res.reason);
            }
          }
        )
      },
      toParam(fromProps) {
        if (fromProps) {
          let params = ''
          for (const propName of Object.keys(fromProps)) {
            const value = fromProps[propName]
            const part = encodeURIComponent(propName) + '=';
            if (value !== null && typeof (value) !== 'undefined') {
              if (typeof value === 'object') {
                for (const key of Object.keys(value)) {
                  let params = propName + '[' + key + ']'
                  var subPart = encodeURIComponent(params) + '='
                  params += subPart + encodeURIComponent(value[key]) + '&'
                }
              } else {
                params += part + encodeURIComponent(value) + '&'
              }
            }
          }
          params = params.slice(0, -1)
          return params
        }
        return ''
      },
      retry(data){
        this.$confirm('此操作将停止自动重试并触发重试, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          Api.retryMethod(data).then(
            res => {
              if (res.result === 200) {
                this.$message({
                  type: 'success',
                  message: '重试成功!'
                });
              } else {
                this.$message({
                  type: 'error',
                  message: '重试失败!' + res.reason
                });
              }
            }
          )

        }).catch(() => {
        });
      },
      // 菜单状态字典翻译
      statusFormat(row, column) {
        if (row.status == "1") {
          return "成功";
        }
        return "失败";
      },
      parseExceptionDetail(message) {
        if (message === null || message === '') {
          return ''
        } else {
          return  message.slice(0, 10)+ '...'
        }
      },
      handleSizeChange(val) {
        this.page.pageSize = val
      },
      changePage() {
        const current = this.page.current;
        const pageSize = this.page.pageSize;
        const start = (current - 1) * pageSize;
        let end = (current) * pageSize;
        end = end > this.tableData.length ? this.tableData.length : end;
        this.page.tableData = this.tableData.slice(start, end);
        this.page.multipleSelection = []
      },
      handleSelect(row) {
        this.page.multipleSelection = row
        this.showCompareButton();
      },
      checkParam() {
        const fromProps = this.fromProps
        if (fromProps.groupCode === "" || fromProps.groupCode == null) {
          this.$Message.warning("请选择分组")
          return false;
        }
        return true
      }
    }
  };
</script>
