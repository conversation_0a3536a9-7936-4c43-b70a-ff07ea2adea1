<template>
  <div class="app-container">
    <div>
      <el-row>
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="活动id">
            <activity-selector v-model="searchForm.actId" style="width:180px" />
          </el-form-item>

          <el-form-item label="备份类型">
            <el-select v-model="searchForm.backupType" placeholder="备份类型" style="width:150px" clearable>
              <el-option v-for="item in backupTypeList" :value="item.code" :key="item.code"
                         :label='item.desc+"("+item.code+")"'>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="数据源">
            <el-select v-model="searchForm.backupSource" placeholder="备份数据源"
                       filterable style="width:150px" clearable>
              <el-option v-for="item in backupSourceList" :value="item.code" :key="item.code"
                         :label='item.desc+"("+item.code+")"'>
              </el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" :loading="loadingActBackupConfig" @click="onSearch(true)" icon="el-icon-search"
                     size="medium">
            查询
          </el-button>
          <el-button type="info" :loading="exportAwardIssueLog" @click="onClickAddConfig" size="medium">
            添加
          </el-button>
          <el-tooltip class="item" effect="light" content="操作表:backup_activity_config" placement="right-start"
                      style="margin-left: 10px;">
            <i class="el-icon-question"></i>
          </el-tooltip>
        </el-form>
      </el-row>
      <el-row>
        <el-table :data="tableData" border v-loading="loadingActBackupConfig">
          <el-table-column prop="id" label="唯一标识" align="center" width="90"></el-table-column>
          <el-table-column prop="actId" label="活动ID" align="center" width="110"></el-table-column>
          <el-table-column prop="backupType" label="类型" align="center" width="60"></el-table-column>
          <el-table-column prop="uri" label="事件URI" align="center" width="70"></el-table-column>
          <el-table-column prop="rankId" label="榜单ID" align="center" width="70"></el-table-column>
          <el-table-column prop="phaseId" label="阶段ID" align="center" width="70"></el-table-column>
          <el-table-column prop="backupTime" label="备份时间点" align="center" width="120"></el-table-column>
          <el-table-column prop="backupSource" label="备份数据源" align="center" width="100"></el-table-column>
          <el-table-column label="键Pattern" align="center" width="250">
            <template slot-scope="scope">{{JSON.stringify(scope.row.keyPatterns)}}</template>
          </el-table-column>
          <el-table-column label="db" align="center" width="150">
            <template slot-scope="scope">{{JSON.stringify(scope.row.databases)}}</template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" align="center" width="160"></el-table-column>
          <el-table-column prop="updateTime" label="更新时间" align="center" width="160"></el-table-column>
          <el-table-column label="操作" fixed="right" align="center">
            <template slot-scope="scope">
              <el-link type="warning" @click="onClickBackup(scope.row)">备份</el-link>
              <el-link type="success" @click="onClickEditConfig(scope.row)" style="margin-left: 10px;">编辑</el-link>
              <el-link type="primary" @click="onClickDelConfig(scope.row)" style="margin-left: 10px;">删除</el-link>
              <el-link type="primary" @click="goBackupRecord(scope.row)" style="margin-left: 10px;">备份记录</el-link>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :page-sizes="[10,15,20,50,100]"
          @size-change="onSearch(false)"
          @current-change="onSearch(false)"
          layout="->, total, prev, pager, next, sizes"
          :current-page.sync="pageData.page"
          :page-size.sync="pageData.size"
          :total="pageData.total">
        </el-pagination>
      </el-row>
    </div>

    <el-dialog title="新增备份配置" :visible.sync="addBackupConfigVisible"
               :close-on-click-modal="false">
      <el-form :model="configFormData" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="活动id">
              <activity-selector v-model="configFormData.actId" @change="onChangeActId" />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="备份类型">
              <el-select v-model="configFormData.backupType" placeholder="备份类型">
                <el-option v-for="item in backupTypeList" :value="item.code" :key="item.code"
                           :label='item.desc+"("+item.code+")"'>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="数据源">
              <el-select v-model="configFormData.backupSource" placeholder="备份数据源" @change="onChangeBackupSource">
                <el-option v-for="item in backupSourceList" :value="item.code" :key="item.code" :label='item.desc+"("+item.code+")"'>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="configFormData.backupType === 'event'">
            <el-form-item label="事件URI">
              <el-select v-model="configFormData.uri" placeholder="事件URI">
                <el-option v-for="item in eventUriList" :value="item.code" :key="item.code" :label='item.desc+"("+item.code+")"'>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="configFormData.backupType === 'point'">
            <el-form-item label="备份时间">
              <el-input-number v-model="configFormData.backupTime"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="configFormData.backupType === 'event'">
          <el-col :span="12">
            <el-form-item label="榜单ID">
              <el-input-number v-model="configFormData.rankId"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="阶段ID">
              <el-input-number v-model="configFormData.phaseId"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item v-for="(keyPattern, index) in configFormData.keyPatterns"
                          :label="'键Pattern' + index" :key="keyPattern">
              <el-input v-model="configFormData.keyPatterns[index]">
                <el-button slot="append" icon="el-icon-delete" @click="configFormData.keyPatterns.length > 1 && configFormData.keyPatterns.splice(index, 1)"></el-button>
                <el-button slot="append" icon="el-icon-plus" @click="configFormData.keyPatterns.push('')"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="20">
            <el-form-item v-for="(database, index) in configFormData.databases"
                          :label="'数据库' + index" :key="database">
              <el-input v-model="configFormData.databases[index]">
                <el-button slot="append" icon="el-icon-delete" @click="configFormData.databases.length > 1 && configFormData.databases.splice(index, 1)"></el-button>
                <el-button slot="append" icon="el-icon-plus" @click="configFormData.databases.push('')"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="addBackupConfigVisible = false">取 消</el-button>
        <el-button type="primary" @click="onAddBackupConfig">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="更新备份配置" :visible.sync="updateBackupConfigVisible"
               :close-on-click-modal="false">
      <el-form :model="configFormData" label-width="150px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="活动id">
              <el-input v-model="configFormData.actId" readonly />
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="备份类型">
              <el-select v-model="configFormData.backupType" placeholder="备份类型" :disabled="true">
                <el-option v-for="item in backupTypeList" :value="item.code" :key="item.code"
                           :label='item.desc+"("+item.code+")"'>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="数据源">
              <el-select v-model="configFormData.backupSource" placeholder="备份数据源" :disabled="true">
                <el-option v-for="item in backupSourceList" :value="item.code" :key="item.code"
                           :label='item.desc+"("+item.code+")"'>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="configFormData.backupType === 'event'">
            <el-form-item label="事件URI">
              <el-select v-model="configFormData.uri" placeholder="事件URI">
                <el-option v-for="item in eventUriList" :value="item.code" :key="item.code" :label='item.desc+"("+item.code+")"'>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="configFormData.backupType === 'point'">
            <el-form-item label="备份时间">
              <el-input-number v-model="configFormData.backupTime"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="configFormData.backupType === 'event'">
          <el-col :span="12">
            <el-form-item label="榜单ID">
              <el-input-number v-model="configFormData.rankId"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="阶段ID">
              <el-input-number v-model="configFormData.phaseId"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="20">
            <el-form-item v-for="(keyPattern, index) in configFormData.keyPatterns"
                          :label="'键Pattern' + index" :key="keyPattern">
              <el-input v-model="configFormData.keyPatterns[index]">
                <el-button slot="append" icon="el-icon-delete" @click="configFormData.keyPatterns.length > 1 && configFormData.keyPatterns.splice(index, 1)"></el-button>
                <el-button slot="append" icon="el-icon-plus" @click="configFormData.keyPatterns.push('')"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="20">
            <el-form-item v-for="(database, index) in configFormData.databases"
                          :label="'数据库' + index" :key="database">
              <el-input v-model="configFormData.databases[index]">
                <el-button slot="append" icon="el-icon-delete" @click="configFormData.databases.length > 1 && configFormData.databases.splice(index, 1)"></el-button>
                <el-button slot="append" icon="el-icon-plus" @click="configFormData.databases.push('')"></el-button>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="updateBackupConfigVisible = false">取 消</el-button>
        <el-button type="primary" @click="onUpdateBackupConfig">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="备份" :visible.sync="showBackupSourcePropVisible" :close-on-click-modal="false">
      <el-descriptions class="margin-top" title="数据源信息如下，是否确认执行备份？" :column="2" border="true">
        <el-descriptions-item label="活动ID">{{dataSourceProp.actId}}</el-descriptions-item>
        <el-descriptions-item label="类型">{{dataSourceProp.sourceType}}</el-descriptions-item>
        <el-descriptions-item label="分组">{{dataSourceProp.groupCode}}</el-descriptions-item>
        <el-descriptions-item label="描述">{{dataSourceProp.name}}</el-descriptions-item>
        <el-descriptions-item label="URI">{{dataSourceProp.sourceUri}}</el-descriptions-item>

        <template slot="extra">
          <el-button @click="showBackupSourcePropVisible = false">取 消</el-button>
          <el-button type="primary" @click="onBackup()" :disabled="backuping">备 份</el-button>
        </template>
      </el-descriptions>

      <div slot="footer" class="dialog-footer" v-if="backuping || backuped">
        <i v-if="backuping" class="el-icon-loading"></i>
        <el-tag type="danger">{{backupResult}}</el-tag>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import API from '@/api/activity/actRedisBackup'
import ActConfigVersion from '@/api/activity/actConfigVersion'
import ActivitySelector from "@/components/ActivitySelector"

export default {
  name: "ActRedisBackup",
  components: { ActivitySelector },
  data: function () {
    return {
      loadingActBackupConfig: false,
      exportAwardIssueLog: false,
      addBackupConfigVisible: false,
      updateBackupConfigVisible: false,
      showBackupSourcePropVisible: false,
      backuping: false,
      backuped: false,
      backupResult: '',
      selectActList: [],
      backupTypeList: [
        {code: 'event', desc: '活动事件'},
        {code: 'point', desc: '时间点'}
      ],
      backupSourceList: [
        {code: 'ZTRanking', desc: '中台榜单'},
        {code: 'ZTAward', desc: '中台抽发奖'},
        {code: 'Ecology', desc: '中控Ecology'},
        {code: 'Stream', desc: '中控Stream'}
      ],
      eventUriList: [
        {code: '1001', desc: '活动开始'},
        {code: '1002', desc: '活动结束'},
        {code: '1003', desc: '计榜开始'},
        {code: '1004', desc: '计榜结束'},
        {code: '1005', desc: '榜单阶段开始'},
        {code: '1006', desc: '榜单阶段结束'},
        {code: '1007', desc: 'PK结算结束'},
        {code: '1008', desc: '晋级结算结束'}
      ],
      statusList: [],
      searchForm: {
        actId: '',
        backupType: '',
        backupSource: '',
        status: ''
      },
      configFormData: {
        id: '',
        actId: '',
        backupType: '',
        backupSource: '',
        uri: '',
        rankId: '',
        phaseId: '',
        backupTime: '',
        keyPatterns: [''],
        databases: ['']
      },
      dataSourceProp: {
        configId: '',
        actId: '',
        groupCode: '',
        name: '',
        sourceType: '',
        sourceUri: ''
      },
      backupResultTimer: null,
      pageData: {
        page: 1,
        size: 10,
        total: 0,
      },
      tableData: []
    }
  },
  created: function () {
    this.loadActInfos()
  },
  methods: {
    onSearch(fromBtn) {
      const req = this.searchForm
      if (req.actId === '' || isNaN(req.actId)) {
        this.$Message.warning("请选择要查询的活动")
        return
      }
      const self = this
      if (fromBtn) {
        self.pageData.page = 1
      }
      req.pageNo = self.pageData.page
      req.pageSize = self.pageData.size
      self.loadingActBackupConfig = true
      API.queryActBackupConfigs(req)
        .then(res => {
          if (res.code == 200) {
            self.pageData.total = res.total
            self.tableData = res.data
          } else {
            self.$message({message: '获取列表数据失败:' + res.reason, type: 'warning'})
          }
          self.loadingActBackupConfig = false
        })
        .catch(error => {
          self.loadingActBackupConfig = false
          console.log(error)
        })
    },
    onClickAddConfig() {
      this.configFormData = {
        actId: '',
        backupType: '',
        backupSource: '',
        uri: '',
        rankId: '',
        phaseId: '',
        backupTime: '',
        keyPatterns: [''],
        databases: ['']
      }

      this.addBackupConfigVisible = true
    },
    onClickBackup(config) {
      const self = this
      console.log(config.actId, config.backupSource)
      API.queryActSourceProp(config.id)
        .then(res => {
          if (res.result == 200) {
            self.dataSourceProp = res.data
            self.dataSourceProp.actId = config.actId
            self.dataSourceProp.configId = config.id
            self.backuping = false
            self.backuped = false
            self.backupResult = ''
            self.showBackupSourcePropVisible = true
          } else {
            self.$message({message: '获取数据源信息失败:' + res.reason, type: 'warning'})
          }
        }).catch(error => {
        console.log(error)
      })
    },
    onClickEditConfig(config) {
      this.configFormData = {
        id: config.id,
        actId: config.actId,
        backupType: config.backupType,
        backupSource: config.backupSource,
        uri: config.uri,
        rankId: config.rankId,
        phaseId: config.phaseId,
        backupTime: config.backupTime,
        keyPatterns: config.keyPatterns && config.keyPatterns.slice(),
        databases: config.databases && config.databases.slice()
      }

      this.configFormData.keyPatterns.length || this.configFormData.keyPatterns.push('')
      this.configFormData.databases.length || this.configFormData.databases.push('')

      this.updateBackupConfigVisible = true;
    },
    onClickDelConfig(config) {
      const self = this
      this.$confirm('删除备份配置:' + config.id + '？', '提示', {type: 'warning'})
        .then(() => {
          API.deleteActBackupConfig(config.id)
            .then(res => {
              if (res.result == 200) {
                self.searchForm.actId = config.actId
                self.onSearch(false)
                self.$message({message: '删除成功', type: 'success'})
              } else {
                self.$message({message: '删除失败了:' + res.reason, type: 'warning'})
              }
            }).catch(error => {
              console.log(error)
          })
        }).catch(() => {
        self.$message({message: '删除已取消', type: 'warning'})
      })
    },
    //加载活动数据
    onChangeActId(val) {
      if (this.configFormData) {
        if (this.configFormData.backupSource == 'ZTRanking') {
          this.configFormData.keyPatterns = ['act:' + val + ':.*', 'hdzt_ranking:'+ val + ':.*']
        } else if (this.configFormData.backupSource == 'Ecology') {
          this.configFormData.keyPatterns = ['act:' + val + ':.*']
        }
      }
    },
    onChangeBackupSource(val) {
      if (this.configFormData && this.configFormData.actId) {
        if (val == 'ZTRanking') {
          this.configFormData.keyPatterns = ['act:' + this.configFormData.actId + ':.*', 'hdzt_ranking:'+ this.configFormData.actId + ':.*']
        } else if (val == 'Ecology') {
          this.configFormData.keyPatterns = ['act:' + this.configFormData.actId + ':.*']
        }
      }
    },
    loadActInfos() {
      ActConfigVersion.loadActInfos().then(
        res => {
          if (res.result === 200) {
            this.selectActList = res.data
          } else {
            alert("拉取活动列表错误," + res.reason)
          }
        }
      )
    },
    onAddBackupConfig() {
      if (!this.configFormData.actId) {
        alert('活动ID不能为空！')
        return
      }
      this.configFormData.keyPatterns = this.configFormData.keyPatterns.filter(keyPattern => keyPattern.trim() !== '')
      this.configFormData.databases = this.configFormData.databases.filter(database => database.trim() !== '')
      const self = this
      API.addActBackupConfig(this.configFormData)
        .then(res => {
          if (res.result == 200) {
            self.searchForm.actId = self.configFormData.actId
            self.onSearch(false)
            self.addBackupConfigVisible = false
            self.$message({message: '添加成功', type: 'success'})
          } else {
            self.$message({message: '添加失败了:' + res.reason, type: 'warning'})
          }
        }).catch(error => {
        console.log(error)
      })
    },
    onUpdateBackupConfig() {
      this.configFormData.keyPatterns = this.configFormData.keyPatterns.filter(keyPattern => keyPattern.trim() !== '')
      this.configFormData.databases = this.configFormData.databases.filter(database => database.trim() !== '')
      const self = this
      API.updateActBackupConfig(this.configFormData)
        .then(res => {
          console.log(res)
          if (res.result == 200) {
            self.searchForm.actId = self.configFormData.actId
            self.onSearch(false)
            self.updateBackupConfigVisible = false
            self.$message({message: '修改成功', type: 'success'})
          } else {
            self.$message({message: '修改失败了:' + res.reason, type: 'warning'})
          }
        }).catch(error => {
        console.log(error)
      })
    },
    onBackup() {
      const self = this
      API.backupActSource(this.dataSourceProp.configId)
        .then(res => {
          if (res.result == 200) {
            self.backuping = true
            self.backupResult = '备份执行中...'
            self.backupResultTimer = setInterval(() => {
              self.refreshBackupResult(res.data)
            }, 3000)
          } else {
            self.$message({message: '备份请求失败:' + res.reason, type: 'warning'})
          }
        }).catch(error => {
          console.log(error)
      })
    },
    refreshBackupResult(recordId) {
      const self = this
      API.queryBackupResult(recordId)
        .then(res => {
          if (res.result == 200) {
            const backupState = res.data ? res.data.backupState : 'trying';
            if (backupState == 'trying') {
              self.backupResult = '备份执行中...'
            } else {
              if (backupState == 'completed') {
                self.backupResult = '备份成功 => 节点：' + res.data.sourceAddress + ', 路径：' + res.data.path + ', 文件：' + res.data.key + ', 大小:' + API.formatFileSize(res.data.fileSize)
              } else {
                self.backupResult = '备份失败 => ' + res.data.failMsg
              }
              self.backuping = false
              self.backuped = true
              clearInterval(self.backupResultTimer)
            }
          } else {
            console.log(res)
          }
        }).catch(error => {
        console.log(error)
      })
    },
    goBackupRecord(config) {
      this.$router.push({path: 'actRedisBackupRecord', query: {actId:config.actId, configId: config.id}})
    }
  }
};
</script>
