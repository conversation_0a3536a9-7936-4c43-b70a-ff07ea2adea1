<template>
  <div class="app-container">
    <div>
      <div v-for="(item,idx) in children" :key="idx">
        <Form v-if="item.type==='Form'" inline>
          <FormItem v-for="(form,idx) in item.children" :key="idx">
            <Select ></Select>
          </FormItem>
        </Form>
      </div>
    </div>

  </div>
</template>

<script>
  import Api from '@/api/activity/actConfigVersion'
  export default {
    name: "",
    components: {},
    data: function () {
      return {
        children: [
          {
            type: 'Form',
            children: [
              {
                type: 'Select',
                children: [
                  {
                    value: '0',
                    name: '已停用'
                  }, {
                    value: '1',
                    name: '生效中'
                  }, {
                    value: '2',
                    name: '全部'
                  }
                ]
              }, {
                type: 'DatePicker',
                format: 'yyyy-MM-dd HH:mm:ss',
                placeholder: '选择生效时间',
                value: 'effectiveTime'
              }
            ]
          }
        ]
      }
      }
  };
</script>
