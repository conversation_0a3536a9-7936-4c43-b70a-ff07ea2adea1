<template>
  <div class="app-container">
    <div>
      <Form inline>
        <FormItem>
          <span>状态查询：</span>
          <Select v-model="status" style="width:200px">
            <Option value="0">已停用</Option>
            <Option value="1">生效中</Option>
            <Option value="2">全部</Option>
          </Select>
        </FormItem>
        <FormItem>
          <Date-picker format="yyyy-MM-dd HH:mm:ss" type="datetime" v-model="effectiveTime"  placeholder="选择生效时间" style="width: 180px"></Date-picker>
        </FormItem>
        <FormItem>
          <Button type="primary" @click="pageChange(1)">查询</Button>
          <Button type="primary" @click="openModal(1)" style="margin-right:10px">新增</Button>
        </FormItem>
        <FormItem>
          <span>banner位置：</span>
          <Select style="width:200px" v-model="searchbannerKey">
            <Option v-for="(item,i) in bannerkeyList" :value="item.bannerKey" :key="i">{{item.bannerDesc}}</Option>
          </Select>
        </FormItem>
      </Form>


      <Modal :value="addLayerState" width="600" :closable="false" :mask-closable="false">
        <p slot="header" style="text-align:center">
          <span v-if="opt_type===1">添加banner</span>
          <span v-if="opt_type===2">编辑banner</span>
        </p>
        <Form :label-width="100">
          <FormItem label="类型" v-if="opt_type===2">
            <span>{{formItem.bannerDesc}}</span>
          </FormItem>
          <FormItem label="选择类型" v-else-if="opt_type===1">
            <RadioGroup v-model="formItem.bannerKey">
              <Radio v-for="item in bannerkeyList" :label="item.bannerKey" :key="item.bannerKey" :value="item.bannerKey"> <span>{{item.bannerDesc}}</span></Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="选择客户端">
            <div>
              <RadioGroup v-model="formItem.pf">
                <Radio label="1">Android</Radio>
                <Radio label="2">iOS</Radio>
                <Radio label="3">iOS&Android</Radio>
              </RadioGroup>
              <span style="color:red">如无需区分客户端请选择“iOS&Android”</span>
            </div>
          </FormItem>
          <FormItem  v-if="formItem.bannerKey!=='GAME_TAB_BANNER_KEY'" label="banner展示类型" @on-change='change'>
            <RadioGroup v-model="formItem.bannerType">
              <Radio label="1">通栏</Radio>
              <Radio label="0">普通</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem  v-else label="banner展示类型" @on-change='change'>
            <RadioGroup v-model="formItem.bannerType">
              <Radio label="1">通栏</Radio>
            </RadioGroup>
          </FormItem>
          <FormItem label="banner标题展示">
            <RadioGroup v-model="formItem.titleShow" @on-change='change'>
              <Radio label="0">否</Radio>
              <Radio label="1">是</Radio>
            </RadioGroup>
          </FormItem>
          <div>
            <FormItem label="标题" v-if="formItem.titleShow==='1'">
              <Input v-model="formItem.title" placeholder="字符数上限：8" style="width: 300px" :maxlength=8></Input>
            </FormItem>
            <FormItem label="副标题" v-if="formItem.titleShow==='1'">
              <Input v-model="formItem.subtitle" placeholder="字符数上限：16" style="width: 300px" :maxlength=16></Input>
            </FormItem>
            <FormItem label="描述" v-if="formItem.titleShow==='0'">
              <Input v-model="formItem.desc" placeholder="请输入描述内容" style="width: 300px"></Input>
            </FormItem>
          </div>
          <FormItem label="banner按钮展示">
            <RadioGroup v-model="formItem.btnShow" @on-change='change'>
              <Radio label="0">否</Radio>
              <Radio label="1">是</Radio>
            </RadioGroup>
          </FormItem>
          <div v-if="formItem.btnShow==='1'">
            <FormItem label="按钮文案">
              <Input v-model="formItem.buttonText" placeholder="字符数上限：4" style="width: 300px" :maxlength=4></Input>
            </FormItem>
            <FormItem label="按钮跳转链接">
              <Input v-model="formItem.buttonLink" style="width: 300px"></Input>
            </FormItem>
          </div>
          <FormItem label="gameId" v-if="formItem.bannerKey==='QUEUING_BANNER_KEY'||formItem.bannerKey==='GAME_OVER_BANNER_KEY'">
            <Input v-model="formItem.gameId" placeholder="请输入gameId" style="width: 300px"></Input>
          </FormItem>
          <FormItem label="展示位置" v-if="formItem.bannerKey==='HOMEPAGE_CHANNEL_FLOW'">
            <Input v-model="formItem.params" placeholder="请输入数字" style="width: 300px"></Input>
          </FormItem>
          <FormItem label="渠道号">
            <div>
              <RadioGroup v-model="formItem.marketStatus" v-if="formItem.bannerKey!=='HIGHLY_RECOMMEND_BANNER_KEY'">
                <Radio label="1">全部</Radio>
                <Radio label="0">部分</Radio>
              </RadioGroup>
              <div v-else>无需填写</div>
              <Input v-if="+formItem.marketStatus===0" v-model="formItem.marketInfo" placeholder="请填入渠道号，多个用英文逗号隔开" style="width: 300px"></Input>
            </div>
          </FormItem>
          <FormItem :label="formItem.bannerType==='1' ? 'banner图片' : '添加图片'">
            <div>
              <RadioGroup v-model="imgFormat">
                <Radio label="png">png</Radio>
                <Radio label="jpg">jpg</Radio>
                <Radio label="gif">gif</Radio>
              </RadioGroup>
            </div>
            <ImageUpload field="file" @choose-success="cropSuccess" @upload-success="cropUploadSuccess" @upload-fail="cropUploadFail" v-model="show"
                       :width="380" :height="160" :maxSize="300" url="//peipei.yy.com/web/file/ambiguous/uploadFile"
                       :img-format="imgFormat"
            ></ImageUpload>
            <div class="imgBox" style="height: 200px">
              <Spin fix v-if="uploadConf.spinShow">
                <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
                <div>上传中</div>
              </Spin>
              <img :src="formItem.bannerPic||'//iph.href.lu/450x300?text=未设置图标'" alt="">
              <div class="uploadBtn" @click="toggleShow">点击修改图片</div>
            </div>
          </FormItem>
          <FormItem label="跳转链接">
            <Input v-model="formItem.bannerLink" placeholder="链接"  style="width: 400px"/>
            <p v-if="formItem.bannerKey!=='HIGHLY_RECOMMEND_BANNER_KEY'">
              <span class="color:red">Android配置房间跳转格式:(替换sid与subid)</span>
              </br>zhuiya://channel/live/type/< sid >/< subsid >
              </br>配置url：需加zhuiya://browser/ 访问网址；
              </br>访问网址需要url编码（转码地址 <a href="https://tool.chinaz.com/tools/urlencode.aspx" target="_blank">https://tool.chinaz.com/tools/urlencode.aspx</a>）
              <!-- </br>(IOS配置频道跳转格式：channel/live/0/sid/ssid)-->
            </p>
            <p v-else>
              <span class="color:red">PC配置房间跳转格式:(替换sid与subid)</span>
              <br/>yy://pd-[sid=38688233&subid=38688233]
              <br/>配置url：需加https:// 前缀
            </p>
          </FormItem>

          <FormItem label="按钮图片" v-if="formItem.bannerType==='1'&&formItem.btnShow==='1'">
            <div>
              <RadioGroup v-model="btnImgFormat">
                <Radio label="png">png</Radio>
                <Radio label="jpg">jpg</Radio>
                <Radio label="gif">gif</Radio>
              </RadioGroup>
            </div>
            <ImageUpload field="file" @choose-success="btnCropSuccess" @upload-success="btnCropUploadSuccess" @upload-fail="btnCropUploadFail" v-model="btnShow"
                       :width="380" :height="160" :maxSize="300" url="//peipei.yy.com/web/file/ambiguous/uploadFile"
                       :img-format="btnImgFormat"
            ></ImageUpload>
            <div class="imgBox" style="height: 200px">
              <Spin fix v-if="uploadConf.btnSpinShow">
                <Icon type="load-c" size=18 class="demo-spin-icon-load"></Icon>
                <div>上传中</div>
              </Spin>
              <img :src="formItem.buttonPic||'//iph.href.lu/450x300?text=未设置图标'" alt="">
              <div class="uploadBtn" @click="btnShow=!btnShow">点击修改图片</div>
            </div>
          </FormItem>

          <!-- <FormItem label="参数">
            &lt;!&ndash; <Input v-model="formItem.params" placeholder="速配滑动x张会出现"  style="width: 200px"/>&ndash;&gt;
             <Input v-model="formItem.params" placeholder="参数"  style="width: 200px"/>
           </FormItem>-->
          <FormItem label="排序">
            <InputNumber v-model="formItem.sort" placeholder="排序权重"  style="width: 200px"/>
          </FormItem>
          <FormItem label="生效时间">
            <Date-picker format="yyyy-MM-dd HH:mm:ss" type="datetimerange" v-model="dateInterval" placeholder="选择日期区间" style="width: 300px"></Date-picker>
          </FormItem>
        </Form>
        <div slot="footer">
          <Button :loading="loadingState" type="primary" @click="loadingState=true;addbanner()" v-if="opt_type==1">
            确认添加
          </Button>
          <Button :loading="loadingState" type="primary" @click="loadingState=true;updatebanner()" v-if="opt_type==2">
            确认修改
          </Button>
          <Button  @click="closeModal()">
            取消
          </Button>
        </div>
      </Modal>
      <Table :context="self" :columns="columns" :data="data"></Table>
      <br>
      <Page :current="curPage" :total="parseInt(total)" :page-size="pageSize" @on-change="pageChange"></Page>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/actConfigVersion'
  import { parseTime } from "@/utils/systemUtils";
  import { TestNum, httpFilter } from '@/utils/util'
  export default {
    name: "Banner",
    components: {},
    data () {
      return {
        self: this,
        data: [],
        columns: [{
          title: 'id',
          width: 80,
          key: 'bannerId'
        }, {
          title: '类型',
          key: 'bannerDesc'
        }, {
          title: '图片',
          key: 'bannerPic',
          width: 250,
          align: 'center',
          render: (h, params) => {
            let pic = params.row.bannerPic || '//iph.href.lu/300x120?text=未设置图标'
            return h('img', {
              attrs: {
                src: httpFilter(pic),
                width: '200px'
              }
            })
          }
        }, {
          title: '添加时间',
          key: 'createTime',
          render: (h, params) => {
            return h('div', [
              h('p', `${params.row.createTime}`)
            ])
          }
        }, {
          title: '有效期',
          key: 'beginTime',
          render: (h, params) => {
            return h('div', [
              h('p', `${params.row.beginTime}-`),
              h('p', params.row.endTime)

            ])
          }
        }, {
          title: '排序权重',
          key: 'sort',
          align: 'center',
          width: 100
        }, {
          title: '链接',
          key: 'bannerLink'
        }, {
          title: '标题',
          key: 'title',
          render: (h, params) => {
            return h('p', params.row.title)
          }
        }, {
          title: 'gameId',
          key: 'gameId',
          render: (h, params) => {
            return h('p', params.row.gameId || null)
          }
        }, {
          title: '客户端',
          key: 'pf',
          render: (h, params) => {
            let content
            switch (params.row.pf) {
              case 1:
                content = 'Android'
                break
              case 2:
                content = 'iOS'
                break
              case 3:
                content = 'iOS&Android'
                break
            }
            return h('p', content)
          }
        },
          {
            title: '状态',
            key: 'status',
            render: (h, params) => {
              let status
              if (params.row.status === 0) {
                status = '已停用'
              } else if (params.row.status === 1) {
                status = '生效中'
              } else if (params.row.status === 2) {
                status = '未生效'
              }
              return h('span', status)
            }
          }, {
            title: '展示位置',
            key: 'params'
          }, {
            title: '描述',
            key: 'bannerDesc'
          }, {
            title: '渠道',
            render: (h, params) => {
              return h('p', params.row.marketStatus === 1 ? '全部' : '部分')
            }
          }, {
            title: '操作',
            align: 'center',
            width: 180,
            render: (h, params) => {
              let update = h('Button', {
                style: {
                  marginLeft: '5px'
                },
                props: {
                  type: 'primary'
                },
                on: {
                  click: () => {
                    this.updateId = params.row.bannerId
                    this.editItem(params.index)
                  }
                }
              }, '编辑')
              let stop = h('Button', {
                style: {
                  marginLeft: '5px'
                },
                props: {
                  type: 'primary'
                },
                on: {
                  click: () => {
                    this.offbanner(params.row.bannerId)
                  }
                }
              }, '停用')
              let enable = h('Button', {
                style: {
                  marginLeft: '5px'
                },
                props: {
                  type: 'primary'
                },
                on: {
                  click: () => {
                    this.enablebanner(params.row.bannerId)
                  }
                }
              }, '启用')
              if (params.row.status === 0) {
                /* return h('p', '无需操作') */
                return h('p', [update, enable])
              } else if (params.row.status === 1) {
                return h('p', [update, stop])
              } else if (params.row.status === 2) {
                return h('p', [update, stop])
              }
            }
          }],
        dateInterval: '',
        bannerkeyList: [],
        searchbannerKey: 'APP_GAME_BANNER_KEY',
        curPage: 1,
        pageSize: 10,
        total: 0,
        // eslint-disable-next-line no-tabs
        status: '1', //	0：已停用，1：生效中,2:全部
        effectiveTime: '',
        formItem: {
          sort: null,
          marketStatus: '1',
          titleShow: '1',
          btnShow: '1',
          bannerType: '1'
        },
        opt_type: 1,
        addLayerState: false,
        loadingState: false,
        uploadConf: {
          spinShow: false,
          btnSpinShow: false
        },
        params: {
          name: 'file'
        },
        show: false,
        btnShow: false,
        updateId: 0,
        imgFormat: 'png',
        btnImgFormat: 'png'
      }
    },
    mounted () {
      this.$nextTick(() => {
        const self = this
        self.pageChange(1)
        self.bannerkey()
      })
    },
    watch: {
      searchbannerKey () {
        this.pageChange(1)
      }
    },
    methods: {
      change () {
        this.$forceUpdate()
      },
      pageChange: function (i) {
        const self = this
        self.curPage = i
        self.listbanner()
      },
      listbanner () {
        const self = this
        const effectiveTime = parseTime(self.effectiveTime, '{y}-{m}-{d} {h}:{i}:{s}');
        let reqData = {
          page: self.curPage,
          pageSize: self.pageSize
        }
        if (effectiveTime) {
          reqData.effectiveTime = effectiveTime
        }
        if (+self.status !== 2) {
          reqData.status = self.status
        }
        if (self.searchbannerKey) {
          reqData.bannerKey = self.searchbannerKey
        }
        Api.listbanner(reqData).then(res => {
          if (res.result === 0) {
            self.data = res.datas
            self.total = res.total
          } else {
            self.$Message.warning(res.msg)
          }
        })
      },
      // 获取所有bannerKey
      bannerkey () {
        const self = this
        Api.bannerkey().then(res => {
          if (res.result === 0) {
            self.bannerkeyList = res.data
          } else {
            self.$Message.warning(res.msg)
          }
        })
      },
      openModal (type) {
        this.addLayerState = true
        this.opt_type = type
        if (type === 1) {
          this.formItem = {
            sort: null,
            marketStatus: '1'
          }
          this.dateInterval = ''
        }
      },
      closeModal () {
        this.addLayerState = false
        this.loadingState = false
      },
      // 添加banner
      addbanner () {
        const self = this
        const formItem = self.formItem
        let reqData = {
          bannerKey: formItem.bannerKey,
          bannerPic: formItem.bannerPic,
          bannerLink: formItem.bannerLink,
          beginTime: parseTime(self.dateInterval[0], '{y}-{m}-{d} {h}:{i}:{s}'),
          endTime: parseTime(self.dateInterval[1], '{y}-{m}-{d} {h}:{i}:{s}'),
          sort: formItem.sort || 0,
          gameId: formItem.gameId || 0,
          pf: +formItem.pf,
          title: formItem.title || '',
          desc: formItem.desc || '',
          marketStatus: +formItem.marketStatus,
          bannerType: +formItem.bannerType,
          buttonPic: formItem.buttonPic || '',
          buttonText: formItem.buttonText || '',
          buttonLink: formItem.buttonLink || '',
          subtitle: formItem.subtitle || ''
        }
        if (formItem.bannerKey === 'GAME_TAB_BANNER_KEY') {
          reqData.bannerType = '1'
        }
        if (formItem.btnShow === '0') {
          reqData.buttonText = ''
          reqData.buttonLink = ''
          reqData.buttonPic = ''
        }
        if (formItem.titleShow === '0') {
          reqData.subtitle = ''
          reqData.title = ''
        }
        if (!reqData.bannerKey || !reqData.bannerPic || !reqData.bannerLink || !reqData.beginTime || !formItem.pf) {
          self.loadingState = false
          self.$Message.warning('请将表单填写完整再提交')
          return false
        }
        if (+formItem.btnShow && formItem.buttonLink === '') {
          self.loadingState = false
          self.$Message.warning('请提写按钮跳转链接')
          return false
        }
        if (!TestNum(reqData.sort)) {
          self.loadingState = false
          self.$Message.warning('权重格式有误')
          return false
        }
        if (formItem.bannerKey !== 'QUEUING_BANNER_KEY' && formItem.bannerKey !== 'GAME_OVER_BANNER_KEY') {
          reqData.gameId = 0
        }
        if (formItem.bannerKey === 'HOMEPAGE_CHANNEL_FLOW') {
          if (!formItem.params) {
            self.loadingState = false
            self.$Message.warning('请将表单填写完整再提交')
            return false
          }
          reqData.params = +formItem.params
        }
        if (formItem.bannerKey === 'HIGHLY_RECOMMEND_BANNER_KEY') {
          reqData.marketStatus = 1
        }
        if (reqData.marketStatus === 0) {
          reqData.marketInfo = formItem.marketInfo
        }
        reqData.bannerLink = reqData.bannerLink.replace(/ /g, '')
        reqData.buttonLink = reqData.buttonLink.replace(/ /g, '')

        Api.addbanner(reqData).then(res => {
          self.loadingState = false
          if (res.result === 0) {
            self.$Message.success('添加成功')
            self.searchbannerKey = formItem.bannerKey
            self.pageChange(1)
            self.addLayerState = false
          } else {
            self.$Message.warning(res.msg)
          }
        })
      },
      // 编辑banner
      updatebanner () {
        const self = this
        const formItem = self.formItem
        let reqData = {
          bannerId: self.updateId,
          bannerPic: formItem.bannerPic,
          bannerLink: formItem.bannerLink,
          beginTime: parseTime(self.dateInterval[0], '{y}-{m}-{d} {h}:{i}:{s}'),
          endTime: parseTime(self.dateInterval[1], '{y}-{m}-{d} {h}:{i}:{s}'),
          sort: formItem.sort || 0,
          gameId: formItem.gameId || 0,
          pf: +formItem.pf,
          title: formItem.title || '',
          desc: formItem.desc || '',
          marketStatus: +formItem.marketStatus,
          bannerType: +formItem.bannerType,
          buttonPic: formItem.buttonPic || '',
          buttonText: formItem.buttonText || '',
          buttonLink: formItem.buttonLink || '',
          subtitle: formItem.subtitle || ''
        }
        if (formItem.bannerKey === 'GAME_TAB_BANNER_KEY') {
          reqData.bannerType = '1'
        }
        if (formItem.btnShow === '0') {
          reqData.buttonText = ''
          reqData.buttonLink = ''
          reqData.buttonPic = ''
        }
        if (formItem.titleShow === '0') {
          reqData.subtitle = ''
          reqData.title = ''
        }
        if (!reqData.bannerPic || !reqData.bannerLink || !reqData.beginTime || !formItem.pf) {
          self.loadingState = false
          self.$Message.warning('请将表单填写完整再提交')
          return false
        }
        if (+formItem.btnShow && formItem.buttonLink === '') {
          self.loadingState = false
          self.$Message.warning('请提写按钮跳转链接')
          return false
        }
        if (!TestNum(reqData.sort)) {
          self.loadingState = false
          self.$Message.warning('权重格式有误')
          return false
        }
        if (formItem.bannerKey === 'HOMEPAGE_CHANNEL_FLOW') {
          if (!formItem.params) {
            self.loadingState = false
            self.$Message.warning('请将表单填写完整再提交')
            return false
          }
          reqData.params = +formItem.params
        }
        if (reqData.marketStatus === 0) {
          reqData.marketInfo = formItem.marketInfo
        }
        reqData.bannerLink = reqData.bannerLink.replace(/ /g, '')
        Api.updatebanner(self.updateId, reqData).then(res => {
          self.loadingState = false
          if (res.result === 0) {
            self.$Message.success('修改成功')
            self.pageChange(1)
            self.addLayerState = false
          } else {
            self.$Message.warning(res.msg)
          }
        })
      },
      // 停用banner
      offbanner (id) {
        const self = this
        self.$Modal.confirm({
          title: '操作确认',
          content: `确定停用？`,
          onOk () {
            Api.offbanner(id).then(res => {
              if (res.result === 0) {
                self.$Message.success('操作成功')
                self.pageChange(1)
              } else {
                self.$Message.warning(res.msg)
              }
            })
          }
        })
      },
      // 启用banner
      enablebanner (id) {
        const self = this
        Api.enablebanner(id).then(res => {
          if (res.result === 0) {
            self.$Message.success('操作成功')
            self.pageChange(1)
          } else {
            self.$Message.warning(res.msg)
          }
        })
      },
      // 父组件触发编辑操作，设置表单数据
      setEditData (data) {
        const self = this
        self.opt_type = 2
        self.formItem = JSON.parse(JSON.stringify(data))
        self.formItem.pf = String(self.formItem.pf)
        self.formItem.marketStatus = String(self.formItem.marketStatus)
        self.dateInterval = [self.formItem.beginTime, self.formItem.endTime]
        self.addLayerState = true
        self.formItem.bannerType += ''
        if (data.subtitle) {
          self.formItem.titleShow = '1'
        } else {
          self.formItem.titleShow = '0'
        }
        if (data.buttonText || data.buttonPic || data.buttonLink) {
          self.formItem.btnShow = '1'
        } else {
          self.formItem.btnShow = '0'
        }
      },
      // 编辑
      editItem: function (index) {
        const self = this
        self.setEditData(self.data[index])
      },
      toggleShow: function () {
        this.show = !this.show
      },
      /**
       * crop success
       *
       * [param] imgDataUrl
       * [param] field
       */
      cropSuccess: function (imgDataUrl) {
        const self = this
        self.uploadConf.spinShow = !self.uploadConf.spinShow
        /* self.formItem.bannerPic = imgDataUrl */
      },
      btnCropSuccess: function (imgDataUrl) {
        const self = this
        self.uploadConf.btnSpinShow = !self.uploadConf.btnSpinShow
        /* self.formItem.bannerPic = imgDataUrl */
      },
      /**
       * upload success
       *
       * [param] jsonData   服务器返回数据，已进行json转码
       * [param] field
       */
      cropUploadSuccess: function (jsonData) {
        const self = this
        self.formItem.bannerPic = jsonData.data
        self.uploadConf.spinShow = !self.uploadConf.spinShow
      },
      btnCropUploadSuccess: function (jsonData) {
        const self = this
        self.formItem.buttonPic = jsonData.data
        self.uploadConf.btnSpinShow = !self.uploadConf.btnSpinShow
      },
      /**
       * upload fail
       *
       * [param] status    server api return error status, like 500
       * [param] field
       */
      cropUploadFail: function () {
        this.$Message.error('上传失败，请重试！')
        this.uploadConf.spinShow = !this.uploadConf.spinShow
      },
      btnCropUploadFail: function () {
        this.$Message.error('上传失败，请重试！')
        this.uploadConf.btnSpinShow = !this.uploadConf.btnSpinShow
      }
    }
  };
</script>
