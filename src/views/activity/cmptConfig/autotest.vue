<template>
    <el-dialog :visible.sync="dialogVisible" :fullscreen="dialogFull" width="60%" :before-close="handleClose">
        <template slot="title">
            <el-row type="flex">
                <el-col :span="12">
                    <div>自动化测试配置</div>
                </el-col>
                <el-col :span="1" :offset="11">
                    <el-button type="text" size="mini" :icon="dialogFull ? 'el-icon-crop' : 'el-icon-full-screen'"
                        @click="dialogFull = !dialogFull"></el-button>
                </el-col>
            </el-row>
        </template>
        <codemirror ref="myCm" v-model="config" :options="cmOption"></codemirror>
        <span slot="footer" class="dialog-footer">
            <el-button @click="handleClose">取 消</el-button>
            <el-button type="primary" @click="submitAutotest">确 定</el-button>
        </span>
    </el-dialog>
</template>
<script>
// require component
import { codemirror } from 'vue-codemirror'
import 'codemirror/mode/xml/xml'
import 'codemirror/theme/monokai.css'
import request from '@/utils/request'

// require styles
import 'codemirror/lib/codemirror.css'
export default {
    data: function () {
        return {
            componentId: 0,
            config: '',
            dialogVisible: false,
            dialogFull: false,
            cmOption: {
                lineNumbers: true, // 显示行号
                styleActiveLine: true,
                theme: 'monokai',
                mode: 'xml'
            }
        }
    },
    components: {
        codemirror
    },
    methods: {
        handleClose() {
            this.dialogVisible = false
        },
        loadAutotestConfig(actId, componentId, componentIndex) {
            this.componentId = componentId
            const self = this
            request({
                url: 'autotest/config/preview',
                params: { actId, componentId, componentIndex },
                method: 'get'
            }).then((res) => {
                if (res && res.result === 200) {
                    self.config = res.data
                    self.dialogVisible = true
                } else {
                    self.$Message(res.msg)
                }
            }).catch((err) => {
                console.log(err)
                self.$Message('something came wrong!')
            })
        },
        submitAutotest() {
            const self = this
            this.$modal.confirm('将使用当前配置触发自动化测试，请确认').then(() => {
                request({
                    url: 'autotest/config/trigger',
                    params: {'componentId': self.componentId},
                    data: self.config,
                    method: 'post'
                }).then((res) => {
                    if (res && res.result === 200) {
                        const taskId = res.data
                        self.$modal.confirm('触发成功，任务ID为：' + taskId + '是否跳转查看详情？').then(() => {
                            window.open('https://automated.yy.com/#/monkey/result?id=' + taskId)
                        })
                    } else {
                        self.$Message(res.msg)
                    }
                }).catch((err) => {
                    console.log(err)
                    self.$Message('something came wrong!')
                }).finally(() => {
                    self.handleClose()
                })
            })
        }
    }
}
</script>
<style>
.CodeMirror {
    border: 1px solid #eee;
    height: 600px;
}
</style>
