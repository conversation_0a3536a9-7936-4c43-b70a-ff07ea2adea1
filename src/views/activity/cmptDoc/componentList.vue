<template>
  <div class="app-container">
    <div class="demo-collapse">
      <el-collapse>
        <el-collapse-item title="LevelOne-最通用稳定[1001~1999]" name="levelOne">
          <div>
            <el-table :data="componentVos.levelOne" border stripe style="width: 100%"
                      row-key="code">
              <el-table-column prop="componentId" label="组件ID" width="100"/>
              <el-table-column prop="componentTitle" label="组件名称" width="300"/>
              <el-table-column prop="author" label="组件作者" width="150"/>
              <el-table-column prop="remark" label="组件说明"/>
              <el-table-column fixed="right" label="Operations" width="120">
                <template slot-scope="scope">
                  <el-button link type="primary" size="small" @click="viewDoc(scope.row.componentId)">查看文档</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>

        <el-collapse-item title="LevelTwo-较通用稳定[2000~3999]" name="levelTwo">
          <div>
            <el-table :data="componentVos.levelTwo" border stripe style="width: 100%"
                      row-key="code">
              <el-table-column prop="componentId" label="组件ID" width="100"/>
              <el-table-column prop="componentTitle" label="组件名称" width="300"/>
              <el-table-column prop="author" label="组件作者" width="150"/>
              <el-table-column prop="remark" label="组件说明"/>
              <el-table-column fixed="right" label="Operations" width="120">
                <template slot-scope="scope">
                  <el-button link type="primary" size="small" @click="viewDoc(scope.row.componentId)">查看文档</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>

        <el-collapse-item title="LevelThree-发展完善中[5000~9999]" name="levelThree">
          <div>
            <el-table :data="componentVos.levelThree" border stripe style="width: 100%"
                      row-key="code">
              <el-table-column prop="componentId" label="组件ID" width="100"/>
              <el-table-column prop="componentTitle" label="组件名称" width="300"/>
              <el-table-column prop="author" label="组件作者" width="150"/>
              <el-table-column prop="remark" label="组件说明"/>
              <el-table-column fixed="right" label="Operations" width="120">
                <template slot-scope="scope">
                  <el-button link type="primary" size="small" @click="viewDoc(scope.row.componentId)">查看文档</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>

        <el-collapse-item title="LevelFour-活动定制组件" name="levelFour">
          <div>
            <el-table :data="componentVos.levelFour" border stripe style="width: 100%"
                      row-key="code">
              <el-table-column prop="componentId" label="组件ID" width="150"/>
              <el-table-column prop="componentTitle" label="组件名称" width="300"/>
              <el-table-column prop="author" label="组件作者" width="150"/>
              <el-table-column prop="remark" label="组件说明"/>
              <el-table-column fixed="right" label="Operations" width="120">
                <template slot-scope="scope">
                  <el-button link type="primary" size="small" @click="viewDoc(scope.row.componentId)">查看文档</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import Api from '@/api/activity/cmptDoc'

export default {
  name: "ComponentList",
  data: function () {
    return {
      componentVos: {}
    }
  },
  created() {
    this.listComponentDefine()
  },
  methods: {
    listComponentDefine() {
      const self = this
      Api.listComponentDefine().then(
        res => {
          if (res.result === 200) {
            self.componentVos = res.data
          } else {
            alert("拉取组件列表错误," + res.reason)
          }
        }
      )
    },
    viewDoc(componentId) {
      const param = {
        componentId: componentId
      }
      this.$router.push({path: 'componentDoc', query: param})
    }
  }
}
</script>
