<template>
  <div class="app-container">
    <div class="demo-collapse">
      <el-collapse v-model="activeNames">
        <el-collapse-item title="组件基本信息" name="componentBasicInfo">
          <div>
            <el-table :data="componentDoc.basicInfo" border stripe style="width: 100%"
                      row-key="code">
              <el-table-column prop="code" label="属性" width="250"/>
              <el-table-column prop="desc" label="属性值" width="400"/>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="组件数据库配置" name="componentDBConfig">
          <div>
            <el-table :data="componentDoc.attrVoList" border stripe style="width: 100%"
                      :tree-props="{ children: 'subAttrList'}" row-key="name">
              <el-table-column prop="name" label="字段" width="250"/>
              <el-table-column prop="desc" label="说明" width="200"/>
              <el-table-column prop="type" label="类型" width="200"/>
              <el-table-column prop="defaultValue" label="默认值" width="200"/>
              <el-table-column prop="configExample" label="配置示例"/>
            </el-table>
          </div>
        </el-collapse-item>
        <el-collapse-item title="事件处理方法" name="componentEventHandler">
          <div>
            <el-row v-for="(item,index) in componentDoc.eventHandlerList" :key="index">
              <div>
                <el-tag>{{ item.methodName }} ({{ item.commentText }})</el-tag>
              </div>
              <div>
                <el-tag>事件参数</el-tag>
              </div>
              <el-table :data="item.inputParams" border stripe style="width: 100%"
                        :tree-props="{ children: 'fieldList'}" row-key="name">
                <el-table-column prop="name" label="参数名" width="250"/>
                <el-table-column prop="typeName" label="类型" width="300"/>
                <el-table-column prop="commentText" label="说明"/>
              </el-table>
              <el-divider/>
            </el-row>
          </div>
        </el-collapse-item>
        <el-collapse-item title="对外方法" name="componentMethods">
          <div>
            <el-row v-for="(item,index) in componentDoc.publicMethodList" :key="index">
              <div>
                <el-tag>{{ item.methodName }} ({{ item.commentText }})</el-tag>
              </div>
              <div>
                <el-tag>请求参数</el-tag>
              </div>
              <el-table :data="item.inputParams" border stripe style="width: 100%"
                        :tree-props="{ children: 'fieldList'}" row-key="name">
                <el-table-column prop="name" label="参数名" width="250"/>
                <el-table-column prop="typeName" label="类型" width="300"/>
                <el-table-column prop="commentText" label="说明"/>
              </el-table>

              <div>
                <el-tag>响应参数</el-tag>
              </div>
              <el-table :data="item.returnParams" border stripe style="width: 100%"
                        :tree-props="{ children: 'fieldList'}" row-key="name">
                <el-table-column prop="name" label="参数名" width="250"/>
                <el-table-column prop="typeName" label="类型" width="300"/>
                <el-table-column prop="commentText" label="说明"/>
              </el-table>
              <el-divider/>
            </el-row>
          </div>
        </el-collapse-item>

        <el-collapse-item title="属性列表" name="componentProperty">
          <div>
            <el-table :data="componentDoc.fieldCommentBeanList" border stripe style="width: 100%" row-key="name">
              <el-table-column prop="name" label="属性名称" width="350"/>
              <el-table-column prop="commentText" label="说明" width="200"/>
              <el-table-column prop="typeName" label="类型" width="200"/>
              <el-table-column prop="value" label="属性值" width="200"/>
            </el-table>
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </div>
</template>

<script>
import Api from '@/api/activity/cmptDoc'

export default {
  name: "CmptDoc",
  data: function () {
    return {
      componentId: 0,
      componentDoc: {},
      activeNames:['componentBasicInfo']
    }
  },
  created() {
    this.componentId = this.$route.query.componentId
    this.loadComponentDoc()
  },
  methods: {
    loadComponentDoc() {
      const self = this
      Api.loadComponentDoc(this.componentId).then(
        res => {
          if (res.result === 200) {
            self.componentDoc = res.data
          } else {
            alert("拉取组件文档错误," + res.reason)
          }
        }
      )
    }
  }
}
</script>
