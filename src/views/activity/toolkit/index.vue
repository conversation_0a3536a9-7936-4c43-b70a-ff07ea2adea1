<template>
  <div class="app-container">
    <div>
      <Form :label-width="90" :model="formItem" label-position="top">
        <FormItem label="执行次数" prop="count" label-width="100%">
          <Input v-model="formItem.count"  style="width: 184px"/>
        </FormItem>
        <FormItem label="插入数据JSON信息，可输入多个JSON信息，用换行分隔" prop="jsonParam" label-width="100%">
          <Input v-model="formItem.jsonParam" type="textarea"  :autosize="{minRows: 5,maxRows: 8}" style="width: 80%"/>
          <!-- <Button :loading="loadingState0" type="primary" @click="login()"  v-hasPermi="['activity:toolkit:update']">go</Button> -->
           <!-- 后端接口已经有权限控制，这里ui可以显示按钮也没问题 -->
          <Button :loading="loadingState0" type="primary" @click="login()" >go</Button>
        </FormItem>
        <div>
          <p>游戏宝贝sample<br>
            {"busiId":400,"actId":202010002,"seq":"baby","actors":{"40001":"<font color="red">1508086586</font> ","40050":"<font color="red">1450596749_2782949806</font>","40003":"<font color="red">1507544887</font>","40004":"<font color="red">1450596749</font>"},"count":<font color="red">10</font>,"score":<font color="red">1000</font>,"itemId":"<font color="red">NDP_002</font>","rankCounts":{},"rankScores":{},"roleCounts":{},"roleScores":{},"extData":{},"extLong":0,"timestamp":0}
          </p>
          <p>交友sample<br>
            {"actId":2023021001,"actors":{50003:"2522180660",50004:"87814665",50014:"50074306"},"busiId":500,"count":1,"extData":{},"extLong":0,"itemId":"20459","rankCounts":{},"rankScores":{},"roleCounts":{},"roleScores":{},"score":10,"seq":"seq","timestamp":0}
          <p>约战sample<br>
            {"busiId":600,"actId":202010002,"seq":"yue","actors":{"60001":"<font color="red">1362154759"</font>,"60050":"<font color="red">63649476_2776187026</font>","60003":"<font color="red">1362154759</font>","60004":"<font color="red">63649476</font>"},"count":<font color="red">2330</font>,"score":<font color="red">23300</font>,"itemId":"<font color="red">140281</font>","rankCounts":{},"rankScores":{},"roleCounts":{},"roleScores":{},"extData":{},"extLong":0,"timestamp":0}</p>
          <p>陪玩sample<br>
            {"busiId":900,"actId":202010002,"seq":"peiwan","actors":{"90001":"<font color="red">2632067707</font>","90003":"<font color="red">2650488202</font>","90004":"<font color="red">10699345</font>","90060":"<font color="red">123</font>"},"count":<font color="red">1</font>,"score":<font color="red">19</font>,"itemId":"<font color="red">PW_ORDER</font>","timestamp":0}
          </p>
        </div>

        <FormItem label="活动ID" prop="actId" label-width="100%">
          <Input v-model="formItem.actId" required="true" style="width: 184px"/>
        </FormItem>
        <FormItem label="插入数据excel信息，可粘贴多行信息，busiId与标红数据，数据间空格分隔，每组用换行分隔，json将生成于上方输入框" prop="excelParam" label-width="100%">
          <Input v-model="formItem.excelParam" type="textarea" :autosize="{minRows: 5,maxRows: 8}" style="width: 80%"/>
          <Button :loading="loadingState1" type="primary" @click="excel2json()">doJsonTrans</Button>
        </FormItem>

        <div>
          <p>
            sample：游戏宝贝json<br>
            {"busiId":<font color="red">400</font>,"actId":202010002,"seq":"baby","actors":{"40001":"<font color="red">1508086586</font> ","40050":"<font color="red">1450596749_2782949806</font>","40003":"<font color="red">1507544887</font>","40004":"<font color="red">1450596749</font>"},"count":<font color="red">10</font>,"score":<font color="red">1000</font>,"itemId":"<font color="red">NDP_002</font>","rankCounts":{},"rankScores":{},"roleCounts":{},"roleScores":{},"extData":{},"extLong":0,"timestamp":0}<br>
            {"busiId":<font color="red">900</font>,"actId":202010002,"seq":"peiwan","actors":{"90001":"<font color="red">2632067707</font>","90003":"<font color="red">2650488202</font>","90004":"<font color="red">10699345</font>","90060":"<font color="red">123</font>"},"count":<font color="red">1</font>,"score":<font color="red">19</font>,"itemId":"<font color="red">PW_ORDER</font>","timestamp":0}<br>
            ...
          </p>
          <p>
            对应粘贴的excel数据,与json数据对应<br>
            <!--制表符\t-->
            <font color="red">400 1508086586 1450596749_2782949806 1507544887 1450596749 10 1000 NDP_002<br>
              900 2632067707 2650488202 10699345 123 1 19 PW_ORDER</font><br>
            ...
          </p>
        </div>

        <FormItem label="活动榜单一键生成actId" prop="mockActId" label-width="100%">
          <Input v-model="formItem.mockActId" required="true" style="width: 184px"/>
        </FormItem>
        <FormItem label="榜单隔天延时(用于结算，单位ms 默认5000)" prop="delayTime" label-width="100%">
          <Input v-model="formItem.delayTime" required="true" style="width: 184px"/>
        </FormItem>
        <FormItem>
          <Button :loading="loadingState3" type="primary" @click="submitToMockJobServer()">提交生成任务</Button>
        </FormItem>
      </Form>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/toolkit'
  export default {
    name: "Toolkit",
    components: {},
    data: function () {
      return {
        formItem: {
          count: 1,
          actId: 202010002,
          mockActId: 202010002,
          delayTime: 5000,
          jsonParam: '',
          excelParam: '',

        },
        loadingState0: false,
        loadingState1: false,
        loadingState3: false,

        busiIdBaby: "400",
        busiIdFri: "500",
        busiIdYue: "600",
        busiIdPw: "900",
        patternPreBaby: '',
        patternPreFri: '',
        patternPreYue: ``,
        patternPrePw: ``,
        rolesBaby: ["40001", "40050", "40003", "40004"],
        rolesFri: ["50003", "50004", "50014"],
        rolesYue: ["60001", "60050", "60003", "60004"],
        rolesPw: ["90001", "90003", "90004", "90060"],
        actorsBaby : ["1508086586","1450596749_2782949806","1507544887","1450596749"],
        titlesBaby: ["宝贝主播uid","交宝贝厅","宝贝用户uid","宝贝公会sid","交送礼次数","送礼积分","礼物代号"],
        patternTailBaby: `"rankCounts":{},"rankScores":{},"roleCounts":{},"roleScores":{},"extData":{},"extLong":0,"timestamp":0}`,
        patternTailFri: `"roleCounts":null,"roleScores":{},"rankCounts":null,"rankScores":null,"ip":"","mac":"","extLong":0,"extData":null,"timestamp":0,"sign":""}`,
        patternTailYue: `"rankCounts":{},"rankScores":{},"roleCounts":{},"roleScores":{},"extData":{},"extLong":0,"timestamp":0}`,
        patternTailPw: `"timestamp":0}`,
        // value: this.makeHtml( `{"busiId":400,"actId":202010002,"seq":"baby",`,this.rolesBaby,this.actorsBaby,10,1000,"NDP_002",this.patternTailBaby,this.titlesBaby)
      }
    },

    methods: {
      login() {

        const self = this;
        const formItem = self.formItem;
        let jsonContent=formItem.jsonParam;
        let cnt = formItem.count;
        let content = jsonContent;
        for( let i=0;i<cnt-1;++i ){
          content += "\n" + jsonContent;
        }
        let arrContent = content.split('\n');
        let size = 10;
        for( let i=0;i<arrContent.length;i=i+size ){
          this.submitToServer(arrContent.slice(i,i+size),i+size,arrContent.length);
        }
      },
      submitToMockJobServer() {
        const self = this;
        const formItem = self.formItem;
        Api.submitToMockJobServer(formItem.mockActId, formItem.delayTime).then(
          res => {
            self.$message.success(res.reason)
          }
        )
      },
      submitToServer( arrJson, i, total ){

        let reqData = {
          count: 1,
          jsonParams: arrJson,
        };
        const self = this;
        //reqData = JSON.stringify(reqData);
        Api.submitToServer(reqData).then(
          res => {
            self.$message.success(res.reason)
          }
        )
      },
      excel2json() {

        const self = this;
        const formItem = self.formItem;
        let JsonParam=formItem.jsonParam;
        if( JsonParam.length > 0 && !confirm("会覆盖JSON输入框内的数据，是否继续操作") ){
          return;
        }
        let Content = formItem.excelParam;
        let arrContent = Content.split('\n');
        let transResult = "";
        for (let i=0;i<arrContent.length;i++){
          let text="";
          let params = arrContent[i].split(' ');
          //console.log(params);
          let actors = params.slice(1,params.length-3);
          let count_arr = params[params.length-3];
          let score_arr = params[params.length-2];
          let itemId_arr = params[params.length-1];
          if (formItem.actId!=undefined){
            this.patternPreBaby = `{"busiId":400,"actId":${this.formItem.actId},"seq":"baby",`;
            this.patternPreFri = `{"busiId":500,"actId":${this.formItem.actId},"seq":"friend",`;
            this.patternPreYue = `{"busiId":600,"actId":${this.formItem.actId},"seq":"yue",`;
            this.patternPrePw = `{"busiId":900,"actId":${this.formItem.actId},"seq":"peiwan",`;
          }
          switch (params[0]) {
            case this.busiIdBaby:
              if (actors.length!=this.rolesBaby.length) alert("actors length does not match");
              text = this.patternPreBaby+this.makeActorsStr(this.rolesBaby,actors)+this.makeParamStr(count_arr,score_arr,itemId_arr)+this.patternTailBaby;
              break;
            case this.busiIdFri:
              if (actors.length!=this.rolesFri.length) alert("actors length does not match");
              text = this.patternPreFri+this.makeActorsStr(this.rolesFri,actors)+this.makeParamStr(count_arr,score_arr,itemId_arr)+this.patternTailFri;
              break;
            case this.busiIdYue:
              if (actors.length!=this.rolesYue.length) alert("actors length does not match");
              text = this.patternPreYue+this.makeActorsStr(this.rolesYue,actors)+this.makeParamStr(count_arr,score_arr,itemId_arr)+this.patternTailYue;
              break;
            case this.busiIdPw:
              if (actors.length!=this.rolesPw.length) alert("actors length does not match");
              text = this.patternPrePw+this.makeActorsStr(this.rolesPw,actors)+this.makeParamStr(count_arr,score_arr,itemId_arr)+this.patternTailPw;
              break;
            default:
              alert("busiId not found");
          }
          transResult=transResult+text;
          if (i<arrContent.length-1) transResult+="\n";
        }
        formItem.jsonParam=transResult;
      },
      makeActorsStr(roles, actors){
        let actorStr = `"actors":{`;
        for (let j=0; j<roles.length; j++){
          actorStr += `"${roles[j]}":"${actors[j]}"`;
          if (j<roles.length-1) actorStr+=`,`;
        }
        actorStr+=`},`;
        return actorStr;
      },

      makeParamStr(c,sc,it) {
        return `"count":${c},"score":${sc},"itemId":"${it}",`;
      },

      makeHtml(pre,roles,actors,c,sc,it,tail,titles) {
        let html = `${pre}`;
        html += `"actors":{`;
        for (let j=0; j<roles.length; j++){
          html += `"${roles[j]}":"<font color="red" title="${titles[j]}">${actors[j]}</font>"`;
          if (j<roles.length-1) html+=`,`;
        }
        html += `},`;
        html += `"count":<font color="red" title="${titles[roles.length]}">${c}</font>,`
          + `"score":<font color="red" title="${titles[roles.length+1]}">${sc}</font>,`
          + `"itemId":"<font color="red" title="${titles[roles.length+2]}">${it}</font>",`;
        html += tail;
        return html;
      }


    }
  };
</script>
