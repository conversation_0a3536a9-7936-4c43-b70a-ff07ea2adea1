<template>
  <div class="app-container">
    <div>

      <Form :label-width="20" :model="formItem" inline>
        <FormItem  prop="actId">
          <Input v-model="formItem.actId" placeholder="actId" style="width: 200px">
            <span slot="prepend">活动ID：</span>
          </Input>
        </FormItem>

        <FormItem>
          <Button :loading="loadingState1" type="primary" @click="setSettleStatus()">进入结算</Button>
          <Button :loading="loadingState2" type="primary" @click="delSettleStatus()">退出结算</Button>
        </FormItem>
      </Form>

    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/geSettle'
  export default {
    name: "GeSettle",
    components: {},
    data: function () {
      return {
        data: [],
        formItem: {
          actId:202012001,
        },
        loadingState1: false,
        loadingState2: false,
      }
    },

    methods:{
      setSettleStatus(){

        const self = this;
        const formItem = self.formItem;
        //let reqData = "actId="+formItem.actId;
        let reqData = {"actId": formItem.actId}
        self.loadingState1 = true;
        if (formItem.actId===null || formItem.actId===""){
          alert("数据有误");
        }
        Api.setSettleStatus(reqData).then(
          res => {
            self.loadingState1 = false;
            alert(res.result);
          }
        )
      },
      delSettleStatus(){

        const self = this;
        const formItem = self.formItem;
        //let reqData = "actId="+formItem.actId;
        self.loadingState2 = true;
        if (formItem.actId===null || formItem.actId===""){
          alert("数据有误");
        }
        let reqData = {"actId": formItem.actId}
        Api.delSettleStatus(reqData).then(
          res => {
            self.loadingState2 = false;
            alert(res.result);
          }
        ).catch(() => {
          self.loadingState2 = false;
          alert("设置失败");
        });
      }
    }
  };
</script>
