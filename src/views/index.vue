<template>
  <div class="dashboard-editor-container">
    <el-row :gutter="32">
      <el-col v-for="(item,index) in actData" :key="index" :xs="24" :sm="24" :lg="12">
        <div class="chart-wrapper">
          <pie-chart :title-text='item.titleText' :legend-data="item.legendData" :series-data="item.seriesData"
                     :source="item.source"/>
        </div>
      </el-col>
   <!-- </el-row>
    <el-row :gutter="32">-->
      <el-col :xs="24" :sm="24" :lg="12" v-if="componentDataShow">
        <div class="chart-wrapper">
          <bar-chart :title-text="componentData.titleText" :legend-data="componentData.legendData"
                     :series-data="componentData.seriesData"/>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>

import Api from '@/api/activity/home'

import PanelGroup from './dashboard/PanelGroup'
import LineChart from './dashboard/LineChart'
import Raddar<PERSON>hart from './dashboard/RaddarChart'
import PieChart from './dashboard/PieChart'
import BarChart from './dashboard/BarChart'

export default {
  name: 'Index',
  components: {
    PanelGroup,
    LineChart,
    RaddarChart,
    PieChart,
    BarChart
  },
  data() {
    return {
      actData: [],
      componentData: {},
      componentDataShow: false
    }
  },
  methods: {
    handleSetLineChartData(type) {

    }
  },
  mounted() {
    const self = this
    Api.statist().then(
      res => {
        if (res.result === 200) {
          self.actData = res.data.actData
          self.componentData = res.data.componentData
          self.componentDataShow = true
        } else {
          self.$Message.warning(res.reason)
        }
      }
    ).catch((error) => {
      self.$Message.error(error.status + "," + error.statusText, 5)
    });
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;

  .chart-wrapper {
    background: #fff;
    padding: 16px 16px 0;
    margin-bottom: 32px;
  }
}

@media (max-width: 1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
