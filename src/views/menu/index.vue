<template>
  <div>
    <EmpReactAdepter remote="AstroAdmin/AdminTableLoader" :loadingText="''" :tableConfig="tableConfig" :styleType="styleType" ></EmpReactAdepter>
  </div>
</template>

<script>
import { getParam } from '@/utils/util'
export default {
  name: "menu",
  data() {
    return {
      tableConfig: [],
      styleType: 1,
    };
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const pageUrl = location.href
      const tableKey = getParam('tableKey')
      const tableName = getParam('tableName')
      const tabName = getParam('tabName')
      const styleType = Number.parseInt(getParam('styleType')) || 1
      const slotNameArr = tableKey.split('|')
      const slotTableTitleArr = tableName.split('|')
      const slotDescriptionArr = tabName.split('|')
      const tableConfig = []
      slotNameArr.map((slotName, index) => {
        tableConfig.push({
          slotDescription: slotDescriptionArr[index] || slotTableTitleArr[index] || '',
          slotName: slotName || '',
          slotTableTitles: slotTableTitleArr[index] || '',
        })
      })
      this.styleType = styleType
      this.tableConfig = tableConfig
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">

</style>
