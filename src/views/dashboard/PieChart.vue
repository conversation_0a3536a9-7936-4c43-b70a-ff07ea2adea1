<template>
  <div :class="className" :style="{height:height,width:width}"/>
</template>

<script>
import echarts from 'echarts'

require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    titleText: {
      type: String,
      default: ''
    },
    legendData: {
      type: Array,
      default: []
    },
    seriesData: {
      type: Array,
      default: []
    }, source: {
      type: String,
      default: ''
    }
  },
  data() {
    return {}
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {

  },
  methods: {
    initChart() {
      const self = this
      const chart = echarts.init(this.$el)

      chart.setOption({
        title: {
          text: this.titleText,
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 'center',
          data: this.legendData
        },
        series: [
          {
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '55%'],
            data: this.seriesData,
          }
        ]
      })

      chart.on('click', function (params) {
        self.$router.push({path: 'dataCollect/componentList22', query: {}});
      });
    }
  }
}
</script>
