<template>
  <div>
    <div slot="header" class="clearfix">
      <span>{{ procName }}</span>
      <el-button style="float: right; padding: 3px 0" type="text" @click="onStartProcess" v-show="showStartProc">发起新流程</el-button>
      <el-button style="float: right; padding: 3px 0" type="text" @click="loadProcessInfo(actId)" v-show="actId > 0 && procState === 'proceeding'">刷新</el-button>
    </div>
    <el-steps :space="200" :active="activeStep" finish-status="success">
      <el-step v-for="step in steps" :key="step.activityId" :title="step.stepName" :description="step.stepDoc" :status="stepStatus(step.state)">
        <template v-if="step.state === 'executable' || step.state === 'fail_executable' || step.state === 'fail_suspend'" slot="description">
          <el-button v-if="step.state === 'executable'" @click="onExecuteProcActivity(step.activityId)" size="small" type="primary" plain>执行</el-button>
          <el-popover v-if="step.state === 'fail_executable'" placement="top-start" width="auto" trigger="hover" title="错误">
            <pre>{{step.failMessage}}</pre>
            <el-button slot="reference" @click="onExecuteProcActivity(step.activityId)" size="small" type="danger" plain>再次执行</el-button>
          </el-popover>
          <el-popover v-if="step.state === 'fail_suspend'" placement="top-start" width="auto" trigger="hover" title="错误">
            <pre>{{step.failMessage}}</pre>
            <el-button slot="reference" @click="onRestartProcessActivity(step.activityId)" size="small" type="danger" plain>重置执行</el-button>
          </el-popover>
        </template>
        <template v-if="step.state === 'fail_suspend'" slot="icon">
          <i class="el-icon-error" />
        </template>
      </el-step>
    </el-steps>
  </div>
</template>

<script>
import { queryProcessStep, startProcess, executeProcessActivity, restartProcessActivity } from '@/api/activity/actFastConfigDeploy'

export default {
  name: 'step',
  data: function() {
    return {
      actId: 0,
      procInstanceId: 0,
      procName: '',
      procDesc: '',
      cycle: 0,
      entry: false,
      procState: '',
      steps: []
    }
  },
  props: {
    procId: {
      type: String,
      required: true
    }
  },
  computed: {
    activeStep() {
      if (this.steps && this.steps.length > 0) {
        let rs = -1
        for (let i = 0; i < this.steps.length; i++) {
          const state = this.steps[i].state
          if (state === 'proceeding' || state === 'executable' || state === 'fail_executable' || state === 'fail_suspend') {
            return i
          }

          if (state === 'done') {
            rs = i + 1
          }
        }

        return rs
      }

      return -1
    },
    showStartProc() {
      return this.procState !== 'proceeding' && (!this.procInstanceId || this.cycle < 0)
    }
  },
  beforeMount() {
    this.loadProcessInfo(0)
  },
  methods: {
    stepStatus(state) {
      if (state === 'fail_suspend' || state === 'fail_executable') {
        return 'error'
      }

      if (state === 'nys') {
        return 'wait'
      }

      if (state === 'proceeding' || state === 'executable') {
        return 'process'
      }

      return 'success'
    },
    loadProcessInfo(actId) {
      this.actId = actId
      const self = this
      queryProcessStep(this.procId, actId).then(res => {
        if (res && res.result === 200) {
          self.procName = res.data.procName
          self.procDesc = res.data.procDesc
          self.procInstanceId = res.data.procInstanceId || 0
          self.procState = res.data.procState
          self.cycle = res.data.cycle
          self.entry = res.data.entry
          self.steps = res.data.steps
          return
        }

        self.$message({message: res.reason, type: 'error'})
      })
    },
    onStartProcess() {
      this.entry ? this.onStartEntryProcess() : this.onStartNormalProcess()
    },
    onStartEntryProcess() {
      if (this.actId) {
        this.$message({message: '发起新' + this.procName + '需重新刷新页面', type: 'error'})
        return
      }

      const self = this
      this.$prompt('请输入需要发起' + this.procName + '的活动ID', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /\d{4}[01][0-9][1-7]\d{3}/,
        inputErrorMessage: '活动ID格式错误'
      }).then(({ value }) => {
        self.doStartProcess(value)
      }).catch(() => {

      })
    },
    onStartNormalProcess() {
      if (!this.actId) {
        this.$message({message: '发起新' + this.procName + '需先选择一个活动', type: 'error'})
        return
      }

      this.doStartProcess(this.actId)
    },
    doStartProcess(_actId) {
      const self = this
      startProcess(this.procId, _actId, {}).then(res => {
        if (res && res.result === 200) {
          self.$message({message: '流程启动成功', type: 'success'})
          self.$nextTick(() => {
            self.loadProcessInfo(_actId)
          })

          return
        }

        if (res && res.result === 210) {
          self.$message({message: res.reason, type: 'warning'})
          self.$nextTick(() => {
            self.loadProcessInfo(_actId)
          })

          return
        }

        if (res && res.result === 211) {
          self.$message({message: res.reason, type: 'error'})
          self.$nextTick(() => {
            self.loadProcessInfo(_actId)
          })

          return
        }

        self.$message({message: (res ? res.reason : '请求错误'), type: 'error'})
      }).catch((err) => {
        console.log(err)
      })
    },
    onExecuteProcActivity(activityId) {
      const self = this
      this.$confirm('触发执行当前步骤？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '我再想想',
        type: 'info'
      }).then(() => {
        executeProcessActivity(self.procInstanceId, activityId).then(res => {
          if (!res || res.result !== 200) {
            self.$message({message: res.reason, type: 'error'})
            return
          }

          self.$message({message: '节点发起执行成功', type: 'success'})
          self.$nextTick(() => {
            self.loadProcessInfo(self.actId)
          })
        }).catch(err => {
          self.$message({message: err, type: 'error'})
        })
      }).catch(() => {})
    },
    onRestartProcessActivity(activityId) {
      const self = this
      this.$confirm('此步骤被配置成了非幂等步骤，重置执行将再次触发执行此步骤，请先确保再次执行不会出现非预期结果？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '我再想想',
        type: 'warning'
      }).then(() => {
        restartProcessActivity(self.procInstanceId, activityId).then(res => {
          if (!res || res.result !== 200) {
            self.$message({message: res.reason, type: 'error'})
            return
          }

          self.$message({message: '节点重置执行成功', type: 'success'})
          self.$nextTick(() => {
            self.loadProcessInfo(self.actId)
          })
        }).catch(err => {
          self.$message({message: err, type: 'error'})
        })
      }).catch(() => {})
    }
  }
}
</script>

<style>

</style>
