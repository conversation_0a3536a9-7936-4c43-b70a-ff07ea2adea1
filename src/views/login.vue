<template>
  <div class="login">
    <div label-position="left" label-width="0px" class="login-form">
      <h3 class="title">{{title}}</h3>
      <!--登录组件锚点-->
      <div id="udbsdk_login_content" class="emstyle" style="_overflow: hidden; _width: 313px;"  v-loading="loginLoading"/>
    </div>
    <!--  底部  -->
    <div class="el-login-footer">
      <span>Copyright © 2018-2021 yy hdzt All Rights Reserved.</span>
    </div>
  </div>
</template>

<script>
  import {decorateURL, openDiv} from "@/utils/yy-login";
  import qs from 'qs'

  export default {
  name: "Login",
  data() {
    return {
      title: process.env.VUE_APP_TITLE,
      loginLoading: false,
      // 注册开关
      register: false,
      redirect: undefined
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        const fullPath = route.fullPath
        let data = route.query
        if (fullPath.indexOf('?')) {
          const dataString = fullPath.substring(fullPath.indexOf('?') + 1)
          data = qs.parse(dataString)
        }
        if (data && data.hasOwnProperty('loginSuccess') && data.loginSuccess === 'true') {
          const path = route.path
          const newQuery = JSON.parse(JSON.stringify(data))
          delete newQuery.loginSuccess
          // 不会留下记录，无法返回
          this.$router.replace({ path, query: newQuery })
          this.loginLoading = true
          this.$store.dispatch("LoginCallback").then(() => {
            this.$router.push({ path: this.redirect || "/" }).catch(()=>{});
          }).catch(() => { this.loginLoading = false});
        }
      },
      immediate: true
    }
  },
  created() {
    this.init()
  },
  methods: {
    init(){
      let fullPath = this.$router.history.base + this.$router.history.current.fullPath
      fullPath = decorateURL(fullPath)
      const callbackUrl = fullPath + (fullPath.indexOf('?') ? '&' : '?') + 'loginSuccess=true'
      openDiv(callbackUrl)
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss">
.login {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-image: url("../assets/images/login-background.jpg");
  background-size: cover;
}
.title {
  margin: 0px auto 30px auto;
  text-align: center;
  color: #707070;
}

.login-form {
  border-radius: 6px;
  background: #ffffff;
  width: 400px;
  padding: 25px 25px 5px 25px;
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}
.login-code {
  width: 33%;
  height: 38px;
  float: right;
  img {
    cursor: pointer;
    vertical-align: middle;
  }
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
.login-code-img {
  height: 38px;
}
</style>
