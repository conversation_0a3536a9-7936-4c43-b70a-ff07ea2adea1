import Vue from 'vue'
import store from '@/store/index'
import { getParam } from '@/utils/util'
export const getDns = (test, online) => {
  return (/test|webtest|pre|localhost/.test(location.hostname)) ? test : online
}
const { VueAdapter } = window.EMP_ADAPTER_LIB

// 注册全局组件
Vue.component('EmpReactAdepter', VueAdapter.adapter(Vue, { store }))
window.EMPShareGlobalVal = {
  frameworkLib: 'EMP_ADAPTER_REACT',
  runtimeLib: 'EMP_SHARE_RUNTIME',
  framework: 'react'
}

// 判断隐藏 AstroTable 功能
window._ASTRO_GLOBAL_HIDE_TABLE = {
  // 'tableKey': 1
}
// 本地开发 url 增加参数 isDev=1 切换为本地 emp.js 地址，服务器地址配置参数不会生效防止错误发版
const isDev = getParam('isDev')

const ADMIN_EMP_VERSION = '17'

VueAdapter.init({
  remotes: [
    {
      name: `astro_ui_admin_compontent_0_${ADMIN_EMP_VERSION}_0`,
      entry: `https://unpkg.yy.com/@astro-ui/admin-compontent@0.${ADMIN_EMP_VERSION}.0/dist/emp.js`,
      // name: `astro_ui_admin_compontent_1_0_0`,
      // entry: `https://localhost:8000/emp.js`,
      alias: 'AstroAdmin'
    },
    // {
    //   name: 'Astro_Render',
    //   entry: 'https://unpkg.yy.com/@astro/render@1.3.0/dist/emp.js',
    //   alias: '@hd/astro-render'
    // }
    // {
    //   name: 'Astro_Render',
    //   entry: 'https://************:8000/emp.js'
    //   // entry: 'https://localhost:8001/emp.js'
    // }
  ]
})
