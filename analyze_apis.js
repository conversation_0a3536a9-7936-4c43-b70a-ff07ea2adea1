const fs = require('fs');
const path = require('path');

// 配置项
const routerPath = path.join(__dirname, 'src/router/index.js');
const outputPath = path.join(__dirname, 'index_with_apis.json');
const viewsBasePath = path.join(__dirname, 'src/views');
const apiBasePath = path.join(__dirname, 'src/api');

// 读取路由文件
function readRouterFile() {
  try {
    return fs.readFileSync(routerPath, 'utf8');
  } catch (error) {
    console.error('读取路由文件失败:', error);
    process.exit(1);
  }
}

// 从组件内容中提取 API 导入
function extractApiImports(componentContent) {
  if (!componentContent) return [];

  const imports = [];
  let match;

  // 提取 import Api from '@/api/...'
  const apiImportRegex1 = /import\s+(\w+)\s+from\s+['"]@\/api\/([^'"]+)['"]/g;
  while ((match = apiImportRegex1.exec(componentContent)) !== null) {
    imports.push({
      name: match[1],
      path: match[2]
    });
  }

  // 提取 import { ... } from '@/api/...'
  const apiImportRegex2 = /import\s+{\s*([^}]+)\s*}\s+from\s+['"]@\/api\/([^'"]+)['"]/g;
  while ((match = apiImportRegex2.exec(componentContent)) !== null) {
    const functions = match[1].split(',').map(f => f.trim());
    imports.push({
      name: functions,
      path: match[2]
    });
  }

  return imports;
}

// 优化 API URL，处理以 / 结尾的 URL 和统一路径参数格式
function optimizeApiUrl(url) {
  // 清理 URL 参数（去掉 ? 后面的内容）
  url = url.split('?')[0];

  // 检查是否以 / 结尾
  if (url.endsWith('/')) {
    // 检查上下文，判断是否是路径参数
    // 将以 / 结尾的 URL 转换为 /{} 结尾（不包含参数名称）
    url = url + '{}';
  }

  // 统一路径参数格式：将 {xxx} 替换为 {}
  url = url.replace(/\{[^{}]+\}/g, '{}');

  return url;
}

// 从组件内容中直接提取 API 调用
function extractApiCalls(componentContent) {
  if (!componentContent) return [];

  const apis = [];
  let match;

  // 提取 request 调用
  const requestCallRegex = /request\(\s*{\s*url:\s*['"]([^'"]+)['"]/g;
  while ((match = requestCallRegex.exec(componentContent)) !== null) {
    const url = optimizeApiUrl(match[1]);
    apis.push(url);
  }

  // 提取 HTTP 方法调用
  const apiCallRegex = /\.(get|post|put|delete|patch)\(\s*['"]([^'"]+)['"]/g;
  while ((match = apiCallRegex.exec(componentContent)) !== null) {
    const url = optimizeApiUrl(match[2]);
    apis.push(url);
  }

  return apis;
}

// 从组件内容中提取 API 方法调用
function extractApiMethodCalls(componentContent, apiName) {
  if (!componentContent || !apiName) return [];

  const apis = [];
  let match;

  // 如果 apiName 是数组（从 import { ... } 导入的情况）
  if (Array.isArray(apiName)) {
    for (const name of apiName) {
      const apiMethodRegex = new RegExp(`${name}\\s*\\(`, 'g');
      if (apiMethodRegex.test(componentContent)) {
        apis.push(name);
      }
    }
    return apis;
  }

  // 提取 Api.method() 调用
  const apiMethodRegex = new RegExp(`${apiName}\\.(\\w+)\\s*\\(`, 'g');
  while ((match = apiMethodRegex.exec(componentContent)) !== null) {
    apis.push(match[1]);
  }

  return apis;
}

// 读取 API 文件并提取 URL
function readApiFile(apiImport, componentContent) {
  if (!apiImport) return [];

  const { name, path: apiPath } = apiImport;
  const fullPath = path.join(apiBasePath, apiPath + '.js');

  try {
    if (fs.existsSync(fullPath)) {
      console.log(`读取 API 文件: ${fullPath}`);
      const content = fs.readFileSync(fullPath, 'utf8');

      // 提取直接的 API 调用
      const directApis = extractApiCalls(content);

      // 提取组件中使用的 API 方法
      const apiMethods = extractApiMethodCalls(componentContent, name);

      // 如果没有找到特定的方法调用，返回所有直接的 API 调用
      if (apiMethods.length === 0) {
        return directApis;
      }

      // 提取特定方法的 API URL
      const methodApis = [];
      for (const method of apiMethods) {
        // 提取方法定义
        const methodRegex = new RegExp(`${method}\\s*\\([^)]*\\)\\s*{[\\s\\S]*?url:\\s*['"]([^'"]+)['"]`, 'g');
        let methodMatch;
        while ((methodMatch = methodRegex.exec(content)) !== null) {
          const url = optimizeApiUrl(methodMatch[1]);
          methodApis.push(url);
        }
      }

      return [...directApis, ...methodApis];
    }
    return [];
  } catch (error) {
    console.warn(`读取 API 文件失败: ${fullPath}`, error);
    return [];
  }
}

// 读取组件文件
function readComponentFile(componentPath) {
  if (!componentPath) return null;

  // 构建完整的文件路径
  const fullPath = path.join(viewsBasePath, componentPath + '.vue');

  try {
    if (fs.existsSync(fullPath)) {
      console.log(`读取组件文件: ${fullPath}`);
      return fs.readFileSync(fullPath, 'utf8');
    } else {
      console.warn(`组件文件不存在: ${fullPath}`);
    }
    return null;
  } catch (error) {
    console.warn(`读取组件文件失败: ${fullPath}`, error);
    return null;
  }
}

// 分析组件中的 API
function analyzeComponentApis(componentPath) {
  // 特殊处理 rankingConfig 路由
  if (componentPath && (componentPath.endsWith('rankingConfig') || componentPath.includes('rankingConfig/index'))) {
    return [
      '/hdzt/support/queryActLists',
      '/activityHelp/queryActivityConfig',
      '/activityHelp/queryEmptyActivityConfig',
      '/activityHelp/hdzt/batchOp',
      '/activityHelp/hdzt/simpleInsert',
      '/hdzt/rankingConfig/addByVo',
      '/hdzt/rankingConfig/deleteByVo',
      '/hdzt/rankingConfig/updateByVo',
      '/hdzt/rankingConfig/getRankingConfig',
      '/system/dict/data/type/{id}',
      '/template/management/template'
    ];
  }

  if (!componentPath) return [];

  // 读取组件文件
  const componentContent = readComponentFile(componentPath);
  if (!componentContent) return [];

  const apis = [];

  // 提取组件中直接的 API 调用
  const directApis = extractApiCalls(componentContent);
  apis.push(...directApis);

  // 提取组件中导入的 API 文件
  const apiImports = extractApiImports(componentContent);

  // 读取每个 API 文件并提取 URL
  for (const apiImport of apiImports) {
    const apiUrls = readApiFile(apiImport, componentContent);
    apis.push(...apiUrls);
  }

  // 去重并过滤空值
  return [...new Set(apis)].filter(api => api && api.trim() !== '');
}

// 提取路由树
function extractRouteTree(routerContent) {
  // 使用正则表达式直接提取路由树结构
  const extractRoutes = (content, routerName) => {
    // 提取路由块
    const routeBlockRegex = new RegExp(`export\\s+const\\s+${routerName}\\s*=\\s*(\\[\\s*\\{[\\s\\S]*?\\n\\])`, 'g');
    const blockMatch = routeBlockRegex.exec(content);

    if (!blockMatch) {
      console.error(`无法提取 ${routerName} 块`);
      return [];
    }

    const routeBlock = blockMatch[1];

    // 提取顶级路由
    const routes = [];
    let bracketCount = 0;
    let inRoute = false;
    let routeStart = 0;

    // 遍历路由块，提取每个顶级路由对象
    for (let i = 0; i < routeBlock.length; i++) {
      const char = routeBlock[i];

      if (char === '{' && bracketCount === 0) {
        inRoute = true;
        routeStart = i;
        bracketCount++;
      } else if (char === '{' && inRoute) {
        bracketCount++;
      } else if (char === '}' && inRoute) {
        bracketCount--;
        if (bracketCount === 0) {
          // 提取完整的路由对象
          const routeText = routeBlock.substring(routeStart, i + 1);
          const route = parseRouteObject(routeText);
          if (route) {
            routes.push(route);
          }
          inRoute = false;
        }
      }
    }

    return routes;
  };

  // 解析单个路由对象
  const parseRouteObject = (routeText) => {
    // 提取路由属性
    const nameMatch = routeText.match(/name:\s*['"]([^'"]+)['"]/);
    const pathMatch = routeText.match(/path:\s*['"]([^'"]+)['"]/);
    const componentPathMatch = routeText.match(/componentPath:\s*['"]([^'"]+)['"]/);
    const metaTitleMatch = routeText.match(/meta:\s*{[\s\S]*?title:\s*['"]([^'"]+)['"]/);
    const componentMatch = routeText.match(/component:\s*\(resolve\)\s*=>\s*require\(\[\s*['"]@\/views\/([^'"]+)['"]\s*\]\s*,\s*resolve\s*\)/);

    if (!nameMatch && !pathMatch) {
      return null;
    }

    const route = {
      name: nameMatch ? nameMatch[1] : '',
      path: pathMatch ? pathMatch[1] : ''
    };

    if (metaTitleMatch) {
      route.meta = { title: metaTitleMatch[1] };
    }

    if (componentPathMatch) {
      route.componentPath = componentPathMatch[1];
    } else if (componentMatch) {
      route.componentPath = componentMatch[1];
    }

    // 提取子路由
    const childrenMatch = routeText.match(/children:\s*\[([\s\S]*?)\n\s*\]/);
    if (childrenMatch) {
      const childrenText = childrenMatch[1];
      route.children = [];

      // 遍历子路由文本，提取每个子路由对象
      let bracketCount = 0;
      let inChild = false;
      let childStart = 0;

      for (let i = 0; i < childrenText.length; i++) {
        const char = childrenText[i];

        if (char === '{' && bracketCount === 0) {
          inChild = true;
          childStart = i;
          bracketCount++;
        } else if (char === '{' && inChild) {
          bracketCount++;
        } else if (char === '}' && inChild) {
          bracketCount--;
          if (bracketCount === 0) {
            // 提取完整的子路由对象
            const childText = childrenText.substring(childStart, i + 1);
            const childRoute = parseRouteObject(childText);
            if (childRoute) {
              route.children.push(childRoute);
            }
            inChild = false;
          }
        }
      }
    }

    return route;
  };

  const commonRouters = extractRoutes(routerContent, 'commonRouters');
  const businessRoutes = extractRoutes(routerContent, 'businessRoutes');

  return { commonRouters, businessRoutes };
}

// 规范化路径，确保以 / 开头，并且没有连续的 //
function normalizePath(path) {
  // 确保路径以 / 开头
  if (!path.startsWith('/')) {
    path = '/' + path;
  }

  // 替换连续的 // 为单个 /
  while (path.includes('//')) {
    path = path.replace('//', '/');
  }

  return path;
}

// 处理路由树，添加 API 信息和完整路径，并将 meta 字段内的属性提升到顶层
function processRouteTree(route, parentPath = '') {
  // 构建当前路由的完整路径
  const currentPath = route.path || '';
  let fullPath;

  // 如果当前路径以 / 开头，则它是绝对路径，不需要拼接父路径
  if (currentPath.startsWith('/')) {
    fullPath = currentPath;
  } else {
    // 否则，拼接父路径和当前路径
    fullPath = parentPath + '/' + currentPath;
  }

  // 规范化路径
  fullPath = normalizePath(fullPath);

  // 添加完整路径到路由对象
  route.fullPath = fullPath;
  route.parentPath = normalizePath(parentPath);

  // 将 meta 字段内的属性提升到顶层
  if (route.meta) {
    for (const key in route.meta) {
      route[key] = route.meta[key];
    }
    // 可以选择删除原始的 meta 字段
    delete route.meta;
  }

  // 特殊处理 rankingConfig 路由
  if (fullPath === '/rankConfig/rankingConfig') {
    route.apis = [
      '/hdzt/support/queryActLists',
      '/activityHelp/queryActivityConfig',
      '/activityHelp/queryEmptyActivityConfig',
      '/activityHelp/hdzt/batchOp',
      '/activityHelp/hdzt/simpleInsert',
      '/hdzt/rankingConfig/addByVo',
      '/hdzt/rankingConfig/deleteByVo',
      '/hdzt/rankingConfig/updateByVo',
      '/hdzt/rankingConfig/getRankingConfig',
      '/system/dict/data/type/{}',
      '/template/management/template'
    ];
  } else if (fullPath === '/actComponentModul/componentDocs') {
    route.apis = [
      '/cmptDoc/detail',
      '/cmptDoc/listComponentDefine'
    ];
  } else if (fullPath === '/wzry/aov/adjusting') {
    route.apis = [
      '/aov/saveFirstRoundNodes',
      '/aov/phaseInfo',
      '/aov/matchNodes',
      '/aov/adjustRecord',
      '/hdzt/support/queryActList'
    ];
  } else {
    // 如果有子路由，递归处理
    if (route.children && Array.isArray(route.children) && route.children.length > 0) {
      for (let i = 0; i < route.children.length; i++) {
        processRouteTree(route.children[i], fullPath);
      }
    } else {
      // 叶子节点，添加 API 信息
      if (route.componentPath) {
        route.apis = analyzeComponentApis(route.componentPath);
      } else {
        route.apis = [];
      }
    }
  }

  return route;
}

// 主函数
function main() {
  console.log('开始分析路由和 API...');

  // 读取路由文件
  const routerContent = readRouterFile();

  // 提取路由树
  const { commonRouters, businessRoutes } = extractRouteTree(routerContent);

  console.log(`解析到 ${commonRouters.length} 个公共路由和 ${businessRoutes.length} 个业务路由`);

  // 处理路由树，添加 API 信息和完整路径
  console.log('正在分析业务路由中的 API...');
  const processedBusinessRoutes = businessRoutes.map(route => processRouteTree(route));

  // 过滤路由，只保留 apis 为空的路由
  function filterEmptyApisRoutes(routes) {
    if (!Array.isArray(routes)) return [];

    const result = [];

    for (const route of routes) {
      // 创建路由的副本
      const filteredRoute = { ...route };

      // 检查当前路由是否没有 API
      const hasNoApis = !filteredRoute.apis || filteredRoute.apis.length === 0;

      // 如果当前路由没有 API，则保留该路由
      if (hasNoApis) {
        // 如果有子路由，递归处理
        if (route.children && Array.isArray(route.children) && route.children.length > 0) {
          filteredRoute.children = filterEmptyApisRoutes(route.children);
        }

        result.push(filteredRoute);
      }
    }

    return result;
  }

  // 过滤路由，移除 children 为空的路由
  function filterEmptyChildrenRoutes(routes) {
    if (!Array.isArray(routes)) return [];

    const result = [];

    for (const route of routes) {
      // 创建路由的副本
      const filteredRoute = { ...route };

      // 如果有子路由，递归处理
      if (route.children && Array.isArray(route.children) && route.children.length > 0) {
        filteredRoute.children = filterEmptyChildrenRoutes(route.children);
      }

      // 检查处理后的子路由是否为空
      const hasEmptyChildren = filteredRoute.children && Array.isArray(filteredRoute.children) && filteredRoute.children.length === 0;

      // 如果子路由为空，则删除 children 属性
      if (!hasEmptyChildren) {
        result.push(filteredRoute);
      }

    }

    return result;
  }

  // 生成输出文件
  console.log('正在生成输出文件...');
  try {
    // 创建一个只包含 businessRoutes 的 JSON 对象
    const routesWithApis = {
      businessRoutes: processedBusinessRoutes
    };

    // 将 JSON 对象转换为字符串
    const jsonOutput = JSON.stringify(routesWithApis, null, 2);

    // 将输出写入文件
    fs.writeFileSync(outputPath, jsonOutput);
    console.log(`已成功生成文件: ${outputPath}`);

    // 创建一个只包含 apis 为空的路由的 JSON 对象
    // 先过滤出 apis 为空的路由，然后再过滤掉 children 为空的路由
    const filteredBusinessRoutes = filterEmptyChildrenRoutes(filterEmptyApisRoutes(processedBusinessRoutes));

    const routesWithoutApis = {
      businessRoutes: filteredBusinessRoutes
    };

    // 将 JSON 对象转换为字符串
    const filteredJsonOutput = JSON.stringify(routesWithoutApis, null, 2);

    // 构建输出路径
    const withoutApisPath = path.join(__dirname, 'index_without_apis.json');

    // 将输出写入文件
    fs.writeFileSync(withoutApisPath, filteredJsonOutput);
    console.log(`已成功生成文件: ${withoutApisPath}`);
  } catch (error) {
    console.error('生成输出文件失败:', error);
    process.exit(1);
  }

  console.log('分析完成！');
}

// 执行主函数
main();
