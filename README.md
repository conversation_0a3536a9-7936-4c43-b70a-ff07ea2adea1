## hdzt_manager_front

活动中台管理后台 前端源码

## 项目说明 
项目的原始代码来着  https://gitee.com/y_project/RuoYi-Vue 是 ruoyi 的一个分支

相关文档可以参考  http://doc.ruoyi.vip/ruoyi/document/qdsc.html

初始模板基于： https://github.com/PanJiaChen/vue-element-admin

使用公司的 fec框架替换了 vue的手脚架 vue cli -浩爷帮忙整合的，有问题可以找浩爷

环境配置 在 build/

development.config.js 开发环境配置

production.config.js 生产环境配置

staging.config.js 测试环境配置

## 启动

注意，请使用 node 14.21.3 版本

```bash
# 安装fec,已经安装可以忽略
npm i -g @yy/fec --registry=https://npm-registry.yy.com --force

# 安装依赖
npm install --registry=https://registry.npm.taobao.org

# 建议使用 yarn 安装依赖
yarn                    

# 出现 self signed certificate in certificate chain 问题处理，看情况重新执行前面的两步操作
npm set strict-ssl false（取消npm的https认证）
yarn config set strict-ssl false (取消yarn的https认证）

# 如果你想启动后，接口请求到本地，即路由到 htpt_manager_back 本地启动的服务，那么需要修改 fec.config.js 中的 proxyLocal = false, 表示代理到本地

# 本地设置 host local-manager-hdzt.yy.com(运营后台用) local-manager-hdzt-tec.yy.com(技术人员后台用)
127.0.0.1 local-manager-hdzt.yy.com
127.0.0.1 local-manager-hdzt-tec.yy.com

# 启动服务
yarn dev 或者 fec d

```

**【运营】使用的后台**:   
  本地：https://local-manager-hdzt.yy.com:4000  
  测试：https://test-manager-hdzt.yy.com/admin-static    
  正式：https://manager-hdzt.yy.com/admin-static  

**【技术】使用的后台**:   
  本地：https://local-manager-hdzt-tec.yy.com:4000  
  测试：https://test-manager-hdzt-tec.yy.com/admin-static  
  正式：https://manager-hdzt-tec.yy.com/admin-static  

注意：如果不能访问，可以修改fec.config文件的 host，修改为host: '0.0.0.0'，重跑即可

## 发版
发版配置在 .gitlab-ci.yml 这个项目是通过分支区分环境的

正式环境只能发 master 分支，测试环境不要发master分支

## 开发新功能指引
### src/router/index.js 下添加路由到 businessRouters
示例如下，将新的菜单追加到 businessRouters 后面：
```javascript
let menu = 
{
  name: "Example",
  path: "/example",
  hidden: false, // 这个开发阶段，可以先设置为 false，这样子就还没纳入权限管理，纳入权限管理后，这里要设置为true
  redirect: "noRedirect",
  component: Layout,
  alwaysShow: true,
  meta: { title: "一级菜单名", icon: "tool", noCache: false, link: null },
  children: [
    {
      name: "Level2",
      path: "level2",
      hidden: false,
      componentPath: '/example/level2/index', // 这里对应 src/views 下的目录， 最后一个一般是 index.vue
      meta: { title: "二级菜单名", icon: "build", noCache: false, link: null }
    }
  ]
}
```
**添加完成后，启动项目就能看到新的菜单了，但是，这里并没有加入到权限管理**

### 加入权限管理
目前使用的权限管理体系是 zhuiya 管理系统提供的，因此需要去 zhuiya 系统添加菜单和权限

#### 1. 添加一级菜单

<font color="red">目前zhuiya系统尚未支持界面化操作，只能通过数据库直接插入，类似如下语句，插入到zhuiya库：</font>
```sql
INSERT INTO zhuiya.zy_auth_menu (pid,access_key,name,url,icon,css_class,order_no,create_time,create_by,update_time,update_by,ext,old_id) VALUES
	 (-1,'hdzt-adminV2','你的一级菜单名称','example','',NULL,750,now(),50048391,now(),50048391,NULL,NULL);
	 
-- 说明一下：
-- pid: 父级菜单id，这里一级菜单，都填写 -1
-- access_key: 要加入到哪个权限体系中，hdzt-adminV2：运营人员专用的后台； hdzt-tec-admin: 技术人员专用的后台，视情况而定
-- name: 菜单名称，对应上一个步骤中的 meta.title 的值
-- url: 路径，这里对应上一个步骤中的 path 属性
-- icon: 图标，目前没用到，前端写死就可以，前端 meta.icon 属性
-- order_no: 菜单的排序，越大排在越前面（相同 pid 下的排序），通常你需要看下数据库中的已有顺序，决定放去哪里
```

添加完成后，我们可以在 zhuiya 测试环境后台看到对应的菜单：https://zhuiya-test.yy.com/admin-static/#/authAdmin

#### 2. 添加二级以上菜单

添加二级菜单可以直接在界面上操作：https://zhuiya-test.yy.com/admin-static/#/authAdmin

![添加子菜单](imgs/create_child_menu.png)

<font color="red">点击左侧菜单中的文本，会出现【添加菜单】按钮，点击即可展示上述添加页面</font>

- *菜单名称*： 这个对应 meta.title
- *菜单地址*： 这个对应 <code>componentPath</code> 注意，要有 / 前缀

#### 3. 添加权限

这个是必不可少的，要想再申请权限页面展示可申请的菜单，<font color="red">必须给这个子菜单添加权限, 哪怕是构造一个虚拟的权限</font>

![添加权限](imgs/create_permission.png)

- *权限名称*： 这个随意，自己定义就好
- *permission*: 这个就不能随意定了，要根据后台请求数据接口来定义，通常对应 <code>@PreAuthorize("@ss.hasPermi('system:dept:list')")</code> 这里面的 <code>system:dept:list</code> 

这里重点说下 *permission*, 我们通常后台写接口，类似下面这样子：
```java
/**
 * 获取部门列表
 */
@ApiOperation("获取部门列表")
@PreAuthorize("@ss.hasPermi('system:dept:list')") // 用这个注解来验证权限
@GetMapping("/list")
public AjaxResult<List<SysDept>> list(SysDept dept) {
    // etc...
}
```

#### 4. hidden 设置默认为 true
当我们将菜单加入到权限管理系统的时候，就可以把 *src/router/index.js* 中添加的新菜单默认隐藏了

#### 5. 上线-同步菜单到线上

只需要在测试环境上操作同步就可以了：https://zhuiya-test.yy.com/admin-static/#/authAdmin

![同步权限](imgs/sync_auth.png)


